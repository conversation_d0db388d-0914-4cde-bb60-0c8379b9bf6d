/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/compress-commons";
exports.ids = ["vendor-chunks/compress-commons"];
exports.modules = {

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/archive-entry.js":
/*!**********************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/archive-entry.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nvar ArchiveEntry = module.exports = function() {};\n\nArchiveEntry.prototype.getName = function() {};\n\nArchiveEntry.prototype.getSize = function() {};\n\nArchiveEntry.prototype.getLastModifiedDate = function() {};\n\nArchiveEntry.prototype.isDirectory = function() {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY29tcHJlc3MtY29tbW9ucy9saWIvYXJjaGl2ZXJzL2FyY2hpdmUtZW50cnkuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXGNvbXByZXNzLWNvbW1vbnNcXGxpYlxcYXJjaGl2ZXJzXFxhcmNoaXZlLWVudHJ5LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbm9kZS1jb21wcmVzcy1jb21tb25zXG4gKlxuICogQ29weXJpZ2h0IChjKSAyMDE0IENocmlzIFRhbGtpbmd0b24sIGNvbnRyaWJ1dG9ycy5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hcmNoaXZlcmpzL25vZGUtY29tcHJlc3MtY29tbW9ucy9ibG9iL21hc3Rlci9MSUNFTlNFLU1JVFxuICovXG52YXIgQXJjaGl2ZUVudHJ5ID0gbW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbigpIHt9O1xuXG5BcmNoaXZlRW50cnkucHJvdG90eXBlLmdldE5hbWUgPSBmdW5jdGlvbigpIHt9O1xuXG5BcmNoaXZlRW50cnkucHJvdG90eXBlLmdldFNpemUgPSBmdW5jdGlvbigpIHt9O1xuXG5BcmNoaXZlRW50cnkucHJvdG90eXBlLmdldExhc3RNb2RpZmllZERhdGUgPSBmdW5jdGlvbigpIHt9O1xuXG5BcmNoaXZlRW50cnkucHJvdG90eXBlLmlzRGlyZWN0b3J5ID0gZnVuY3Rpb24oKSB7fTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/archive-entry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/archive-output-stream.js":
/*!******************************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/archive-output-stream.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar Transform = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Transform);\n\nvar ArchiveEntry = __webpack_require__(/*! ./archive-entry */ \"(ssr)/./node_modules/compress-commons/lib/archivers/archive-entry.js\");\nvar util = __webpack_require__(/*! ../util */ \"(ssr)/./node_modules/compress-commons/lib/util/index.js\");\n\nvar ArchiveOutputStream = module.exports = function(options) {\n  if (!(this instanceof ArchiveOutputStream)) {\n    return new ArchiveOutputStream(options);\n  }\n\n  Transform.call(this, options);\n\n  this.offset = 0;\n  this._archive = {\n    finish: false,\n    finished: false,\n    processing: false\n  };\n};\n\ninherits(ArchiveOutputStream, Transform);\n\nArchiveOutputStream.prototype._appendBuffer = function(zae, source, callback) {\n  // scaffold only\n};\n\nArchiveOutputStream.prototype._appendStream = function(zae, source, callback) {\n  // scaffold only\n};\n\nArchiveOutputStream.prototype._emitErrorCallback = function(err) {\n  if (err) {\n    this.emit('error', err);\n  }\n};\n\nArchiveOutputStream.prototype._finish = function(ae) {\n  // scaffold only\n};\n\nArchiveOutputStream.prototype._normalizeEntry = function(ae) {\n  // scaffold only\n};\n\nArchiveOutputStream.prototype._transform = function(chunk, encoding, callback) {\n  callback(null, chunk);\n};\n\nArchiveOutputStream.prototype.entry = function(ae, source, callback) {\n  source = source || null;\n\n  if (typeof callback !== 'function') {\n    callback = this._emitErrorCallback.bind(this);\n  }\n\n  if (!(ae instanceof ArchiveEntry)) {\n    callback(new Error('not a valid instance of ArchiveEntry'));\n    return;\n  }\n\n  if (this._archive.finish || this._archive.finished) {\n    callback(new Error('unacceptable entry after finish'));\n    return;\n  }\n\n  if (this._archive.processing) {\n    callback(new Error('already processing an entry'));\n    return;\n  }\n\n  this._archive.processing = true;\n  this._normalizeEntry(ae);\n  this._entry = ae;\n\n  source = util.normalizeInputSource(source);\n\n  if (Buffer.isBuffer(source)) {\n    this._appendBuffer(ae, source, callback);\n  } else if (util.isStream(source)) {\n    this._appendStream(ae, source, callback);\n  } else {\n    this._archive.processing = false;\n    callback(new Error('input source must be valid Stream or Buffer instance'));\n    return;\n  }\n\n  return this;\n};\n\nArchiveOutputStream.prototype.finish = function() {\n  if (this._archive.processing) {\n    this._archive.finish = true;\n    return;\n  }\n\n  this._finish();\n};\n\nArchiveOutputStream.prototype.getBytesWritten = function() {\n  return this.offset;\n};\n\nArchiveOutputStream.prototype.write = function(chunk, cb) {\n  if (chunk) {\n    this.offset += chunk.length;\n  }\n\n  return Transform.prototype.write.call(this, chunk, cb);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/archive-output-stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/zip/constants.js":
/*!**********************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/zip/constants.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nmodule.exports = {\n  WORD: 4,\n  DWORD: 8,\n  EMPTY: Buffer.alloc(0),\n\n  SHORT: 2,\n  SHORT_MASK: 0xffff,\n  SHORT_SHIFT: 16,\n  SHORT_ZERO: Buffer.from(Array(2)),\n  LONG: 4,\n  LONG_ZERO: Buffer.from(Array(4)),\n\n  MIN_VERSION_INITIAL: 10,\n  MIN_VERSION_DATA_DESCRIPTOR: 20,\n  MIN_VERSION_ZIP64: 45,\n  VERSION_MADEBY: 45,\n\n  METHOD_STORED: 0,\n  METHOD_DEFLATED: 8,\n\n  PLATFORM_UNIX: 3,\n  PLATFORM_FAT: 0,\n\n  SIG_LFH: 0x04034b50,\n  SIG_DD: 0x08074b50,\n  SIG_CFH: 0x02014b50,\n  SIG_EOCD: 0x06054b50,\n  SIG_ZIP64_EOCD: 0x06064B50,\n  SIG_ZIP64_EOCD_LOC: 0x07064B50,\n\n  ZIP64_MAGIC_SHORT: 0xffff,\n  ZIP64_MAGIC: 0xffffffff,\n  ZIP64_EXTRA_ID: 0x0001,\n\n  ZLIB_NO_COMPRESSION: 0,\n  ZLIB_BEST_SPEED: 1,\n  ZLIB_BEST_COMPRESSION: 9,\n  ZLIB_DEFAULT_COMPRESSION: -1,\n\n  MODE_MASK: 0xFFF,\n  DEFAULT_FILE_MODE: 33188, // 010644 = -rw-r--r-- = S_IFREG | S_IRUSR | S_IWUSR | S_IRGRP | S_IROTH\n  DEFAULT_DIR_MODE: 16877,  // 040755 = drwxr-xr-x = S_IFDIR | S_IRWXU | S_IRGRP | S_IXGRP | S_IROTH | S_IXOTH\n\n  EXT_FILE_ATTR_DIR: 1106051088,  // 010173200020 = drwxr-xr-x = (((S_IFDIR | 0755) << 16) | S_DOS_D)\n  EXT_FILE_ATTR_FILE: 2175008800, // 020151000040 = -rw-r--r-- = (((S_IFREG | 0644) << 16) | S_DOS_A) >>> 0\n\n  // Unix file types\n  S_IFMT: 61440,   // 0170000 type of file mask\n  S_IFIFO: 4096,   // 010000 named pipe (fifo)\n  S_IFCHR: 8192,   // 020000 character special\n  S_IFDIR: 16384,  // 040000 directory\n  S_IFBLK: 24576,  // 060000 block special\n  S_IFREG: 32768,  // 0100000 regular\n  S_IFLNK: 40960,  // 0120000 symbolic link\n  S_IFSOCK: 49152, // 0140000 socket\n\n  // DOS file type flags\n  S_DOS_A: 32, // 040 Archive\n  S_DOS_D: 16, // 020 Directory\n  S_DOS_V: 8,  // 010 Volume\n  S_DOS_S: 4,  // 04 System\n  S_DOS_H: 2,  // 02 Hidden\n  S_DOS_R: 1   // 01 Read Only\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/zip/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/zip/general-purpose-bit.js":
/*!********************************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/zip/general-purpose-bit.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nvar zipUtil = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/util.js\");\n\nvar DATA_DESCRIPTOR_FLAG = 1 << 3;\nvar ENCRYPTION_FLAG = 1 << 0;\nvar NUMBER_OF_SHANNON_FANO_TREES_FLAG = 1 << 2;\nvar SLIDING_DICTIONARY_SIZE_FLAG = 1 << 1;\nvar STRONG_ENCRYPTION_FLAG = 1 << 6;\nvar UFT8_NAMES_FLAG = 1 << 11;\n\nvar GeneralPurposeBit = module.exports = function() {\n  if (!(this instanceof GeneralPurposeBit)) {\n    return new GeneralPurposeBit();\n  }\n\n  this.descriptor = false;\n  this.encryption = false;\n  this.utf8 = false;\n  this.numberOfShannonFanoTrees = 0;\n  this.strongEncryption = false;\n  this.slidingDictionarySize = 0;\n\n  return this;\n};\n\nGeneralPurposeBit.prototype.encode = function() {\n  return zipUtil.getShortBytes(\n    (this.descriptor ? DATA_DESCRIPTOR_FLAG : 0) |\n    (this.utf8 ? UFT8_NAMES_FLAG : 0) |\n    (this.encryption ? ENCRYPTION_FLAG : 0) |\n    (this.strongEncryption ? STRONG_ENCRYPTION_FLAG : 0)\n  );\n};\n\nGeneralPurposeBit.prototype.parse = function(buf, offset) {\n  var flag = zipUtil.getShortBytesValue(buf, offset);\n  var gbp = new GeneralPurposeBit();\n\n  gbp.useDataDescriptor((flag & DATA_DESCRIPTOR_FLAG) !== 0);\n  gbp.useUTF8ForNames((flag & UFT8_NAMES_FLAG) !== 0);\n  gbp.useStrongEncryption((flag & STRONG_ENCRYPTION_FLAG) !== 0);\n  gbp.useEncryption((flag & ENCRYPTION_FLAG) !== 0);\n  gbp.setSlidingDictionarySize((flag & SLIDING_DICTIONARY_SIZE_FLAG) !== 0 ? 8192 : 4096);\n  gbp.setNumberOfShannonFanoTrees((flag & NUMBER_OF_SHANNON_FANO_TREES_FLAG) !== 0 ? 3 : 2);\n\n  return gbp;\n};\n\nGeneralPurposeBit.prototype.setNumberOfShannonFanoTrees = function(n) {\n  this.numberOfShannonFanoTrees = n;\n};\n\nGeneralPurposeBit.prototype.getNumberOfShannonFanoTrees = function() {\n  return this.numberOfShannonFanoTrees;\n};\n\nGeneralPurposeBit.prototype.setSlidingDictionarySize = function(n) {\n  this.slidingDictionarySize = n;\n};\n\nGeneralPurposeBit.prototype.getSlidingDictionarySize = function() {\n  return this.slidingDictionarySize;\n};\n\nGeneralPurposeBit.prototype.useDataDescriptor = function(b) {\n  this.descriptor = b;\n};\n\nGeneralPurposeBit.prototype.usesDataDescriptor = function() {\n  return this.descriptor;\n};\n\nGeneralPurposeBit.prototype.useEncryption = function(b) {\n  this.encryption = b;\n};\n\nGeneralPurposeBit.prototype.usesEncryption = function() {\n  return this.encryption;\n};\n\nGeneralPurposeBit.prototype.useStrongEncryption = function(b) {\n  this.strongEncryption = b;\n};\n\nGeneralPurposeBit.prototype.usesStrongEncryption = function() {\n  return this.strongEncryption;\n};\n\nGeneralPurposeBit.prototype.useUTF8ForNames = function(b) {\n  this.utf8 = b;\n};\n\nGeneralPurposeBit.prototype.usesUTF8ForNames = function() {\n  return this.utf8;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/zip/general-purpose-bit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/zip/unix-stat.js":
/*!**********************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/zip/unix-stat.js ***!
  \**********************************************************************/
/***/ ((module) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nmodule.exports = {\n    /**\n     * Bits used for permissions (and sticky bit)\n     */\n    PERM_MASK: 4095, // 07777\n\n    /**\n     * Bits used to indicate the filesystem object type.\n     */\n    FILE_TYPE_FLAG: 61440, // 0170000\n\n    /**\n     * Indicates symbolic links.\n     */\n    LINK_FLAG: 40960, // 0120000\n\n    /**\n     * Indicates plain files.\n     */\n    FILE_FLAG: 32768, // 0100000\n\n    /**\n     * Indicates directories.\n     */\n    DIR_FLAG: 16384, // 040000\n\n    // ----------------------------------------------------------\n    // somewhat arbitrary choices that are quite common for shared\n    // installations\n    // -----------------------------------------------------------\n\n    /**\n     * Default permissions for symbolic links.\n     */\n    DEFAULT_LINK_PERM: 511, // 0777\n\n    /**\n     * Default permissions for directories.\n     */\n    DEFAULT_DIR_PERM: 493, // 0755\n\n    /**\n     * Default permissions for plain files.\n     */\n    DEFAULT_FILE_PERM: 420 // 0644\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/zip/unix-stat.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/zip/util.js":
/*!*****************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/zip/util.js ***!
  \*****************************************************************/
/***/ ((module) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nvar util = module.exports = {};\n\nutil.dateToDos = function(d, forceLocalTime) {\n  forceLocalTime = forceLocalTime || false;\n\n  var year = forceLocalTime ? d.getFullYear() : d.getUTCFullYear();\n\n  if (year < 1980) {\n    return 2162688; // 1980-1-1 00:00:00\n  } else if (year >= 2044) {\n    return **********; // 2043-12-31 23:59:58\n  }\n\n  var val = {\n    year: year,\n    month: forceLocalTime ? d.getMonth() : d.getUTCMonth(),\n    date: forceLocalTime ? d.getDate() : d.getUTCDate(),\n    hours: forceLocalTime ? d.getHours() : d.getUTCHours(),\n    minutes: forceLocalTime ? d.getMinutes() : d.getUTCMinutes(),\n    seconds: forceLocalTime ? d.getSeconds() : d.getUTCSeconds()\n  };\n\n  return ((val.year - 1980) << 25) | ((val.month + 1) << 21) | (val.date << 16) |\n    (val.hours << 11) | (val.minutes << 5) | (val.seconds / 2);\n};\n\nutil.dosToDate = function(dos) {\n  return new Date(((dos >> 25) & 0x7f) + 1980, ((dos >> 21) & 0x0f) - 1, (dos >> 16) & 0x1f, (dos >> 11) & 0x1f, (dos >> 5) & 0x3f, (dos & 0x1f) << 1);\n};\n\nutil.fromDosTime = function(buf) {\n  return util.dosToDate(buf.readUInt32LE(0));\n};\n\nutil.getEightBytes = function(v) {\n  var buf = Buffer.alloc(8);\n  buf.writeUInt32LE(v % 0x0100000000, 0);\n  buf.writeUInt32LE((v / 0x0100000000) | 0, 4);\n\n  return buf;\n};\n\nutil.getShortBytes = function(v) {\n  var buf = Buffer.alloc(2);\n  buf.writeUInt16LE((v & 0xFFFF) >>> 0, 0);\n\n  return buf;\n};\n\nutil.getShortBytesValue = function(buf, offset) {\n  return buf.readUInt16LE(offset);\n};\n\nutil.getLongBytes = function(v) {\n  var buf = Buffer.alloc(4);\n  buf.writeUInt32LE((v & 0xFFFFFFFF) >>> 0, 0);\n\n  return buf;\n};\n\nutil.getLongBytesValue = function(buf, offset) {\n  return buf.readUInt32LE(offset);\n};\n\nutil.toDosTime = function(d) {\n  return util.getLongBytes(util.dateToDos(d));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/zip/util.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/zip/zip-archive-entry.js":
/*!******************************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/zip/zip-archive-entry.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar normalizePath = __webpack_require__(/*! normalize-path */ \"(ssr)/./node_modules/normalize-path/index.js\");\n\nvar ArchiveEntry = __webpack_require__(/*! ../archive-entry */ \"(ssr)/./node_modules/compress-commons/lib/archivers/archive-entry.js\");\nvar GeneralPurposeBit = __webpack_require__(/*! ./general-purpose-bit */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/general-purpose-bit.js\");\nvar UnixStat = __webpack_require__(/*! ./unix-stat */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/unix-stat.js\");\n\nvar constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/constants.js\");\nvar zipUtil = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/util.js\");\n\nvar ZipArchiveEntry = module.exports = function(name) {\n  if (!(this instanceof ZipArchiveEntry)) {\n    return new ZipArchiveEntry(name);\n  }\n\n  ArchiveEntry.call(this);\n\n  this.platform = constants.PLATFORM_FAT;\n  this.method = -1;\n\n  this.name = null;\n  this.size = 0;\n  this.csize = 0;\n  this.gpb = new GeneralPurposeBit();\n  this.crc = 0;\n  this.time = -1;\n\n  this.minver = constants.MIN_VERSION_INITIAL;\n  this.mode = -1;\n  this.extra = null;\n  this.exattr = 0;\n  this.inattr = 0;\n  this.comment = null;\n\n  if (name) {\n    this.setName(name);\n  }\n};\n\ninherits(ZipArchiveEntry, ArchiveEntry);\n\n/**\n * Returns the extra fields related to the entry.\n *\n * @returns {Buffer}\n */\nZipArchiveEntry.prototype.getCentralDirectoryExtra = function() {\n  return this.getExtra();\n};\n\n/**\n * Returns the comment set for the entry.\n *\n * @returns {string}\n */\nZipArchiveEntry.prototype.getComment = function() {\n  return this.comment !== null ? this.comment : '';\n};\n\n/**\n * Returns the compressed size of the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getCompressedSize = function() {\n  return this.csize;\n};\n\n/**\n * Returns the CRC32 digest for the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getCrc = function() {\n  return this.crc;\n};\n\n/**\n * Returns the external file attributes for the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getExternalAttributes = function() {\n  return this.exattr;\n};\n\n/**\n * Returns the extra fields related to the entry.\n *\n * @returns {Buffer}\n */\nZipArchiveEntry.prototype.getExtra = function() {\n  return this.extra !== null ? this.extra : constants.EMPTY;\n};\n\n/**\n * Returns the general purpose bits related to the entry.\n *\n * @returns {GeneralPurposeBit}\n */\nZipArchiveEntry.prototype.getGeneralPurposeBit = function() {\n  return this.gpb;\n};\n\n/**\n * Returns the internal file attributes for the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getInternalAttributes = function() {\n  return this.inattr;\n};\n\n/**\n * Returns the last modified date of the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getLastModifiedDate = function() {\n  return this.getTime();\n};\n\n/**\n * Returns the extra fields related to the entry.\n *\n * @returns {Buffer}\n */\nZipArchiveEntry.prototype.getLocalFileDataExtra = function() {\n  return this.getExtra();\n};\n\n/**\n * Returns the compression method used on the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getMethod = function() {\n  return this.method;\n};\n\n/**\n * Returns the filename of the entry.\n *\n * @returns {string}\n */\nZipArchiveEntry.prototype.getName = function() {\n  return this.name;\n};\n\n/**\n * Returns the platform on which the entry was made.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getPlatform = function() {\n  return this.platform;\n};\n\n/**\n * Returns the size of the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getSize = function() {\n  return this.size;\n};\n\n/**\n * Returns a date object representing the last modified date of the entry.\n *\n * @returns {number|Date}\n */\nZipArchiveEntry.prototype.getTime = function() {\n  return this.time !== -1 ? zipUtil.dosToDate(this.time) : -1;\n};\n\n/**\n * Returns the DOS timestamp for the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getTimeDos = function() {\n  return this.time !== -1 ? this.time : 0;\n};\n\n/**\n * Returns the UNIX file permissions for the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getUnixMode = function() {\n  return this.platform !== constants.PLATFORM_UNIX ? 0 : ((this.getExternalAttributes() >> constants.SHORT_SHIFT) & constants.SHORT_MASK);\n};\n\n/**\n * Returns the version of ZIP needed to extract the entry.\n *\n * @returns {number}\n */\nZipArchiveEntry.prototype.getVersionNeededToExtract = function() {\n  return this.minver;\n};\n\n/**\n * Sets the comment of the entry.\n *\n * @param comment\n */\nZipArchiveEntry.prototype.setComment = function(comment) {\n  if (Buffer.byteLength(comment) !== comment.length) {\n    this.getGeneralPurposeBit().useUTF8ForNames(true);\n  }\n\n  this.comment = comment;\n};\n\n/**\n * Sets the compressed size of the entry.\n *\n * @param size\n */\nZipArchiveEntry.prototype.setCompressedSize = function(size) {\n  if (size < 0) {\n    throw new Error('invalid entry compressed size');\n  }\n\n  this.csize = size;\n};\n\n/**\n * Sets the checksum of the entry.\n *\n * @param crc\n */\nZipArchiveEntry.prototype.setCrc = function(crc) {\n  if (crc < 0) {\n    throw new Error('invalid entry crc32');\n  }\n\n  this.crc = crc;\n};\n\n/**\n * Sets the external file attributes of the entry.\n *\n * @param attr\n */\nZipArchiveEntry.prototype.setExternalAttributes = function(attr) {\n  this.exattr = attr >>> 0;\n};\n\n/**\n * Sets the extra fields related to the entry.\n *\n * @param extra\n */\nZipArchiveEntry.prototype.setExtra = function(extra) {\n  this.extra = extra;\n};\n\n/**\n * Sets the general purpose bits related to the entry.\n *\n * @param gpb\n */\nZipArchiveEntry.prototype.setGeneralPurposeBit = function(gpb) {\n  if (!(gpb instanceof GeneralPurposeBit)) {\n    throw new Error('invalid entry GeneralPurposeBit');\n  }\n\n  this.gpb = gpb;\n};\n\n/**\n * Sets the internal file attributes of the entry.\n *\n * @param attr\n */\nZipArchiveEntry.prototype.setInternalAttributes = function(attr) {\n  this.inattr = attr;\n};\n\n/**\n * Sets the compression method of the entry.\n *\n * @param method\n */\nZipArchiveEntry.prototype.setMethod = function(method) {\n  if (method < 0) {\n    throw new Error('invalid entry compression method');\n  }\n\n  this.method = method;\n};\n\n/**\n * Sets the name of the entry.\n *\n * @param name\n * @param prependSlash\n */\nZipArchiveEntry.prototype.setName = function(name, prependSlash = false) {\n  name = normalizePath(name, false)\n    .replace(/^\\w+:/, '')\n    .replace(/^(\\.\\.\\/|\\/)+/, '');\n\n  if (prependSlash) {\n    name = `/${name}`;\n  }\n\n  if (Buffer.byteLength(name) !== name.length) {\n    this.getGeneralPurposeBit().useUTF8ForNames(true);\n  }\n\n  this.name = name;\n};\n\n/**\n * Sets the platform on which the entry was made.\n *\n * @param platform\n */\nZipArchiveEntry.prototype.setPlatform = function(platform) {\n  this.platform = platform;\n};\n\n/**\n * Sets the size of the entry.\n *\n * @param size\n */\nZipArchiveEntry.prototype.setSize = function(size) {\n  if (size < 0) {\n    throw new Error('invalid entry size');\n  }\n\n  this.size = size;\n};\n\n/**\n * Sets the time of the entry.\n *\n * @param time\n * @param forceLocalTime\n */\nZipArchiveEntry.prototype.setTime = function(time, forceLocalTime) {\n  if (!(time instanceof Date)) {\n    throw new Error('invalid entry time');\n  }\n\n  this.time = zipUtil.dateToDos(time, forceLocalTime);\n};\n\n/**\n * Sets the UNIX file permissions for the entry.\n *\n * @param mode\n */\nZipArchiveEntry.prototype.setUnixMode = function(mode) {\n  mode |= this.isDirectory() ? constants.S_IFDIR : constants.S_IFREG;\n\n  var extattr = 0;\n  extattr |= (mode << constants.SHORT_SHIFT) | (this.isDirectory() ? constants.S_DOS_D : constants.S_DOS_A);\n\n  this.setExternalAttributes(extattr);\n  this.mode = mode & constants.MODE_MASK;\n  this.platform = constants.PLATFORM_UNIX;\n};\n\n/**\n * Sets the version of ZIP needed to extract this entry.\n *\n * @param minver\n */\nZipArchiveEntry.prototype.setVersionNeededToExtract = function(minver) {\n  this.minver = minver;\n};\n\n/**\n * Returns true if this entry represents a directory.\n *\n * @returns {boolean}\n */\nZipArchiveEntry.prototype.isDirectory = function() {\n  return this.getName().slice(-1) === '/';\n};\n\n/**\n * Returns true if this entry represents a unix symlink,\n * in which case the entry's content contains the target path\n * for the symlink.\n *\n * @returns {boolean}\n */\nZipArchiveEntry.prototype.isUnixSymlink = function() {\n  return (this.getUnixMode() & UnixStat.FILE_TYPE_FLAG) === UnixStat.LINK_FLAG;\n};\n\n/**\n * Returns true if this entry is using the ZIP64 extension of ZIP.\n *\n * @returns {boolean}\n */\nZipArchiveEntry.prototype.isZip64 = function() {\n  return this.csize > constants.ZIP64_MAGIC || this.size > constants.ZIP64_MAGIC;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/zip/zip-archive-entry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/archivers/zip/zip-archive-output-stream.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/compress-commons/lib/archivers/zip/zip-archive-output-stream.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar crc32 = __webpack_require__(/*! buffer-crc32 */ \"(ssr)/./node_modules/buffer-crc32/index.js\");\nvar {CRC32Stream} = __webpack_require__(/*! crc32-stream */ \"(ssr)/./node_modules/crc32-stream/lib/index.js\");\nvar {DeflateCRC32Stream} = __webpack_require__(/*! crc32-stream */ \"(ssr)/./node_modules/crc32-stream/lib/index.js\");\n\nvar ArchiveOutputStream = __webpack_require__(/*! ../archive-output-stream */ \"(ssr)/./node_modules/compress-commons/lib/archivers/archive-output-stream.js\");\nvar ZipArchiveEntry = __webpack_require__(/*! ./zip-archive-entry */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/zip-archive-entry.js\");\nvar GeneralPurposeBit = __webpack_require__(/*! ./general-purpose-bit */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/general-purpose-bit.js\");\n\nvar constants = __webpack_require__(/*! ./constants */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/constants.js\");\nvar util = __webpack_require__(/*! ../../util */ \"(ssr)/./node_modules/compress-commons/lib/util/index.js\");\nvar zipUtil = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/util.js\");\n\nvar ZipArchiveOutputStream = module.exports = function(options) {\n  if (!(this instanceof ZipArchiveOutputStream)) {\n    return new ZipArchiveOutputStream(options);\n  }\n\n  options = this.options = this._defaults(options);\n\n  ArchiveOutputStream.call(this, options);\n\n  this._entry = null;\n  this._entries = [];\n  this._archive = {\n    centralLength: 0,\n    centralOffset: 0,\n    comment: '',\n    finish: false,\n    finished: false,\n    processing: false,\n    forceZip64: options.forceZip64,\n    forceLocalTime: options.forceLocalTime\n  };\n};\n\ninherits(ZipArchiveOutputStream, ArchiveOutputStream);\n\nZipArchiveOutputStream.prototype._afterAppend = function(ae) {\n  this._entries.push(ae);\n\n  if (ae.getGeneralPurposeBit().usesDataDescriptor()) {\n    this._writeDataDescriptor(ae);\n  }\n\n  this._archive.processing = false;\n  this._entry = null;\n\n  if (this._archive.finish && !this._archive.finished) {\n    this._finish();\n  }\n};\n\nZipArchiveOutputStream.prototype._appendBuffer = function(ae, source, callback) {\n  if (source.length === 0) {\n    ae.setMethod(constants.METHOD_STORED);\n  }\n\n  var method = ae.getMethod();\n\n  if (method === constants.METHOD_STORED) {\n    ae.setSize(source.length);\n    ae.setCompressedSize(source.length);\n    ae.setCrc(crc32.unsigned(source));\n  }\n\n  this._writeLocalFileHeader(ae);\n\n  if (method === constants.METHOD_STORED) {\n    this.write(source);\n    this._afterAppend(ae);\n    callback(null, ae);\n    return;\n  } else if (method === constants.METHOD_DEFLATED) {\n    this._smartStream(ae, callback).end(source);\n    return;\n  } else {\n    callback(new Error('compression method ' + method + ' not implemented'));\n    return;\n  }\n};\n\nZipArchiveOutputStream.prototype._appendStream = function(ae, source, callback) {\n  ae.getGeneralPurposeBit().useDataDescriptor(true);\n  ae.setVersionNeededToExtract(constants.MIN_VERSION_DATA_DESCRIPTOR);\n\n  this._writeLocalFileHeader(ae);\n\n  var smart = this._smartStream(ae, callback);\n  source.once('error', function(err) {\n    smart.emit('error', err);\n    smart.end();\n  })\n  source.pipe(smart);\n};\n\nZipArchiveOutputStream.prototype._defaults = function(o) {\n  if (typeof o !== 'object') {\n    o = {};\n  }\n\n  if (typeof o.zlib !== 'object') {\n    o.zlib = {};\n  }\n\n  if (typeof o.zlib.level !== 'number') {\n    o.zlib.level = constants.ZLIB_BEST_SPEED;\n  }\n\n  o.forceZip64 = !!o.forceZip64;\n  o.forceLocalTime = !!o.forceLocalTime;\n\n  return o;\n};\n\nZipArchiveOutputStream.prototype._finish = function() {\n  this._archive.centralOffset = this.offset;\n\n  this._entries.forEach(function(ae) {\n    this._writeCentralFileHeader(ae);\n  }.bind(this));\n\n  this._archive.centralLength = this.offset - this._archive.centralOffset;\n\n  if (this.isZip64()) {\n    this._writeCentralDirectoryZip64();\n  }\n\n  this._writeCentralDirectoryEnd();\n\n  this._archive.processing = false;\n  this._archive.finish = true;\n  this._archive.finished = true;\n  this.end();\n};\n\nZipArchiveOutputStream.prototype._normalizeEntry = function(ae) {\n  if (ae.getMethod() === -1) {\n    ae.setMethod(constants.METHOD_DEFLATED);\n  }\n\n  if (ae.getMethod() === constants.METHOD_DEFLATED) {\n    ae.getGeneralPurposeBit().useDataDescriptor(true);\n    ae.setVersionNeededToExtract(constants.MIN_VERSION_DATA_DESCRIPTOR);\n  }\n\n  if (ae.getTime() === -1) {\n    ae.setTime(new Date(), this._archive.forceLocalTime);\n  }\n\n  ae._offsets = {\n    file: 0,\n    data: 0,\n    contents: 0,\n  };\n};\n\nZipArchiveOutputStream.prototype._smartStream = function(ae, callback) {\n  var deflate = ae.getMethod() === constants.METHOD_DEFLATED;\n  var process = deflate ? new DeflateCRC32Stream(this.options.zlib) : new CRC32Stream();\n  var error = null;\n\n  function handleStuff() {\n    var digest = process.digest().readUInt32BE(0);\n    ae.setCrc(digest);\n    ae.setSize(process.size());\n    ae.setCompressedSize(process.size(true));\n    this._afterAppend(ae);\n    callback(error, ae);\n  }\n\n  process.once('end', handleStuff.bind(this));\n  process.once('error', function(err) {\n    error = err;\n  });\n\n  process.pipe(this, { end: false });\n\n  return process;\n};\n\nZipArchiveOutputStream.prototype._writeCentralDirectoryEnd = function() {\n  var records = this._entries.length;\n  var size = this._archive.centralLength;\n  var offset = this._archive.centralOffset;\n\n  if (this.isZip64()) {\n    records = constants.ZIP64_MAGIC_SHORT;\n    size = constants.ZIP64_MAGIC;\n    offset = constants.ZIP64_MAGIC;\n  }\n\n  // signature\n  this.write(zipUtil.getLongBytes(constants.SIG_EOCD));\n\n  // disk numbers\n  this.write(constants.SHORT_ZERO);\n  this.write(constants.SHORT_ZERO);\n\n  // number of entries\n  this.write(zipUtil.getShortBytes(records));\n  this.write(zipUtil.getShortBytes(records));\n\n  // length and location of CD\n  this.write(zipUtil.getLongBytes(size));\n  this.write(zipUtil.getLongBytes(offset));\n\n  // archive comment\n  var comment = this.getComment();\n  var commentLength = Buffer.byteLength(comment);\n  this.write(zipUtil.getShortBytes(commentLength));\n  this.write(comment);\n};\n\nZipArchiveOutputStream.prototype._writeCentralDirectoryZip64 = function() {\n  // signature\n  this.write(zipUtil.getLongBytes(constants.SIG_ZIP64_EOCD));\n\n  // size of the ZIP64 EOCD record\n  this.write(zipUtil.getEightBytes(44));\n\n  // version made by\n  this.write(zipUtil.getShortBytes(constants.MIN_VERSION_ZIP64));\n\n  // version to extract\n  this.write(zipUtil.getShortBytes(constants.MIN_VERSION_ZIP64));\n\n  // disk numbers\n  this.write(constants.LONG_ZERO);\n  this.write(constants.LONG_ZERO);\n\n  // number of entries\n  this.write(zipUtil.getEightBytes(this._entries.length));\n  this.write(zipUtil.getEightBytes(this._entries.length));\n\n  // length and location of CD\n  this.write(zipUtil.getEightBytes(this._archive.centralLength));\n  this.write(zipUtil.getEightBytes(this._archive.centralOffset));\n\n  // extensible data sector\n  // not implemented at this time\n\n  // end of central directory locator\n  this.write(zipUtil.getLongBytes(constants.SIG_ZIP64_EOCD_LOC));\n\n  // disk number holding the ZIP64 EOCD record\n  this.write(constants.LONG_ZERO);\n\n  // relative offset of the ZIP64 EOCD record\n  this.write(zipUtil.getEightBytes(this._archive.centralOffset + this._archive.centralLength));\n\n  // total number of disks\n  this.write(zipUtil.getLongBytes(1));\n};\n\nZipArchiveOutputStream.prototype._writeCentralFileHeader = function(ae) {\n  var gpb = ae.getGeneralPurposeBit();\n  var method = ae.getMethod();\n  var offsets = ae._offsets;\n\n  var size = ae.getSize();\n  var compressedSize = ae.getCompressedSize();\n\n  if (ae.isZip64() || offsets.file > constants.ZIP64_MAGIC) {\n    size = constants.ZIP64_MAGIC;\n    compressedSize = constants.ZIP64_MAGIC;\n\n    ae.setVersionNeededToExtract(constants.MIN_VERSION_ZIP64);\n\n    var extraBuf = Buffer.concat([\n      zipUtil.getShortBytes(constants.ZIP64_EXTRA_ID),\n      zipUtil.getShortBytes(24),\n      zipUtil.getEightBytes(ae.getSize()),\n      zipUtil.getEightBytes(ae.getCompressedSize()),\n      zipUtil.getEightBytes(offsets.file)\n    ], 28);\n\n    ae.setExtra(extraBuf);\n  }\n\n  // signature\n  this.write(zipUtil.getLongBytes(constants.SIG_CFH));\n\n  // version made by\n  this.write(zipUtil.getShortBytes((ae.getPlatform() << 8) | constants.VERSION_MADEBY));\n\n  // version to extract and general bit flag\n  this.write(zipUtil.getShortBytes(ae.getVersionNeededToExtract()));\n  this.write(gpb.encode());\n\n  // compression method\n  this.write(zipUtil.getShortBytes(method));\n\n  // datetime\n  this.write(zipUtil.getLongBytes(ae.getTimeDos()));\n\n  // crc32 checksum\n  this.write(zipUtil.getLongBytes(ae.getCrc()));\n\n  // sizes\n  this.write(zipUtil.getLongBytes(compressedSize));\n  this.write(zipUtil.getLongBytes(size));\n\n  var name = ae.getName();\n  var comment = ae.getComment();\n  var extra = ae.getCentralDirectoryExtra();\n\n  if (gpb.usesUTF8ForNames()) {\n    name = Buffer.from(name);\n    comment = Buffer.from(comment);\n  }\n\n  // name length\n  this.write(zipUtil.getShortBytes(name.length));\n\n  // extra length\n  this.write(zipUtil.getShortBytes(extra.length));\n\n  // comments length\n  this.write(zipUtil.getShortBytes(comment.length));\n\n  // disk number start\n  this.write(constants.SHORT_ZERO);\n\n  // internal attributes\n  this.write(zipUtil.getShortBytes(ae.getInternalAttributes()));\n\n  // external attributes\n  this.write(zipUtil.getLongBytes(ae.getExternalAttributes()));\n\n  // relative offset of LFH\n  if (offsets.file > constants.ZIP64_MAGIC) {\n    this.write(zipUtil.getLongBytes(constants.ZIP64_MAGIC));\n  } else {\n    this.write(zipUtil.getLongBytes(offsets.file));\n  }\n\n  // name\n  this.write(name);\n\n  // extra\n  this.write(extra);\n\n  // comment\n  this.write(comment);\n};\n\nZipArchiveOutputStream.prototype._writeDataDescriptor = function(ae) {\n  // signature\n  this.write(zipUtil.getLongBytes(constants.SIG_DD));\n\n  // crc32 checksum\n  this.write(zipUtil.getLongBytes(ae.getCrc()));\n\n  // sizes\n  if (ae.isZip64()) {\n    this.write(zipUtil.getEightBytes(ae.getCompressedSize()));\n    this.write(zipUtil.getEightBytes(ae.getSize()));\n  } else {\n    this.write(zipUtil.getLongBytes(ae.getCompressedSize()));\n    this.write(zipUtil.getLongBytes(ae.getSize()));\n  }\n};\n\nZipArchiveOutputStream.prototype._writeLocalFileHeader = function(ae) {\n  var gpb = ae.getGeneralPurposeBit();\n  var method = ae.getMethod();\n  var name = ae.getName();\n  var extra = ae.getLocalFileDataExtra();\n\n  if (ae.isZip64()) {\n    gpb.useDataDescriptor(true);\n    ae.setVersionNeededToExtract(constants.MIN_VERSION_ZIP64);\n  }\n\n  if (gpb.usesUTF8ForNames()) {\n    name = Buffer.from(name);\n  }\n\n  ae._offsets.file = this.offset;\n\n  // signature\n  this.write(zipUtil.getLongBytes(constants.SIG_LFH));\n\n  // version to extract and general bit flag\n  this.write(zipUtil.getShortBytes(ae.getVersionNeededToExtract()));\n  this.write(gpb.encode());\n\n  // compression method\n  this.write(zipUtil.getShortBytes(method));\n\n  // datetime\n  this.write(zipUtil.getLongBytes(ae.getTimeDos()));\n\n  ae._offsets.data = this.offset;\n\n  // crc32 checksum and sizes\n  if (gpb.usesDataDescriptor()) {\n    this.write(constants.LONG_ZERO);\n    this.write(constants.LONG_ZERO);\n    this.write(constants.LONG_ZERO);\n  } else {\n    this.write(zipUtil.getLongBytes(ae.getCrc()));\n    this.write(zipUtil.getLongBytes(ae.getCompressedSize()));\n    this.write(zipUtil.getLongBytes(ae.getSize()));\n  }\n\n  // name length\n  this.write(zipUtil.getShortBytes(name.length));\n\n  // extra length\n  this.write(zipUtil.getShortBytes(extra.length));\n\n  // name\n  this.write(name);\n\n  // extra\n  this.write(extra);\n\n  ae._offsets.contents = this.offset;\n};\n\nZipArchiveOutputStream.prototype.getComment = function(comment) {\n  return this._archive.comment !== null ? this._archive.comment : '';\n};\n\nZipArchiveOutputStream.prototype.isZip64 = function() {\n  return this._archive.forceZip64 || this._entries.length > constants.ZIP64_MAGIC_SHORT || this._archive.centralLength > constants.ZIP64_MAGIC || this._archive.centralOffset > constants.ZIP64_MAGIC;\n};\n\nZipArchiveOutputStream.prototype.setComment = function(comment) {\n  this._archive.comment = comment;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/archivers/zip/zip-archive-output-stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/compress-commons.js":
/*!***************************************************************!*\
  !*** ./node_modules/compress-commons/lib/compress-commons.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nmodule.exports = {\n  ArchiveEntry: __webpack_require__(/*! ./archivers/archive-entry */ \"(ssr)/./node_modules/compress-commons/lib/archivers/archive-entry.js\"),\n  ZipArchiveEntry: __webpack_require__(/*! ./archivers/zip/zip-archive-entry */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/zip-archive-entry.js\"),\n  ArchiveOutputStream: __webpack_require__(/*! ./archivers/archive-output-stream */ \"(ssr)/./node_modules/compress-commons/lib/archivers/archive-output-stream.js\"),\n  ZipArchiveOutputStream: __webpack_require__(/*! ./archivers/zip/zip-archive-output-stream */ \"(ssr)/./node_modules/compress-commons/lib/archivers/zip/zip-archive-output-stream.js\")\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY29tcHJlc3MtY29tbW9ucy9saWIvY29tcHJlc3MtY29tbW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLG1CQUFPLENBQUMsdUdBQTJCO0FBQ25ELG1CQUFtQixtQkFBTyxDQUFDLHVIQUFtQztBQUM5RCx1QkFBdUIsbUJBQU8sQ0FBQyx1SEFBbUM7QUFDbEUsMEJBQTBCLG1CQUFPLENBQUMsdUlBQTJDO0FBQzdFIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcY29tcHJlc3MtY29tbW9uc1xcbGliXFxjb21wcmVzcy1jb21tb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbm9kZS1jb21wcmVzcy1jb21tb25zXG4gKlxuICogQ29weXJpZ2h0IChjKSAyMDE0IENocmlzIFRhbGtpbmd0b24sIGNvbnRyaWJ1dG9ycy5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hcmNoaXZlcmpzL25vZGUtY29tcHJlc3MtY29tbW9ucy9ibG9iL21hc3Rlci9MSUNFTlNFLU1JVFxuICovXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgQXJjaGl2ZUVudHJ5OiByZXF1aXJlKCcuL2FyY2hpdmVycy9hcmNoaXZlLWVudHJ5JyksXG4gIFppcEFyY2hpdmVFbnRyeTogcmVxdWlyZSgnLi9hcmNoaXZlcnMvemlwL3ppcC1hcmNoaXZlLWVudHJ5JyksXG4gIEFyY2hpdmVPdXRwdXRTdHJlYW06IHJlcXVpcmUoJy4vYXJjaGl2ZXJzL2FyY2hpdmUtb3V0cHV0LXN0cmVhbScpLFxuICBaaXBBcmNoaXZlT3V0cHV0U3RyZWFtOiByZXF1aXJlKCcuL2FyY2hpdmVycy96aXAvemlwLWFyY2hpdmUtb3V0cHV0LXN0cmVhbScpXG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/compress-commons.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/compress-commons/lib/util/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/compress-commons/lib/util/index.js ***!
  \*********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-compress-commons\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT\n */\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream);\nvar PassThrough = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").PassThrough);\n\nvar util = module.exports = {};\n\nutil.isStream = function(source) {\n  return source instanceof Stream;\n};\n\nutil.normalizeInputSource = function(source) {\n  if (source === null) {\n    return Buffer.alloc(0);\n  } else if (typeof source === 'string') {\n    return Buffer.from(source);\n  } else if (util.isStream(source) && !source._readableState) {\n    var normalized = new PassThrough();\n    source.pipe(normalized);\n\n    return normalized;\n  }\n\n  return source;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY29tcHJlc3MtY29tbW9ucy9saWIvdXRpbC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsb0RBQXdCO0FBQ3JDLGtCQUFrQiw0R0FBc0M7O0FBRXhEOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxjb21wcmVzcy1jb21tb25zXFxsaWJcXHV0aWxcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbm9kZS1jb21wcmVzcy1jb21tb25zXG4gKlxuICogQ29weXJpZ2h0IChjKSAyMDE0IENocmlzIFRhbGtpbmd0b24sIGNvbnRyaWJ1dG9ycy5cbiAqIExpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZS5cbiAqIGh0dHBzOi8vZ2l0aHViLmNvbS9hcmNoaXZlcmpzL25vZGUtY29tcHJlc3MtY29tbW9ucy9ibG9iL21hc3Rlci9MSUNFTlNFLU1JVFxuICovXG52YXIgU3RyZWFtID0gcmVxdWlyZSgnc3RyZWFtJykuU3RyZWFtO1xudmFyIFBhc3NUaHJvdWdoID0gcmVxdWlyZSgncmVhZGFibGUtc3RyZWFtJykuUGFzc1Rocm91Z2g7XG5cbnZhciB1dGlsID0gbW9kdWxlLmV4cG9ydHMgPSB7fTtcblxudXRpbC5pc1N0cmVhbSA9IGZ1bmN0aW9uKHNvdXJjZSkge1xuICByZXR1cm4gc291cmNlIGluc3RhbmNlb2YgU3RyZWFtO1xufTtcblxudXRpbC5ub3JtYWxpemVJbnB1dFNvdXJjZSA9IGZ1bmN0aW9uKHNvdXJjZSkge1xuICBpZiAoc291cmNlID09PSBudWxsKSB7XG4gICAgcmV0dXJuIEJ1ZmZlci5hbGxvYygwKTtcbiAgfSBlbHNlIGlmICh0eXBlb2Ygc291cmNlID09PSAnc3RyaW5nJykge1xuICAgIHJldHVybiBCdWZmZXIuZnJvbShzb3VyY2UpO1xuICB9IGVsc2UgaWYgKHV0aWwuaXNTdHJlYW0oc291cmNlKSAmJiAhc291cmNlLl9yZWFkYWJsZVN0YXRlKSB7XG4gICAgdmFyIG5vcm1hbGl6ZWQgPSBuZXcgUGFzc1Rocm91Z2goKTtcbiAgICBzb3VyY2UucGlwZShub3JtYWxpemVkKTtcblxuICAgIHJldHVybiBub3JtYWxpemVkO1xuICB9XG5cbiAgcmV0dXJuIHNvdXJjZTtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/compress-commons/lib/util/index.js\n");

/***/ })

};
;