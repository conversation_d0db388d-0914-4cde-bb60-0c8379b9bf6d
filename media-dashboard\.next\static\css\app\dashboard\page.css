/*!**********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/styles/dashboard.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************************/
/* تحسينات لوحة التحكم */

/* تأثيرات الحركة */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
  }
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 15px !important;
  }
  
  .stats-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }
  
  .nav-items {
    display: none !important;
  }
  
  .mobile-menu {
    display: block !important;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    flex-direction: column !important;
    gap: 15px !important;
    text-align: center !important;
  }
  
  .user-info {
    order: -1;
  }
}

/* تحسينات الألوان الداكنة */
.dark-theme {
  --bg-primary: #1a1d29;
  --bg-secondary: #2d3748;
  --text-primary: #ffffff;
  --text-secondary: #a0aec0;
  --accent-primary: #667eea;
  --accent-secondary: #764ba2;
  --success: #68d391;
  --warning: #f6ad55;
  --error: #f56565;
  --info: #4299e1;
}

/* تحسينات التمرير */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #2d3748;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #667eea;
}

/* تحسينات البطاقات */
.stat-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.5), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.stat-card:hover::before {
  transform: translateX(100%);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* تحسينات الأزرار */
.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: bold;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: transparent;
  border: 2px solid #4a5568;
  border-radius: 8px;
  color: #a0aec0;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', Arial, sans-serif;
}

.btn-secondary:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

/* تحسينات النصوص */
.gradient-text {
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* تحسينات الشبكة */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  animation: fadeIn 0.6s ease-out;
}

/* تحسينات الشريط الجانبي */
.sidebar {
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-item {
  position: relative;
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  background: #667eea;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.sidebar-item.active::before,
.sidebar-item:hover::before {
  transform: scaleY(1);
}

/* تحسينات الحالة المتصلة */
.online-indicator {
  position: relative;
}

.online-indicator::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #68d391;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* تحسينات التحميل */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #2d3748;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تحسينات الإشعارات */
.notification-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  background: #f56565;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  animation: pulse 2s infinite;
}

/* تحسينات الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
  font-family: 'Cairo', Arial, sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* تحسينات إمكانية الوصول */
.focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

