import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// مسار ملف بيانات المستخدمين
const USERS_FILE = path.join(process.cwd(), 'users-data.json');

// تعريف الأدوار والصلاحيات
export const ROLES = {
  ADMIN: {
    name: 'ADMIN',
    permissions: [
      'ALL', // صلاحيات كاملة
      'USER_CREATE', 'USER_READ', 'USER_UPDATE', 'USER_DELETE', // إدارة المستخدمين
      'MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE', // إدارة المواد
      'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', // إدارة الجداول
      'MAP_CREATE', 'MAP_READ', 'MAP_UPDATE', 'MAP_DELETE', // إدارة الخريطة
      'BROADCAST_CREATE', 'BROADCAST_READ', 'BROADCAST_UPDATE', 'BROADCAST_DELETE' // إدارة البث
    ],
    description: 'ADMIN_DESC'
  },
  CONTENT_MANAGER: {
    name: 'CONTENT_MANAGER',
    permissions: [
      'MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE', // إدارة المواد
      'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE' // إدارة الجداول
    ],
    description: 'CONTENT_MANAGER_DESC'
  },
  MEDIA_MANAGER: {
    name: 'MEDIA_MANAGER',
    permissions: [
      'MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE', // إدارة المواد فقط
      'SCHEDULE_READ' // عرض الجداول فقط
    ],
    description: 'MEDIA_MANAGER_DESC'
  },
  SCHEDULER: {
    name: 'SCHEDULER',
    permissions: [
      'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', // إدارة الجداول
      'MEDIA_READ' // عرض المواد فقط
    ],
    description: 'SCHEDULER_DESC'
  },
  FULL_VIEWER: {
    name: 'FULL_VIEWER',
    permissions: [
      'MEDIA_READ', // عرض المواد فقط
      'SCHEDULE_READ', // عرض الجداول فقط
      'MAP_READ', // عرض الخريطة فقط
      'BROADCAST_READ', // عرض البث فقط
      'REPORT_READ', // عرض التقارير فقط
      'DASHBOARD_READ' // عرض لوحة التحكم فقط
    ],
    description: 'FULL_VIEWER_DESC'
  },
  DATA_ENTRY: {
    name: 'DATA_ENTRY',
    permissions: [
      'MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE' // إدارة المواد فقط
    ],
    description: 'DATA_ENTRY_DESC'
  },
  MAP_SCHEDULER: {
    name: 'MAP_SCHEDULER',
    permissions: [
      'MAP_CREATE', 'MAP_READ', 'MAP_UPDATE', 'MAP_DELETE', // إدارة الخريطة
      'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', // إدارة الجداول
      'MEDIA_READ' // عرض المواد فقط (بدون تعديل)
    ],
    description: 'MAP_SCHEDULER_DESC'
  },
  VIEWER: {
    name: 'VIEWER',
    permissions: [
      'MEDIA_READ', // عرض المواد فقط
      'SCHEDULE_READ' // عرض الجداول فقط
    ],
    description: 'VIEWER_DESC'
  }
};

// دالة لتحميل المستخدمين من الملف
function loadUsers() {
  try {
    if (fs.existsSync(USERS_FILE)) {
      const data = fs.readFileSync(USERS_FILE, 'utf8');
      const users = JSON.parse(data);
      return users;
    } else {
      return [];
    }
  } catch (error) {
    console.error('❌ خطأ في تحميل المستخدمين:', error);
    return [];
  }
}

// دالة لحفظ المستخدمين في الملف
function saveUsers(users: any[]) {
  try {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
    console.log(`💾 تم حفظ ${users.length} مستخدم في الملف`);
  } catch (error) {
    console.error('❌ خطأ في حفظ المستخدمين:', error);
  }
}

// GET - الحصول على قائمة المستخدمين
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const role = url.searchParams.get('role');

    let filteredUsers = loadUsers();

    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }

    // إزالة كلمات المرور من النتائج
    const usersWithoutPasswords = filteredUsers.map(user => {
      const { password, ...userWithoutPassword } = user;
      return {
        ...userWithoutPassword,
        roleInfo: ROLES[user.role as keyof typeof ROLES]
      };
    });

    return NextResponse.json({
      success: true,
      users: usersWithoutPasswords,
      roles: ROLES,
      totalUsers: filteredUsers.length
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات المستخدمين'
    }, { status: 500 });
  }
}

// POST - إضافة مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const userData = await request.json();

    const { username, password, name, email, role, phone } = userData;

    // التحقق من البيانات المطلوبة
    if (!username || !password || !name || !role) {
      return NextResponse.json({
        success: false,
        error: 'جميع الحقول مطلوبة'
      }, { status: 400 });
    }

    // تحميل المستخدمين الحاليين
    const users = loadUsers();

    // التحقق من عدم وجود اسم المستخدم مسبقاً
    if (users.find(user => user.username === username)) {
      return NextResponse.json({
        success: false,
        error: 'اسم المستخدم موجود مسبقاً'
      }, { status: 400 });
    }

    // التحقق من صحة الدور
    if (!ROLES[role as keyof typeof ROLES]) {
      return NextResponse.json({
        success: false,
        error: 'دور المستخدم غير صحيح'
      }, { status: 400 });
    }

    // إنشاء المستخدم الجديد
    const newUser = {
      id: Date.now().toString(),
      username,
      password,
      name,
      email: email || '',
      phone: phone || '',
      role,
      isActive: true,
      createdAt: new Date().toISOString(),
      lastLogin: null
    };

    // إضافة المستخدم للقائمة وحفظها
    users.push(newUser);
    saveUsers(users);

    // إرجاع المستخدم بدون كلمة المرور
    const { password: _, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        roleInfo: ROLES[role as keyof typeof ROLES]
      },
      message: 'تم إنشاء المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء المستخدم'
    }, { status: 500 });
  }
}

// PUT - تحديث مستخدم
export async function PUT(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('id');
    const userData = await request.json();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'معرف المستخدم مطلوب'
      }, { status: 400 });
    }

    // تحميل المستخدمين
    const users = loadUsers();
    const userIndex = users.findIndex(user => user.id === userId);

    if (userIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'المستخدم غير موجود'
      }, { status: 404 });
    }

    // تحديث بيانات المستخدم
    users[userIndex] = {
      ...users[userIndex],
      ...userData,
      id: userId // التأكد من عدم تغيير المعرف
    };

    // حفظ التحديثات
    saveUsers(users);

    const { password: _, ...userWithoutPassword } = users[userIndex];

    return NextResponse.json({
      success: true,
      user: {
        ...userWithoutPassword,
        roleInfo: ROLES[users[userIndex].role as keyof typeof ROLES]
      },
      message: 'تم تحديث المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث المستخدم'
    }, { status: 500 });
  }
}

// DELETE - حذف مستخدم
export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('id');

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'معرف المستخدم مطلوب'
      }, { status: 400 });
    }

    // تحميل المستخدمين
    const users = loadUsers();
    const userIndex = users.findIndex(user => user.id === userId);

    if (userIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'المستخدم غير موجود'
      }, { status: 404 });
    }

    // منع حذف المدير الرئيسي
    if (users[userIndex].role === 'ADMIN' && users[userIndex].id === '1') {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف المدير الرئيسي'
      }, { status: 403 });
    }

    // حذف المستخدم
    users.splice(userIndex, 1);
    saveUsers(users);

    return NextResponse.json({
      success: true,
      message: 'تم حذف المستخدم بنجاح'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف المستخدم'
    }, { status: 500 });
  }
}
