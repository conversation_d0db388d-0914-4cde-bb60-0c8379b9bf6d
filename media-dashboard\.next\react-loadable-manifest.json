{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "app\\daily-schedule\\import\\page.tsx -> ../import-list": {"id": "app\\daily-schedule\\import\\page.tsx -> ../import-list", "files": ["static/css/_app-pages-browser_src_app_daily-schedule_import-list_tsx.css", "static/chunks/_app-pages-browser_src_app_daily-schedule_import-list_tsx.js"]}}