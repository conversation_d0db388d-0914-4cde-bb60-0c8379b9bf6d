import { useTranslation } from 'react-i18next';
import { getTranslation, getMediaTypeLabel, getRoleLabel, getRoleDescription } from '@/utils/translations';

// Hook مخصص للترجمة يضمن الاتساق
export const useAppTranslation = () => {
  const { i18n } = useTranslation();
  const currentLang = (i18n.language || 'ar') as 'ar' | 'en';
  
  // دالة الترجمة الأساسية
  const t = (key: string) => {
    return getTranslation(key, currentLang);
  };
  
  // دالة ترجمة أنواع المواد
  const tMediaType = (type: string) => {
    return getMediaTypeLabel(type, currentLang);
  };
  
  // دالة ترجمة الأدوار
  const tRole = (role: string) => {
    return getRoleLabel(role, currentLang);
  };
  
  // دالة ترجمة أوصاف الأدوار
  const tRoleDesc = (role: string) => {
    return getRoleDescription(role, currentLang);
  };
  
  return {
    t,
    tMediaType,
    tRole, 
    tRoleDesc,
    currentLang,
    isRTL: currentLang === 'ar'
  };
};
