/** @type {import('next').NextConfig} */
const nextConfig = {
  // تعطيل ESLint تماماً
  eslint: {
    ignoreDuringBuilds: true,
    dirs: [],
  },

  // تعطيل TypeScript errors
  typescript: {
    ignoreBuildErrors: true,
  },

  // إعدادات الصور
  images: {
    domains: ['localhost', '127.0.0.1'],
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
    ],
  },

  // إعدادات webpack
  webpack: (config, { dev, isServer }) => {
    // إعدادات للتطوير
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };
    }

    // حل مشاكل الوحدات
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    return config;
  },

  // إعدادات الأداء
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['react-icons'],
  },

  // إعدادات الضغط
  compress: true,

  // إعدادات الأمان
  poweredByHeader: false,
}

module.exports = nextConfig
