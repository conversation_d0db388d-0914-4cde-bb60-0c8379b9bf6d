/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/inflight";
exports.ids = ["vendor-chunks/inflight"];
exports.modules = {

/***/ "(ssr)/./node_modules/inflight/inflight.js":
/*!*******************************************!*\
  !*** ./node_modules/inflight/inflight.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var wrappy = __webpack_require__(/*! wrappy */ \"(ssr)/./node_modules/wrappy/wrappy.js\")\nvar reqs = Object.create(null)\nvar once = __webpack_require__(/*! once */ \"(ssr)/./node_modules/once/once.js\")\n\nmodule.exports = wrappy(inflight)\n\nfunction inflight (key, cb) {\n  if (reqs[key]) {\n    reqs[key].push(cb)\n    return null\n  } else {\n    reqs[key] = [cb]\n    return makeres(key)\n  }\n}\n\nfunction makeres (key) {\n  return once(function RES () {\n    var cbs = reqs[key]\n    var len = cbs.length\n    var args = slice(arguments)\n\n    // XXX It's somewhat ambiguous whether a new callback added in this\n    // pass should be queued for later execution if something in the\n    // list of callbacks throws, or if it should just be discarded.\n    // However, it's such an edge case that it hardly matters, and either\n    // choice is likely as surprising as the other.\n    // As it happens, we do go ahead and schedule it for later execution.\n    try {\n      for (var i = 0; i < len; i++) {\n        cbs[i].apply(null, args)\n      }\n    } finally {\n      if (cbs.length > len) {\n        // added more in the interim.\n        // de-zalgo, just in case, but don't call again.\n        cbs.splice(0, len)\n        process.nextTick(function () {\n          RES.apply(null, args)\n        })\n      } else {\n        delete reqs[key]\n      }\n    }\n  })\n}\n\nfunction slice (args) {\n  var length = args.length\n  var array = []\n\n  for (var i = 0; i < length; i++) array[i] = args[i]\n  return array\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/inflight/inflight.js\n");

/***/ })

};
;