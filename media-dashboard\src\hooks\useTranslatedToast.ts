import { useTranslation } from 'react-i18next';
import { useToast } from '@/components/Toast';

export const useTranslatedToast = () => {
  const { t } = useTranslation();
  const { showToast, ToastContainer } = useToast();

  const showTranslatedToast = (messageKey: string, type: 'success' | 'error' | 'info' = 'info') => {
    const message = t(`messages.${type}.${messageKey}`);
    showToast(message, type);
  };

  const showSuccessToast = (messageKey: string) => {
    showTranslatedToast(messageKey, 'success');
  };

  const showErrorToast = (messageKey: string) => {
    showTranslatedToast(messageKey, 'error');
  };

  const showInfoToast = (messageKey: string) => {
    showTranslatedToast(messageKey, 'info');
  };

  return {
    showTranslatedToast,
    showSuccessToast,
    showErrorToast,
    showInfoToast,
    ToastContainer
  };
};
