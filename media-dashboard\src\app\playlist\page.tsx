'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/Toast';
import DynamicTimeline from '@/components/DynamicTimeline';
import { TimelineItem } from '@/lib/dynamicScheduler';

interface PlaylistItem extends TimelineItem {
  mediaItem?: any;
  segments?: any[];
}

interface PlaylistStatistics {
  total: number;
  completed: number;
  current: number;
  upcoming: number;
}

export default function PlaylistPage() {
  const router = useRouter();
  const { showToast, ToastContainer } = useToast();

  const [currentTime, setCurrentTime] = useState(new Date().toLocaleTimeString('ar-SA'));
  const [isPlaying, setIsPlaying] = useState(false);
  const [playlist, setPlaylist] = useState<PlaylistItem[]>([]);
  const [statistics, setStatistics] = useState<PlaylistStatistics>({
    total: 0,
    completed: 0,
    current: 0,
    upcoming: 0
  });
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(true);
  const [availableBreakMedia, setAvailableBreakMedia] = useState<MediaItem[]>([]);
  const [showSidebar, setShowSidebar] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBreakId, setSelectedBreakId] = useState<string | null>(null);
  const [draggedItem, setDraggedItem] = useState<MediaItem | null>(null);
  const [viewMode, setViewMode] = useState<'timeline' | 'table'>('timeline');

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString('ar-SA'));
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    fetchPlaylist();
  }, [selectedDate]);

  useEffect(() => {
    // تحديث البيانات كل دقيقة لتحديث حالات العرض
    const interval = setInterval(() => {
      fetchPlaylist();
    }, 60000); // كل دقيقة

    return () => clearInterval(interval);
  }, [selectedDate]);

  const fetchPlaylist = async () => {
    try {
      const response = await fetch(`/api/playlist?date=${selectedDate}`);
      const result = await response.json();

      if (result.success) {
        setPlaylist(result.data);
        setStatistics(result.statistics);
        setAvailableBreakMedia(result.availableBreakMedia || []);
      } else {
        showToast('فشل في جلب جدول الإذاعة', 'error');
      }
    } catch (error) {
      console.error('Error fetching playlist:', error);
      showToast('حدث خطأ أثناء جلب البيانات', 'error');
    } finally {
      setLoading(false);
    }
  };

  const refreshPlaylist = () => {
    setLoading(true);
    fetchPlaylist();
    showToast('تم تحديث جدول الإذاعة', 'success');
  };

  const addToBreak = async (breakId: string, mediaItemId: string) => {
    try {
      const response = await fetch('/api/playlist/breaks', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          breakId,
          mediaItemId,
          date: selectedDate,
          insertPosition: 'end'
        })
      });

      const result = await response.json();

      if (result.success) {
        setPlaylist(result.timeline || result.playlist);
        showToast('تم إضافة المادة للفاصل بنجاح', 'success');
        refreshPlaylist();
      } else {
        showToast(result.error, 'error');
      }
    } catch (error) {
      console.error('Error adding to break:', error);
      showToast('حدث خطأ أثناء إضافة المادة للفاصل', 'error');
    }
  };

  const handleDragStart = (item: MediaItem) => {
    setDraggedItem(item);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, breakId: string) => {
    e.preventDefault();
    if (draggedItem) {
      addToBreak(breakId, draggedItem.id);
      setDraggedItem(null);
    }
  };

  const filteredBreakMedia = availableBreakMedia.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // دوال إدارة Timeline
  const handleTimelineUpdate = async (newTimeline: TimelineItem[]) => {
    try {
      // تحديث الجدول في الخادم
      const response = await fetch('/api/playlist/timeline', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          date: selectedDate,
          timeline: newTimeline
        })
      });

      const result = await response.json();
      if (result.success) {
        setPlaylist(newTimeline as PlaylistItem[]);
        showToast('تم تحديث الجدول بنجاح', 'success');
      } else {
        showToast(result.error, 'error');
      }
    } catch (error) {
      console.error('Error updating timeline:', error);
      showToast('حدث خطأ أثناء تحديث الجدول', 'error');
    }
  };

  const handleItemEdit = async (itemId: string, newDuration: string) => {
    try {
      const response = await fetch('/api/playlist/timeline/item', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          itemId,
          newDuration,
          date: selectedDate
        })
      });

      const result = await response.json();
      if (result.success) {
        setPlaylist(result.timeline);
        showToast('تم تحديث مدة العنصر بنجاح', 'success');
      } else {
        showToast(result.error, 'error');
      }
    } catch (error) {
      console.error('Error editing item:', error);
      showToast('حدث خطأ أثناء تعديل العنصر', 'error');
    }
  };

  const handleItemDelete = async (itemId: string) => {
    if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;

    try {
      const response = await fetch(`/api/playlist/timeline/item?itemId=${itemId}&date=${selectedDate}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      if (result.success) {
        setPlaylist(result.timeline);
        showToast('تم حذف العنصر بنجاح', 'success');
      } else {
        showToast(result.error, 'error');
      }
    } catch (error) {
      console.error('Error deleting item:', error);
      showToast('حدث خطأ أثناء حذف العنصر', 'error');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'منتهي': return '#6c757d';
      case 'يُعرض الآن': return '#28a745';
      case 'قادم': return '#007bff';
      default: return '#6c757d';
    }
  };

  const getTypeIcon = (type: string) => {
    const icons: { [key: string]: string } = {
      PROGRAM: '📺',
      SERIES: '🎭',
      FILM: '🎬',
      MOVIE: '🎬', // للتوافق مع البيانات القديمة
      FILLER: '📦',
      STING: '⚡',
      PROMO: '📢',
      NEXT: '▶️',
      NOW: '🔴',
      'سنعود': '⏰',
      'عدنا': '✅',
      'برنامج': '📺',
      'أخبار': '📰',
      'فيلم': '🎬',
      'أغنية': '🎵',
      'مخصص': '🔧'
    };
    return icons[type] || '📺';
  };

  const getTypeLabel = (type: string) => {
    const types: { [key: string]: string } = {
      PROGRAM: 'Program',
      SERIES: 'Series',
      FILM: 'Film',
      MOVIE: 'Film', // للتوافق مع البيانات القديمة
      FILLER: 'Filler',
      STING: 'Sting',
      PROMO: 'Promo',
      NEXT: 'Next',
      NOW: 'Now',
      'سنعود': 'سنعود',
      'عدنا': 'عدنا',
      'مخصص': 'مخصص'
    };
    return types[type] || type;
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>⏳ جاري تحميل جدول الإذاعة...</div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl',
      display: 'flex',
      gap: '20px'
    }}>
      {/* القائمة الجانبية للمواد الصغيرة */}
      <div style={{
        width: showSidebar ? '300px' : '60px',
        background: 'rgba(255,255,255,0.95)',
        borderRadius: '20px',
        padding: showSidebar ? '20px' : '10px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)',
        transition: 'width 0.3s ease',
        overflow: 'hidden'
      }}>
        <button
          onClick={() => setShowSidebar(!showSidebar)}
          style={{
            background: 'linear-gradient(45deg, #007bff, #0056b3)',
            color: 'white',
            border: 'none',
            borderRadius: '10px',
            padding: '10px',
            cursor: 'pointer',
            width: '100%',
            marginBottom: showSidebar ? '20px' : '0'
          }}
        >
          {showSidebar ? '◀️ إخفاء' : '📚'}
        </button>

        {showSidebar && (
          <>
            <h3 style={{ color: '#2c3e50', marginBottom: '15px', fontSize: '1.1rem' }}>
              🎵 مواد الفواصل
            </h3>

            <input
              type="text"
              placeholder="ابحث في المواد..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '8px',
                border: '2px solid #e0e0e0',
                borderRadius: '8px',
                fontSize: '0.9rem',
                marginBottom: '15px',
                direction: 'rtl'
              }}
            />

            <div style={{
              maxHeight: '400px',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '10px'
            }}>
              {filteredBreakMedia.map((item) => (
                <div
                  key={item.id}
                  draggable
                  onDragStart={() => handleDragStart(item)}
                  style={{
                    background: '#f8f9fa',
                    borderRadius: '8px',
                    padding: '10px',
                    border: '1px solid #e9ecef',
                    cursor: 'grab',
                    transition: 'transform 0.2s ease'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.transform = 'scale(1.02)'}
                  onMouseLeave={(e) => e.currentTarget.style.transform = 'scale(1)'}
                >
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontSize: '1.2rem' }}>{getTypeIcon(item.type)}</span>
                    <div>
                      <h5 style={{ margin: '0 0 3px 0', fontSize: '0.9rem', color: '#333' }}>
                        {item.name}
                      </h5>
                      <p style={{ margin: 0, fontSize: '0.8rem', color: '#6c757d' }}>
                        {getTypeLabel(item.type)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div style={{
              marginTop: '15px',
              padding: '10px',
              background: '#e3f2fd',
              borderRadius: '8px',
              fontSize: '0.8rem',
              color: '#1565c0'
            }}>
              💡 اسحب المواد إلى الفواصل في الجدول
            </div>
          </>
        )}
      </div>

      {/* المحتوى الرئيسي */}
      <div style={{
        flex: 1,
        background: 'white',
        borderRadius: '20px',
        padding: '40px',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '30px' }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#333',
            margin: 0,
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            ▶️ جدول الإذاعة اليومية
          </h1>
          <button
            onClick={() => router.push('/')}
            style={{
              background: 'linear-gradient(45deg, #6c757d, #495057)',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '10px 20px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            🏠 العودة للرئيسية
          </button>
        </div>

        {/* معلومات البث المباشر */}
        <div style={{
          background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',
          borderRadius: '15px',
          padding: '25px',
          marginBottom: '30px',
          color: 'white',
          textAlign: 'center'
        }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: '20px' }}>
            <div>
              <h2 style={{ margin: '0 0 10px 0', fontSize: '1.5rem' }}>🔴 البث المباشر</h2>
              <p style={{ margin: 0, fontSize: '1.1rem' }}>الوقت الحالي: {currentTime}</p>
              <p style={{ margin: '5px 0 0 0', fontSize: '0.9rem', opacity: 0.8 }}>
                التاريخ: {new Date(selectedDate).toLocaleDateString('ar-SA')}
              </p>
            </div>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
              <input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                style={{
                  padding: '10px',
                  border: '2px solid rgba(255,255,255,0.3)',
                  borderRadius: '10px',
                  background: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontSize: '1rem'
                }}
              />

              {/* أزرار تبديل العرض */}
              <div style={{ display: 'flex', background: 'rgba(255,255,255,0.2)', borderRadius: '10px', overflow: 'hidden' }}>
                <button
                  onClick={() => setViewMode('timeline')}
                  style={{
                    background: viewMode === 'timeline' ? 'rgba(255,255,255,0.3)' : 'transparent',
                    color: 'white',
                    border: 'none',
                    padding: '10px 15px',
                    cursor: 'pointer',
                    fontSize: '0.9rem'
                  }}
                >
                  ⏰ Timeline
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  style={{
                    background: viewMode === 'table' ? 'rgba(255,255,255,0.3)' : 'transparent',
                    color: 'white',
                    border: 'none',
                    padding: '10px 15px',
                    cursor: 'pointer',
                    fontSize: '0.9rem'
                  }}
                >
                  📋 جدول
                </button>
              </div>

              <button
                onClick={() => setIsPlaying(!isPlaying)}
                style={{
                  background: isPlaying ? '#dc3545' : '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '10px',
                  padding: '10px 20px',
                  cursor: 'pointer',
                  fontSize: '1rem',
                  fontWeight: 'bold'
                }}
              >
                {isPlaying ? '⏸️ إيقاف' : '▶️ تشغيل'}
              </button>
              <button
                onClick={refreshPlaylist}
                style={{
                  background: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  border: '2px solid white',
                  borderRadius: '10px',
                  padding: '10px 20px',
                  cursor: 'pointer',
                  fontSize: '1rem'
                }}
              >
                🔄 تحديث
              </button>
            </div>
          </div>
        </div>

        {/* رسالة توضيحية للسحب والإفلات */}
        {viewMode === 'timeline' && (
          <div style={{
            background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
            borderRadius: '12px',
            padding: '15px',
            marginBottom: '20px',
            border: '1px solid #90caf9',
            textAlign: 'center'
          }}>
            <h4 style={{ color: '#1565c0', margin: '0 0 8px 0', fontSize: '1rem' }}>
              💡 كيفية استخدام Timeline التفاعلي
            </h4>
            <p style={{ color: '#1565c0', margin: 0, fontSize: '0.9rem' }}>
              • اسحب المواد الصغيرة من القائمة الجانبية وأفلتها على الفواصل الصفراء أو الرمادية
              <br />
              • انقر على أي عنصر لتعديل مدته • استخدم أزرار الحذف لإزالة العناصر
            </p>
          </div>
        )}

        {/* عرض Timeline أو الجدول */}
        {viewMode === 'timeline' ? (
          <DynamicTimeline
            timeline={playlist}
            onTimelineUpdate={handleTimelineUpdate}
            onItemEdit={handleItemEdit}
            onItemDelete={handleItemDelete}
            onBreakContentAdd={addToBreak}
            availableBreakMedia={filteredBreakMedia}
            isEditable={true}
          />
        ) : (
          <div style={{
            background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '30px',
            border: '1px solid #dee2e6'
          }}>
            <h2 style={{ color: '#495057', marginBottom: '20px', fontSize: '1.3rem' }}>
              📋 جدول اليوم - {new Date(selectedDate).toLocaleDateString('ar-SA')}
            </h2>

          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse', background: 'white', borderRadius: '10px', overflow: 'hidden' }}>
              <thead>
                <tr style={{ background: 'linear-gradient(45deg, #667eea, #764ba2)', color: 'white' }}>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>النوع</th>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>اسم المادة</th>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>وقت البداية</th>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>وقت النهاية</th>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>المدة</th>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>الحالة</th>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>التقدم</th>
                  <th style={{ padding: '15px', textAlign: 'center', fontSize: '1rem' }}>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {playlist.length === 0 ? (
                  <tr>
                    <td colSpan={8} style={{ padding: '40px', textAlign: 'center', color: '#6c757d' }}>
                      <div style={{ fontSize: '3rem', marginBottom: '15px' }}>📭</div>
                      <h3>لا توجد مواد مجدولة لهذا اليوم</h3>
                      <p>انتقل إلى الخريطة البرامجية لإضافة مواد لهذا اليوم</p>
                    </td>
                  </tr>
                ) : (
                  playlist.map((item) => (
                    <tr
                      key={item.id}
                      style={{
                        borderBottom: '1px solid #dee2e6',
                        background: item.status === 'يُعرض الآن' ? '#d4edda' :
                                   item.isBreak || item.isGap ? '#fff3cd' : 'white'
                      }}
                      onDragOver={item.isBreak || item.isGap ? handleDragOver : undefined}
                      onDrop={item.isBreak || item.isGap ? (e) => handleDrop(e, item.id) : undefined}
                    >
                      <td style={{ padding: '15px', textAlign: 'center', fontSize: '1.5rem' }}>
                        {item.isBreak ? '⏸️' : item.isGap ? '⏳' : getTypeIcon(item.type)}
                      </td>
                      <td style={{
                        padding: '15px',
                        textAlign: 'center',
                        fontWeight: item.status === 'يُعرض الآن' ? 'bold' : 'normal'
                      }}>
                        <div>
                          {item.segmentNumber && (
                            <span style={{
                              background: '#007bff',
                              color: 'white',
                              padding: '2px 6px',
                              borderRadius: '10px',
                              fontSize: '0.7rem',
                              marginLeft: '5px'
                            }}>
                              {item.segmentNumber}/{item.totalSegments}
                            </span>
                          )}
                          {item.name}
                          {item.isBreak && (
                            <div style={{
                              fontSize: '0.8rem',
                              color: '#856404',
                              marginTop: '5px',
                              fontStyle: 'italic'
                            }}>
                              اسحب مادة هنا للإضافة
                            </div>
                          )}
                          {item.segment && (
                            <div style={{ fontSize: '0.8rem', color: '#6c757d', marginTop: '5px' }}>
                              كود: {item.segment.code || 'غير محدد'}
                            </div>
                          )}
                        </div>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>{item.startTime}</td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>{item.endTime}</td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>{item.duration}</td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <span style={{
                          background: getStatusColor(item.status),
                          color: 'white',
                          padding: '5px 12px',
                          borderRadius: '15px',
                          fontSize: '0.9rem',
                          fontWeight: 'bold'
                        }}>
                          {item.status}
                        </span>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '10px', justifyContent: 'center' }}>
                          <div style={{
                            width: '60px',
                            height: '8px',
                            background: '#e9ecef',
                            borderRadius: '4px',
                            overflow: 'hidden'
                          }}>
                            <div style={{
                              width: `${item.progress}%`,
                              height: '100%',
                              background: getStatusColor(item.status),
                              transition: 'width 0.3s ease'
                            }}></div>
                          </div>
                          <span style={{ fontSize: '0.9rem', color: '#666' }}>{item.progress}%</span>
                        </div>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <div style={{ display: 'flex', gap: '5px', justifyContent: 'center' }}>
                          {(item.isBreak || item.isGap) && (
                            <button
                              onClick={() => {
                                setSelectedBreakId(item.id);
                                showToast('اسحب مادة من القائمة الجانبية', 'info');
                              }}
                              style={{
                              background: '#28a745',
                              color: 'white',
                              border: 'none',
                              borderRadius: '5px',
                              padding: '5px 8px',
                              cursor: 'pointer',
                              fontSize: '0.8rem'
                            }}>
                              ➕
                            </button>
                          )}
                          {!item.isBreak && !item.isGap && (
                            <button
                              onClick={() => showToast('ميزة التعديل ستكون متاحة قريباً', 'info')}
                              style={{
                              background: '#ffc107',
                              color: 'white',
                              border: 'none',
                              borderRadius: '5px',
                              padding: '5px 8px',
                              cursor: 'pointer',
                              fontSize: '0.8rem'
                            }}>
                              ✏️
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
        )}

        {/* أزرار الإجراءات */}
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            onClick={() => alert('سيتم إضافة نموذج إضافة المواد قريباً')}
            style={{
              background: 'linear-gradient(45deg, #007bff, #0056b3)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,123,255,0.3)',
            }}
          >
            ➕ إضافة مادة
          </button>

          <button
            onClick={() => alert('تم تصدير الجدول! (هذا مجرد عرض تجريبي)')}
            style={{
              background: 'linear-gradient(45deg, #28a745, #20c997)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(40,167,69,0.3)',
            }}
          >
            📤 تصدير الجدول
          </button>

          <button
            onClick={() => alert('تم حفظ التغييرات! (هذا مجرد عرض تجريبي)')}
            style={{
              background: 'linear-gradient(45deg, #9c27b0, #7b1fa2)',
              color: 'white',
              border: 'none',
              borderRadius: '25px',
              padding: '15px 30px',
              fontSize: '1rem',
              fontWeight: 'bold',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(156,39,176,0.3)',
            }}
          >
            💾 حفظ التغييرات
          </button>
        </div>

        {/* معلومات إضافية */}
        <div style={{
          marginTop: '30px',
          padding: '20px',
          background: 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)',
          borderRadius: '15px',
          border: '1px solid #ffeaa7',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#856404', marginBottom: '10px' }}>📊 إحصائيات اليوم</h3>
          <div style={{ display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap', gap: '20px' }}>
            <div>
              <strong style={{ color: '#856404' }}>إجمالي العناصر:</strong>
              <span style={{ color: '#856404', marginLeft: '5px' }}>{statistics.total}</span>
            </div>
            <div>
              <strong style={{ color: '#856404' }}>السيجمانت:</strong>
              <span style={{ color: '#856404', marginLeft: '5px' }}>{statistics.segments || 0}</span>
            </div>
            <div>
              <strong style={{ color: '#856404' }}>الفواصل:</strong>
              <span style={{ color: '#856404', marginLeft: '5px' }}>{statistics.breaks || 0}</span>
            </div>
            <div>
              <strong style={{ color: '#856404' }}>المنتهية:</strong>
              <span style={{ color: '#856404', marginLeft: '5px' }}>{statistics.completed}</span>
            </div>
            <div>
              <strong style={{ color: '#856404' }}>قيد العرض:</strong>
              <span style={{ color: '#856404', marginLeft: '5px' }}>{statistics.current}</span>
            </div>
            <div>
              <strong style={{ color: '#856404' }}>القادمة:</strong>
              <span style={{ color: '#856404', marginLeft: '5px' }}>{statistics.upcoming}</span>
            </div>
          </div>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
}