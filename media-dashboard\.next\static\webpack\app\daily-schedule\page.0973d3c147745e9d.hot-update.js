"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/daily-schedule/page",{

/***/ "(app-pages-browser)/./src/app/daily-schedule/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/daily-schedule/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _daily_schedule_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./daily-schedule.css */ \"(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DailySchedulePage() {\n    _s();\n    const { user, isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_5__.useAppTranslation)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [gridRows, setGridRows] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('ALL');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [showWeeklySchedule, setShowWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // تهيئة التاريخ الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            setSelectedDate(today.toISOString().split('T')[0]);\n        }\n    }[\"DailySchedulePage.useEffect\"], []);\n    // جلب البيانات عند تغيير التاريخ\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (selectedDate) {\n                fetchScheduleData();\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        selectedDate\n    ]);\n    // جلب بيانات الجدول الإذاعي\n    const fetchScheduleData = async ()=>{\n        setLoading(true);\n        try {\n            console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);\n            console.log('🌐 URL المطلوب:', \"/api/daily-schedule?date=\".concat(selectedDate));\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة:', data);\n            if (data.success) {\n                var _data_data_scheduleItems, _data_data_availableMedia, _data_data_scheduleRows, _data_data_availableMedia1, _data_data_availableMedia2;\n                console.log('✅ استجابة ناجحة من API:', {\n                    scheduleItemsCount: ((_data_data_scheduleItems = data.data.scheduleItems) === null || _data_data_scheduleItems === void 0 ? void 0 : _data_data_scheduleItems.length) || 0,\n                    availableMediaCount: ((_data_data_availableMedia = data.data.availableMedia) === null || _data_data_availableMedia === void 0 ? void 0 : _data_data_availableMedia.length) || 0,\n                    scheduleRowsCount: ((_data_data_scheduleRows = data.data.scheduleRows) === null || _data_data_scheduleRows === void 0 ? void 0 : _data_data_scheduleRows.length) || 0,\n                    date: data.data.date,\n                    dayOfWeek: data.data.dayOfWeek\n                });\n                setScheduleItems(data.data.scheduleItems);\n                setAvailableMedia(data.data.availableMedia || []);\n                console.log(\"\\uD83D\\uDCDA تم تحديد \".concat(((_data_data_availableMedia1 = data.data.availableMedia) === null || _data_data_availableMedia1 === void 0 ? void 0 : _data_data_availableMedia1.length) || 0, \" مادة متاحة في القائمة الجانبية\"));\n                // التحقق من وجود صفوف في الجدول\n                if (data.data.scheduleRows && data.data.scheduleRows.length > 0) {\n                    // تطبيق الحماية على المواد الأساسية المحفوظة\n                    const protectedRows = data.data.scheduleRows.map((row)=>{\n                        if (row.type === 'segment' && !row.isTemporary) {\n                            return {\n                                ...row,\n                                canDelete: false\n                            }; // حماية المواد الأساسية\n                        }\n                        return row;\n                    });\n                    setGridRows(protectedRows);\n                    console.log('📝 تم تحميل', protectedRows.length, 'صف من الجدول المحفوظ مع تطبيق الحماية');\n                } else {\n                    // إذا لم تكن هناك صفوف، نحاول بناء الجدول من الخريطة البرامجية\n                    console.log('⚠️ لم يتم العثور على جدول محفوظ، سيتم محاولة بناء الجدول من الخريطة البرامجية');\n                    await buildScheduleFromWeekly();\n                }\n                if (data.fromSavedFile) {\n                    console.log('📂 تم تحميل جدول محفوظ مسبقاً');\n                    console.log('💾 تاريخ الحفظ:', data.savedAt);\n                    console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);\n                } else {\n                    console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');\n                    console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');\n                }\n                console.log('📦 المواد المتاحة:', ((_data_data_availableMedia2 = data.data.availableMedia) === null || _data_data_availableMedia2 === void 0 ? void 0 : _data_data_availableMedia2.length) || 0);\n                // عرض عينة من المواد المتاحة\n                if (data.data.availableMedia && data.data.availableMedia.length > 0) {\n                    console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));\n                } else {\n                    console.log('⚠️ لا توجد مواد متاحة في القائمة الجانبية');\n                }\n            } else {\n                console.log('❌ فشل في جلب البيانات:', data.error || 'خطأ غير محدد');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n            // جلب الجدول الأسبوعي للمراجعة\n            await fetchWeeklySchedule();\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        } finally{\n            setLoading(false);\n            console.log('✅ انتهى تحميل البيانات');\n        }\n    };\n    // البحث عن الاستنجات المطابقة لمادة معينة\n    const findMatchingStings = (mediaName, stingType)=>{\n        return mediaItems.filter((item)=>item.name === mediaName && item.type === stingType && item.status === 'VALID' && item.tx === true);\n    };\n    // إضافة استنجات تلقائية بين السيجمنت\n    const addAutomaticStings = (scheduleRows, mediaName)=>{\n        console.log(\"\\uD83D\\uDD0D البحث عن استنجات للمادة: \".concat(mediaName));\n        // البحث عن استنجات \"سنعود\" و \"عدنا\" لهذه المادة\n        const snawodStings = findMatchingStings(mediaName, 'سنعود');\n        const odnaStings = findMatchingStings(mediaName, 'عدنا');\n        console.log(\"\\uD83D\\uDCE6 وجدت \".concat(snawodStings.length, ' استنج \"سنعود\" و ').concat(odnaStings.length, ' استنج \"عدنا\" للمادة: ').concat(mediaName));\n        if (snawodStings.length === 0 && odnaStings.length === 0) {\n            console.log(\"⚠️ لا توجد استنجات للمادة: \".concat(mediaName));\n            return scheduleRows; // لا توجد استنجات متطابقة\n        }\n        // البحث عن المادة الأساسية في الجدول\n        const mediaRows = scheduleRows.map((row, index)=>({\n                ...row,\n                originalIndex: index\n            })).filter((row)=>row.type === 'segment' && row.content && row.content.includes(mediaName) && !row.isAutoGenerated // تجنب المواد المولدة تلقائياً\n        );\n        console.log(\"\\uD83C\\uDFAC وجدت \".concat(mediaRows.length, \" صف للمادة: \").concat(mediaName));\n        if (mediaRows.length <= 1) {\n            console.log(\"⚠️ المادة \".concat(mediaName, \" لها سيجمنت واحد فقط - لا حاجة للاستنجات\"));\n            return scheduleRows; // لا حاجة للاستنجات إذا كان سيجمنت واحد فقط\n        }\n        const newRows = [\n            ...scheduleRows\n        ];\n        const insertions = [];\n        // إضافة استنجات بين كل سيجمنت والتالي\n        for(let i = 0; i < mediaRows.length - 1; i++){\n            const currentRow = mediaRows[i];\n            // إضافة \"سنعود\" بعد السيجمنت الحالي\n            if (snawodStings.length > 0) {\n                const snawodSting = snawodStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex,\n                    sting: {\n                        id: \"auto_snawod_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(snawodSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(snawodSting),\n                        mediaType: 'سنعود',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n            // إضافة \"عدنا\" قبل السيجمنت التالي (بعد \"سنعود\")\n            if (odnaStings.length > 0) {\n                const odnaSting = odnaStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex + (snawodStings.length > 0 ? 1 : 0),\n                    sting: {\n                        id: \"auto_odna_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(odnaSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(odnaSting),\n                        mediaType: 'عدنا',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n        }\n        // إدراج الاستنجات من النهاية للبداية لتجنب تغيير الفهارس\n        insertions.sort((a, b)=>b.afterIndex - a.afterIndex);\n        insertions.forEach((insertion)=>{\n            newRows.splice(insertion.afterIndex + 1, 0, insertion.sting);\n        });\n        console.log(\"✨ تم إضافة \".concat(insertions.length, \" استنج تلقائي للمادة: \").concat(mediaName));\n        return newRows;\n    };\n    // بناء الجدول من الخريطة البرامجية\n    const buildScheduleFromWeekly = async ()=>{\n        try {\n            console.log('🔄 محاولة بناء الجدول من الخريطة البرامجية للتاريخ:', selectedDate);\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            console.log('📅 تفاصيل التاريخ:');\n            console.log('  - التاريخ الأصلي:', selectedDate);\n            console.log('  - التاريخ المحول:', date.toISOString().split('T')[0]);\n            console.log('  - يوم الأسبوع:', date.getDay(), '(0=أحد, 1=اثنين, 2=ثلاثاء, 3=أربعاء, 4=خميس, 5=جمعة, 6=سبت)');\n            console.log('  - بداية الأسبوع:', weekStartStr);\n            console.log('  - نهاية الأسبوع:', new Date(new Date(weekStartStr).getTime() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);\n            console.log('🌐 طلب البيانات من:', \"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status);\n                return;\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة من weekly-schedule:', data);\n            if (data.success && data.data && data.data.scheduleItems) {\n                const dayOfWeek = date.getDay();\n                console.log('📅 اليوم المطلوب:', dayOfWeek);\n                console.log('📦 إجمالي المواد في الخريطة:', data.data.scheduleItems.length);\n                // عرض جميع المواد للتشخيص\n                console.log('🔍 جميع المواد في الخريطة:');\n                data.data.scheduleItems.forEach((item, index)=>{\n                    var _item_mediaItem;\n                    console.log(\"  \".concat(index + 1, \". \").concat(((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime, \" - إعادة: \").concat(item.isRerun));\n                });\n                // فلترة المواد الخاصة بهذا اليوم\n                const dayItems = data.data.scheduleItems.filter((item)=>{\n                    const matches = item.dayOfWeek === dayOfWeek && item.mediaItem;\n                    if (matches) {\n                        var _item_mediaItem;\n                        console.log(\"✅ مادة متطابقة: \".concat((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime));\n                    } else {\n                        var _item_mediaItem1;\n                        console.log(\"❌ مادة غير متطابقة: \".concat(((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" (مطلوب: \").concat(dayOfWeek, \") - mediaItem: \").concat(!!item.mediaItem));\n                    }\n                    return matches;\n                });\n                console.log('📋 عدد المواد المطابقة لهذا اليوم:', dayItems.length);\n                if (dayItems.length > 0) {\n                    // ترتيب المواد حسب وقت البداية\n                    dayItems.sort((a, b)=>a.startTime.localeCompare(b.startTime));\n                    // بناء صفوف الجدول\n                    const newRows = [];\n                    let currentTime = '08:00:00';\n                    // إضافة صف فارغ في البداية\n                    newRows.push({\n                        id: \"empty_start_\".concat(Date.now()),\n                        type: 'empty',\n                        time: currentTime,\n                        canDelete: true\n                    });\n                    // إضافة المواد\n                    for (const item of dayItems){\n                        // إضافة المادة\n                        const mediaItem = item.mediaItem;\n                        const itemName = mediaItem.name;\n                        const itemType = mediaItem.type;\n                        const itemId = mediaItem.id;\n                        // إضافة تفاصيل المادة\n                        const details = [];\n                        if (mediaItem.episodeNumber) details.push(\"ح\".concat(mediaItem.episodeNumber));\n                        if (mediaItem.seasonNumber && mediaItem.seasonNumber > 0) details.push(\"م\".concat(mediaItem.seasonNumber));\n                        if (mediaItem.partNumber) details.push(\"ج\".concat(mediaItem.partNumber));\n                        const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n                        // تحديد نوع المادة\n                        let rowType = 'segment';\n                        let itemContent = \"\".concat(itemName).concat(detailsText);\n                        if ([\n                            'PROMO',\n                            'STING',\n                            'FILLER',\n                            'FILL_IN'\n                        ].includes(itemType)) {\n                            rowType = 'filler';\n                            itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                        }\n                        // حساب المدة\n                        let itemDuration = '00:01:00';\n                        if (mediaItem.duration) {\n                            itemDuration = mediaItem.duration;\n                        } else if (mediaItem.segments && mediaItem.segments.length > 0) {\n                            let totalSeconds = 0;\n                            mediaItem.segments.forEach((segment)=>{\n                                if (segment.duration) {\n                                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                                }\n                            });\n                            if (totalSeconds > 0) {\n                                const hours = Math.floor(totalSeconds / 3600);\n                                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                                const secs = totalSeconds % 60;\n                                itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                            }\n                        }\n                        // إضافة الصف\n                        const newRow = {\n                            id: \"\".concat(rowType, \"_\").concat(Date.now(), \"_\").concat(itemId),\n                            type: rowType,\n                            time: item.startTime,\n                            content: itemContent,\n                            mediaItemId: itemId,\n                            duration: itemDuration,\n                            canDelete: false,\n                            isRerun: item.isRerun,\n                            isTemporary: false,\n                            originalStartTime: item.startTime\n                        };\n                        console.log('🔒 إضافة مادة أساسية محمية:', newRow.content, 'canDelete:', newRow.canDelete);\n                        newRows.push(newRow);\n                        // تحديث الوقت الحالي\n                        currentTime = calculateNextTime(item.startTime, itemDuration);\n                    }\n                    // إضافة الاستنجات التلقائية لكل مادة\n                    let finalRows = [\n                        ...newRows\n                    ];\n                    const processedMediaNames = new Set();\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem;\n                        const mediaName = (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name;\n                        if (mediaName && !processedMediaNames.has(mediaName)) {\n                            processedMediaNames.add(mediaName);\n                            finalRows = addAutomaticStings(finalRows, mediaName);\n                        }\n                    });\n                    // إضافة صفوف فارغة في النهاية\n                    for(let i = 0; i < 8; i++){\n                        finalRows.push({\n                            id: \"empty_end_\".concat(Date.now(), \"_\").concat(i),\n                            type: 'empty',\n                            canDelete: true\n                        });\n                    }\n                    // تحديث الجدول\n                    setGridRows(finalRows);\n                    console.log('✅ تم بناء الجدول من الخريطة البرامجية:', finalRows.length, 'صف');\n                    // إعادة حساب الأوقات\n                    recalculateTimes(finalRows);\n                } else {\n                    console.log('⚠️ لا توجد مواد لهذا اليوم في الخريطة البرامجية');\n                    console.log('💡 تحقق من:');\n                    console.log('  - وجود مواد في الخريطة البرامجية لهذا اليوم');\n                    console.log('  - صحة يوم الأسبوع المحسوب');\n                    console.log('  - وجود mediaItem في كل مادة');\n                }\n            } else {\n                console.log('❌ فشل في جلب بيانات الخريطة البرامجية');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في بناء الجدول من الخريطة البرامجية:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        }\n    };\n    // جلب الجدول الأسبوعي للمراجعة\n    const fetchWeeklySchedule = async ()=>{\n        try {\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const data = await response.json();\n            console.log('📊 استجابة API للجدول الأسبوعي:', data);\n            if (data.success && data.data) {\n                // استخراج مواد اليوم المحدد من scheduleItems\n                const dayOfWeek = new Date(selectedDate).getDay();\n                const daySchedule = [];\n                console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);\n                console.log('📦 البيانات المتاحة:', data.data);\n                // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)\n                if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {\n                    console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);\n                    const dayItems = data.data.scheduleItems.filter((item)=>{\n                        var _item_mediaItem;\n                        console.log('🔍 فحص المادة:', (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);\n                        return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء\n                    }).sort((a, b)=>{\n                        // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59\n                        const timeA = a.startTime;\n                        const timeB = b.startTime;\n                        const getTimeOrder = (time)=>{\n                            const hour = parseInt(time.split(':')[0]);\n                            if (hour >= 8 && hour < 18) return 1; // صباح ومساء\n                            if (hour >= 18) return 2; // برايم تايم\n                            return 3; // منتصف الليل والفجر\n                        };\n                        const orderA = getTimeOrder(timeA);\n                        const orderB = getTimeOrder(timeB);\n                        if (orderA !== orderB) return orderA - orderB;\n                        return timeA.localeCompare(timeB);\n                    });\n                    console.log('✅ مواد اليوم المفلترة:', dayItems.length);\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3;\n                        const scheduleItem = {\n                            time: item.startTime,\n                            name: ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'مادة غير محددة',\n                            episodeNumber: item.episodeNumber || ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.episodeNumber),\n                            partNumber: item.partNumber || ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.partNumber),\n                            seasonNumber: item.seasonNumber || ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.seasonNumber),\n                            isRerun: item.isRerun || false\n                        };\n                        daySchedule.push(scheduleItem);\n                        console.log('📺 إضافة مادة للخريطة:', scheduleItem);\n                    });\n                } else {\n                    console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');\n                }\n                setWeeklySchedule(daySchedule);\n                console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');\n            } else {\n                console.log('❌ فشل في جلب البيانات أو البيانات فارغة');\n                setWeeklySchedule([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);\n            setWeeklySchedule([]);\n        }\n    };\n    // فلترة المواد المتاحة\n    const filteredMedia = availableMedia.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesType = filterType === 'ALL' || item.type === filterType;\n        return matchesSearch && matchesType;\n    });\n    // أنواع المواد للفلترة\n    const mediaTypes = [\n        'ALL',\n        'PROGRAM',\n        'SERIES',\n        'FILM',\n        'SONG',\n        'PROMO',\n        'STING',\n        'FILLER',\n        'NEXT',\n        'NOW',\n        'سنعود',\n        'عدنا',\n        'MINI',\n        'CROSS'\n    ];\n    // دالة ترجمة أنواع المواد\n    const getTypeLabel = (type)=>{\n        const typeLabels = {\n            'ALL': t('schedule.allTypes'),\n            'PROGRAM': t('schedule.types.program'),\n            'SERIES': t('schedule.types.series'),\n            'FILM': t('schedule.types.film'),\n            'MOVIE': t('schedule.types.film'),\n            'SONG': t('schedule.types.song'),\n            'STING': t('schedule.types.sting'),\n            'FILL_IN': t('schedule.types.fillIn'),\n            'FILLER': t('schedule.types.filler'),\n            'PROMO': t('schedule.types.promo'),\n            'NEXT': t('schedule.types.next'),\n            'NOW': t('schedule.types.now'),\n            'سنعود': t('mediaTypes.سنعود'),\n            'عدنا': t('mediaTypes.عدنا'),\n            'MINI': t('mediaTypes.MINI'),\n            'CROSS': t('mediaTypes.CROSS')\n        };\n        return typeLabels[type] || type;\n    };\n    // إضافة صف فارغ\n    const addEmptyRow = (afterIndex)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        const newRow = {\n            id: \"empty_\".concat(Date.now()),\n            type: 'empty',\n            canDelete: true\n        };\n        newRows.splice(afterIndex + 1, 0, newRow);\n        setGridRows(newRows);\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // إضافة صفوف فارغة متعددة\n    const addMultipleEmptyRows = function(afterIndex) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 8;\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        for(let i = 0; i < count; i++){\n            const newRow = {\n                id: \"empty_\".concat(Date.now(), \"_\").concat(i),\n                type: 'empty',\n                canDelete: true\n            };\n            newRows.splice(afterIndex + 1 + i, 0, newRow);\n        }\n        setGridRows(newRows);\n        console.log(\"✅ تم إضافة \".concat(count, \" صف فارغ\"));\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // التحقق من الحاجة لإضافة صفوف فارغة\n    const checkAndAddEmptyRows = (currentIndex)=>{\n        const currentRows = gridRows;\n        const nextEmptyIndex = currentRows.findIndex((row, index)=>index > currentIndex && row.type === 'empty');\n        // إذا لم توجد صفوف فارغة بعد الفاصل الحالي\n        if (nextEmptyIndex === -1) {\n            console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 8 صفوف');\n            addMultipleEmptyRows(currentIndex, 8);\n        } else {\n            console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);\n        }\n    };\n    // حذف صف فارغ\n    const deleteRow = (rowId)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        // استعادة موضع التمرير بعد الحذف\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 100);\n    };\n    // حذف سيجمنت مع حماية المواد الأساسية\n    const deleteSegment = (rowId)=>{\n        const row = gridRows.find((r)=>r.id === rowId);\n        // حماية مضاعفة للمواد الأساسية\n        if (row && row.type === 'segment' && (!row.isTemporary || !row.canDelete)) {\n            alert('⚠️ لا يمكن حذف المواد الأساسية من هنا!\\n\\nهذه المادة مستوردة من الخريطة البرامجية.\\n\\nلتغيير هذه المادة:\\n1. اذهب للخريطة البرامجية\\n2. قم بتعديل المادة هناك\\n3. ستتحدث تلقائياً في الجدول اليومي');\n            return;\n        }\n        // التأكد من أن المادة قابلة للحذف\n        if (row && !row.canDelete) {\n            alert('⚠️ هذه المادة محمية من الحذف!');\n            return;\n        }\n        if (confirm(t('schedule.confirmDeleteSegment'))) {\n            const newRows = gridRows.filter((row)=>row.id !== rowId);\n            recalculateTimes(newRows);\n            console.log('🗑️ تم حذف السيجمنت');\n        }\n    };\n    // حذف فاصل بدون تأكيد\n    const deleteFiller = (rowId)=>{\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        console.log('🗑️ تم حذف الفاصل');\n    };\n    // تحريك صف لأعلى\n    const moveRowUp = (index)=>{\n        if (index <= 0) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index - 1], newRows[index]] = [\n            newRows[index],\n            newRows[index - 1]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬆️ تم تحريك الصف لأعلى');\n    };\n    // تحريك صف لأسفل\n    const moveRowDown = (index)=>{\n        if (index >= gridRows.length - 1) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index], newRows[index + 1]] = [\n            newRows[index + 1],\n            newRows[index]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬇️ تم تحريك الصف لأسفل');\n    };\n    // معالجة سحب الصفوف داخل الجدول\n    const handleRowDragStart = (e, index)=>{\n        e.dataTransfer.setData('text/plain', index.toString());\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    // معالجة إسقاط الصفوف داخل الجدول\n    const handleRowDrop = (e, targetIndex)=>{\n        e.preventDefault();\n        const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));\n        if (sourceIndex === targetIndex) return;\n        const newRows = [\n            ...gridRows\n        ];\n        const [movedRow] = newRows.splice(sourceIndex, 1);\n        newRows.splice(targetIndex, 0, movedRow);\n        recalculateTimes(newRows);\n        console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);\n        // تثبيت الجدول بعد السحب\n        setTimeout(()=>{\n            const gridElement = document.querySelector('.schedule-grid');\n            if (gridElement) {\n                gridElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'start'\n                });\n            }\n        }, 100);\n    };\n    // معالجة إسقاط المواد\n    const handleDrop = (e, rowIndex)=>{\n        e.preventDefault();\n        // حفظ موضع التمرير الحالي قبل أي تغيير\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const rowElement = e.currentTarget.closest('.grid-row');\n        const rowOffsetTop = (rowElement === null || rowElement === void 0 ? void 0 : rowElement.getBoundingClientRect().top) || 0;\n        try {\n            var _mediaData_segments, _mediaData_segments1;\n            // محاولة الحصول على البيانات بطرق مختلفة\n            let mediaData;\n            const jsonData = e.dataTransfer.getData('application/json');\n            const textData = e.dataTransfer.getData('text/plain');\n            console.log('📥 البيانات المسحوبة:', {\n                jsonData,\n                textData\n            });\n            // التحقق من أن هذا ليس سحب صف داخلي\n            if (textData && !jsonData && /^\\d+$/.test(textData)) {\n                console.log('🔄 هذا سحب صف داخلي، تجاهل');\n                return;\n            }\n            if (jsonData) {\n                mediaData = JSON.parse(jsonData);\n            } else if (textData) {\n                try {\n                    mediaData = JSON.parse(textData);\n                } catch (e) {\n                    console.error('❌ لا يمكن تحليل البيانات المسحوبة');\n                    return;\n                }\n            } else {\n                console.error('❌ لا توجد بيانات مسحوبة');\n                return;\n            }\n            // التحقق من أن الصف فارغ\n            const targetRow = gridRows[rowIndex];\n            if (targetRow.type !== 'empty') {\n                alert('يمكن إسقاط المواد في الصفوف الفارغة فقط');\n                return;\n            }\n            console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);\n            // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر\n            if (!mediaData || typeof mediaData !== 'object' || typeof mediaData === 'string' || Array.isArray(mediaData)) {\n                console.error('❌ بيانات المادة غير صحيحة:', mediaData);\n                console.error('❌ نوع البيانات:', typeof mediaData);\n                console.error('❌ هل هو مصفوفة؟', Array.isArray(mediaData));\n                return;\n            }\n            // التأكد من وجود الاسم\n            const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';\n            const itemType = mediaData.type || 'UNKNOWN';\n            const itemId = mediaData.id || Date.now().toString();\n            console.log('📥 معلومات المادة:', {\n                name: itemName,\n                type: itemType,\n                id: itemId,\n                segments: ((_mediaData_segments = mediaData.segments) === null || _mediaData_segments === void 0 ? void 0 : _mediaData_segments.length) || 0\n            });\n            // تحديد نوع المادة المسحوبة\n            let dragItemType = 'filler';\n            let itemContent = itemName;\n            // إضافة تفاصيل المادة\n            const details = [];\n            if (mediaData.episodeNumber) details.push(\"ح\".concat(mediaData.episodeNumber));\n            if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(\"م\".concat(mediaData.seasonNumber));\n            if (mediaData.partNumber) details.push(\"ج\".concat(mediaData.partNumber));\n            const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n            // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات\n            if ([\n                'PROMO',\n                'STING',\n                'FILLER',\n                'FILL_IN'\n            ].includes(itemType)) {\n                dragItemType = 'filler';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                // تخزين نوع المادة لاستخدامه في تمييز الألوان\n                mediaData.mediaType = itemType;\n            } else {\n                dragItemType = 'segment';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" (مادة إضافية)\");\n            }\n            // حساب المدة الحقيقية للمادة\n            let itemDuration = '00:01:00'; // مدة افتراضية\n            console.log('🔍 تحليل مدة المادة:', {\n                name: itemName,\n                hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),\n                segmentsCount: ((_mediaData_segments1 = mediaData.segments) === null || _mediaData_segments1 === void 0 ? void 0 : _mediaData_segments1.length) || 0,\n                hasDuration: !!mediaData.duration,\n                directDuration: mediaData.duration\n            });\n            if (mediaData.segments && mediaData.segments.length > 0) {\n                // حساب إجمالي مدة جميع السيجمنتات\n                let totalSeconds = 0;\n                mediaData.segments.forEach((segment, index)=>{\n                    if (segment.duration) {\n                        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                        const segmentSeconds = hours * 3600 + minutes * 60 + seconds;\n                        totalSeconds += segmentSeconds;\n                        console.log(\"  \\uD83D\\uDCFA سيجمنت \".concat(index + 1, \": \").concat(segment.duration, \" (\").concat(segmentSeconds, \" ثانية)\"));\n                    }\n                });\n                if (totalSeconds > 0) {\n                    const hours = Math.floor(totalSeconds / 3600);\n                    const minutes = Math.floor(totalSeconds % 3600 / 60);\n                    const secs = totalSeconds % 60;\n                    itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                }\n                console.log('📊 حساب مدة المادة من السيجمنتات:', {\n                    name: itemName,\n                    segments: mediaData.segments.length,\n                    totalSeconds,\n                    finalDuration: itemDuration\n                });\n            } else if (mediaData.duration) {\n                // استخدام المدة المباشرة إذا كانت موجودة\n                itemDuration = mediaData.duration;\n                console.log('📊 استخدام مدة مباشرة:', itemDuration);\n            } else {\n                console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);\n            }\n            // إنشاء كود للمادة المسحوبة\n            let itemCode = '';\n            if (mediaData.segmentCode) {\n                itemCode = mediaData.segmentCode;\n            } else if (mediaData.id) {\n                itemCode = \"\".concat(itemType, \"_\").concat(mediaData.id);\n            } else {\n                itemCode = \"\".concat(itemType, \"_\").concat(Date.now().toString().slice(-6));\n            }\n            // إنشاء صف جديد مع المدة الحقيقية\n            const newRow = {\n                id: \"dropped_\".concat(Date.now()),\n                type: dragItemType,\n                content: itemContent,\n                mediaItemId: itemId,\n                segmentCode: itemCode,\n                duration: itemDuration,\n                canDelete: true\n            };\n            // إضافة نوع المادة للفواصل لتمييزها بألوان مختلفة\n            if (dragItemType === 'filler' && [\n                'PROMO',\n                'STING',\n                'FILL_IN',\n                'FILLER'\n            ].includes(itemType)) {\n                newRow.mediaType = itemType;\n                console.log(\"\\uD83C\\uDFA8 تم تعيين نوع المادة: \".concat(itemType, \" للصف الجديد\"));\n            }\n            // التأكد من أن الصف المستهدف فارغ\n            if (gridRows[rowIndex].type !== 'empty') {\n                console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);\n                return;\n            }\n            // استبدال الصف الفارغ مباشرة\n            const newRows = [\n                ...gridRows\n            ];\n            newRows[rowIndex] = newRow;\n            console.log('✅ تم إضافة المادة:', {\n                name: itemName,\n                type: dragItemType,\n                duration: itemDuration,\n                position: rowIndex,\n                content: itemContent,\n                beforeType: gridRows[rowIndex].type,\n                afterType: newRow.type\n            });\n            // تحديث الصفوف مباشرة\n            setGridRows(newRows);\n            // إعادة حساب الأوقات بعد تأخير قصير\n            setTimeout(()=>{\n                recalculateTimes(newRows);\n                // التحقق من الحاجة لإضافة صفوف فارغة\n                checkAndAddEmptyRows(rowIndex);\n                // تثبيت الجدول بعد إسقاط المادة\n                setTimeout(()=>{\n                    const gridElement = document.querySelector('.schedule-grid');\n                    if (gridElement) {\n                        gridElement.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'start'\n                        });\n                        console.log('📍 تم تثبيت الجدول بعد إسقاط المادة');\n                    }\n                }, 100);\n            }, 50);\n        } catch (error) {\n            console.error('❌ خطأ في إسقاط المادة:', error);\n            // في حالة حدوث خطأ، نستعيد موضع التمرير على أي حال\n            setTimeout(()=>{\n                if (gridBody) {\n                    gridBody.scrollTop = currentScrollTop;\n                }\n            }, 100);\n        }\n    };\n    // إعادة حساب الأوقات مثل Excel\n    const recalculateTimes = (rows)=>{\n        const newRows = [\n            ...rows\n        ];\n        let currentTime = '08:00:00'; // نقطة البداية بالثواني\n        let hasFillers = false; // هل تم إضافة فواصل؟\n        // التحقق من وجود فواصل\n        hasFillers = rows.some((row)=>row.type === 'filler');\n        console.log('🔄 بدء إعادة حساب الأوقات من 08:00:00', hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');\n        for(let i = 0; i < newRows.length; i++){\n            const row = newRows[i];\n            if (row.type === 'segment' || row.type === 'filler') {\n                // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل\n                if (i === 0 || hasFillers) {\n                    newRows[i] = {\n                        ...row,\n                        time: currentTime\n                    };\n                } else {\n                    newRows[i] = {\n                        ...row,\n                        time: undefined\n                    };\n                }\n                if (row.duration) {\n                    // حساب الوقت التالي بناءً على المدة\n                    const nextTime = calculateNextTime(currentTime, row.duration);\n                    console.log(\"⏰ \".concat(row.type, ': \"').concat(row.content, '\" - من ').concat(currentTime, \" إلى \").concat(nextTime, \" (مدة: \").concat(row.duration, \")\"));\n                    currentTime = nextTime;\n                }\n            } else if (row.type === 'empty') {\n                // الصفوف الفارغة لا تؤثر على الوقت\n                newRows[i] = {\n                    ...row,\n                    time: undefined\n                };\n            }\n            // التحقق من الوصول لوقت مادة أساسية\n            if (row.originalStartTime && hasFillers) {\n                const targetMinutes = timeToMinutes(row.originalStartTime);\n                const currentMinutes = timeToMinutes(currentTime);\n                const difference = targetMinutes - currentMinutes;\n                if (Math.abs(difference) > 5) {\n                    console.log('⚠️ انحراف زمني: المادة \"'.concat(row.content, '\" مجدولة في ').concat(row.originalStartTime, \" لكن ستدخل في \").concat(currentTime, \" (فرق: \").concat(difference, \" دقيقة)\"));\n                } else {\n                    console.log('✅ توقيت صحيح: المادة \"'.concat(row.content, '\" ستدخل في ').concat(currentTime, \" (مجدولة: \").concat(row.originalStartTime, \")\"));\n                }\n            }\n        }\n        console.log(\"\\uD83C\\uDFC1 انتهاء الحساب - الوقت النهائي: \".concat(currentTime));\n        // حفظ موضع التمرير قبل التحديث\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        // تحديث الصفوف دائماً لضمان التحديث الصحيح\n        setGridRows(newRows);\n        // استعادة موضع التمرير بعد التحديث\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // تحويل الوقت إلى دقائق\n    const timeToMinutes = (time)=>{\n        const [hours, minutes] = time.split(':').map(Number);\n        return hours * 60 + minutes;\n    };\n    // حساب المدة الإجمالية للمادة بدقة\n    const calculateTotalDuration = (item)=>{\n        if (item.segments && item.segments.length > 0) {\n            let totalSeconds = 0;\n            item.segments.forEach((segment)=>{\n                if (segment.duration) {\n                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                }\n            });\n            if (totalSeconds > 0) {\n                const hours = Math.floor(totalSeconds / 3600);\n                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                const secs = totalSeconds % 60;\n                return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n            }\n        }\n        return item.duration || '00:01:00';\n    };\n    // حفظ تعديلات الجدول\n    const saveScheduleChanges = async ()=>{\n        try {\n            console.log('💾 بدء حفظ التعديلات...');\n            console.log('📅 التاريخ:', selectedDate);\n            console.log('📝 عدد الصفوف:', gridRows.length);\n            const response = await fetch('/api/daily-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    date: selectedDate,\n                    scheduleRows: gridRows\n                })\n            });\n            const result = await response.json();\n            if (response.ok && result.success) {\n                console.log('✅ تم حفظ تعديلات الجدول الإذاعي بنجاح');\n                alert('✅ تم حفظ التعديلات بنجاح!');\n            } else {\n                console.error('❌ فشل في حفظ التعديلات:', result.error);\n                alert('❌ فشل في حفظ التعديلات: ' + result.error);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ التعديلات:', error);\n            alert('❌ خطأ في الاتصال بالخادم');\n        }\n    };\n    // تصدير الجدول الإذاعي إلى Excel\n    const exportDailySchedule = async ()=>{\n        try {\n            console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');\n            if (!selectedDate) {\n                alert('يرجى تحديد التاريخ أولاً');\n                return;\n            }\n            // حفظ البيانات الحالية أولاً\n            console.log('💾 حفظ البيانات الحالية...');\n            await saveScheduleChanges();\n            // إرسال البيانات الحالية مع طلب التصدير (تجاهل المواد المؤقتة)\n            const currentData = {\n                date: selectedDate,\n                scheduleRows: gridRows.filter((row)=>(row.type === 'segment' || row.type === 'filler' && row.content) && !row.isTemporary)\n            };\n            const response = await fetch(\"/api/export-daily-schedule-new?date=\".concat(selectedDate), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(currentData)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"Daily_Schedule_\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('✅ تم تصدير الجدول الإذاعي بنجاح');\n            alert('✅ تم تصدير الجدول بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في تصدير الجدول:', error);\n            alert('❌ فشل في تصدير الجدول: ' + error.message);\n        }\n    };\n    // حساب الوقت التالي بناءً على المدة (بدقة الثواني)\n    const calculateNextTime = (startTime, duration)=>{\n        // تحليل وقت البداية\n        const startParts = startTime.split(':');\n        const startHours = parseInt(startParts[0]);\n        const startMins = parseInt(startParts[1]);\n        const startSecs = parseInt(startParts[2] || '0');\n        // تحليل المدة\n        const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n        // حساب إجمالي الثواني\n        let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;\n        totalSeconds += durHours * 3600 + durMins * 60 + durSecs;\n        // تحويل إلى ساعات ودقائق وثواني\n        const hours = Math.floor(totalSeconds / 3600) % 24;\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            title: t('schedule.daily'),\n            subtitle: t('schedule.scheduledMedia'),\n            icon: \"\\uD83D\\uDCCA\",\n            fullWidth: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"1b542d50d800f28e\",\n                    children: \".grid-row.empty.jsx-1b542d50d800f28e{background:#b8dce8!important;background-color:#b8dce8!important;min-height:50px!important;height:50px!important;border-color:#a0c4d4!important}.grid-row.empty.jsx-1b542d50d800f28e .action-btn.jsx-1b542d50d800f28e{font-size:.75rem!important;padding:5px 8px!important;min-width:30px!important;height:30px!important;line-height:1.2!important;-webkit-border-radius:4px!important;-moz-border-radius:4px!important;border-radius:4px!important;margin:2px!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important;overflow:hidden!important;white-space:nowrap!important}.grid-row.empty.jsx-1b542d50d800f28e .actions-cell.jsx-1b542d50d800f28e{padding:8px!important;gap:5px!important;display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important;-webkit-box-align:center!important;-webkit-align-items:center!important;-moz-box-align:center!important;-ms-flex-align:center!important;align-items:center!important;-webkit-box-pack:center!important;-webkit-justify-content:center!important;-moz-box-pack:center!important;-ms-flex-pack:center!important;justify-content:center!important;height:50px!important;max-height:50px!important;overflow:hidden!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important}\"\n                }, void 0, false, void 0, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#b8dce8',\n                        minHeight: '100vh',\n                        margin: '-2rem',\n                        padding: '2rem'\n                    },\n                    className: \"jsx-1b542d50d800f28e\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-controls\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"date-selector\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"schedule-date\",\n                                            className: \"jsx-1b542d50d800f28e\",\n                                            children: [\n                                                t('common.selectDate'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"schedule-date\",\n                                            type: \"date\",\n                                            value: selectedDate,\n                                            onChange: (e)=>setSelectedDate(e.target.value),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1180,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"header-buttons\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: saveScheduleChanges,\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button primary\",\n                                            children: [\n                                                \"\\uD83D\\uDCBE \",\n                                                t('common.save')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1190,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: exportDailySchedule,\n                                            style: {\n                                                background: 'linear-gradient(45deg, #17a2b8, #138496)',\n                                                color: 'white'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button export\",\n                                            children: [\n                                                \"\\uD83D\\uDCCA \",\n                                                t('reports.exportExcel')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.href = '/daily-schedule/import',\n                                            style: {\n                                                background: 'linear-gradient(45deg, #8e44ad, #9b59b6)',\n                                                color: 'white'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button\",\n                                            children: \"\\uD83D\\uDCE5 Import List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1208,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowWeeklySchedule(!showWeeklySchedule),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button\",\n                                            children: showWeeklySchedule ? \"\\uD83D\\uDCCB \".concat(t('schedule.hideSchedule')) : \"\\uD83D\\uDCC5 \".concat(t('schedule.showSchedule'))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1219,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 1177,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-content\",\n                            children: [\n                                showWeeklySchedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: t('schedule.weeklySchedule')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1234,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-schedule-list\",\n                                            children: Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? weeklySchedule.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-time\",\n                                                            children: item.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-name\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1241,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ح\",\n                                                                        item.episodeNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1243,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ج\",\n                                                                        item.partNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1246,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1240,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-status\",\n                                                            children: item.isRerun ? '🔄' : '🎯'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1249,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1238,\n                                                    columnNumber: 19\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"no-data\",\n                                                children: t('schedule.noWeeklyData')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 1255,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1235,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1233,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: \"المواد المتاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-controls\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filterType,\n                                                    onChange: (e)=>setFilterType(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"filter-select\",\n                                                    children: mediaTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type,\n                                                            className: \"jsx-1b542d50d800f28e\",\n                                                            children: getTypeLabel(type)\n                                                        }, type, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1273,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1267,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث في المواد...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"search-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1279,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1266,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-header\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                            children: \"الاسم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1292,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1293,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                            children: \"المدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1294,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                            children: \"الكود\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1295,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-body\",\n                                                    children: filteredMedia.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: (e)=>{\n                                                                e.dataTransfer.setData('application/json', JSON.stringify(item));\n                                                                // إضافة class للتصغير أثناء السحب\n                                                                e.currentTarget.classList.add('dragging');\n                                                            },\n                                                            onDragEnd: (e)=>{\n                                                                // إزالة class بعد انتهاء السحب\n                                                                e.currentTarget.classList.remove('dragging');\n                                                            },\n                                                            \"data-type\": item.type,\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-row \".concat(item.type.toLowerCase()),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    title: \"\".concat(item.name, \" - \").concat(getTypeLabel(item.type)),\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-name-text\",\n                                                                            children: item.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1317,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-tags\",\n                                                                            children: [\n                                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"episode-tag\",\n                                                                                    children: [\n                                                                                        \"ح\",\n                                                                                        item.episodeNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1319,\n                                                                                    columnNumber: 46\n                                                                                }, this),\n                                                                                item.seasonNumber && item.seasonNumber > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"season-tag\",\n                                                                                    children: [\n                                                                                        \"م\",\n                                                                                        item.seasonNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1320,\n                                                                                    columnNumber: 70\n                                                                                }, this),\n                                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"part-tag\",\n                                                                                    children: [\n                                                                                        \"ج\",\n                                                                                        item.partNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1321,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1318,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1316,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                                    children: getTypeLabel(item.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1324,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                                    children: calculateTotalDuration(item)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1325,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                                    children: item.segmentCode || item.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1326,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, item.id, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1301,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1299,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1289,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1262,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-grid\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-header\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-column\",\n                                                    children: \"الكود\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1336,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-column\",\n                                                    children: \"الوقت\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1337,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-column\",\n                                                    children: \"المحتوى\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1338,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-column\",\n                                                    children: \"المدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1339,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-column\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1340,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-column\",\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1341,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1335,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-body\",\n                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"loading\",\n                                                children: t('common.loading')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 1346,\n                                                columnNumber: 15\n                                            }, this) : gridRows.map((row, index)=>{\n                                                var _row_content, _row_content1, _row_content2, _row_content3;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    draggable: row.type === 'filler' || row.type === 'empty' || row.type === 'segment' && row.canDelete,\n                                                    onDragStart: (e)=>handleRowDragStart(e, index),\n                                                    onDrop: (e)=>{\n                                                        handleRowDrop(e, index);\n                                                        // إزالة تأثير drag-over\n                                                        e.currentTarget.classList.remove('drag-over');\n                                                    },\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        // إضافة تأثير بصري عند السحب فوق الصف الفارغ\n                                                        if (row.type === 'empty') {\n                                                            e.currentTarget.classList.add('drag-over');\n                                                        }\n                                                    },\n                                                    onDragLeave: (e)=>{\n                                                        // إزالة تأثير drag-over عند مغادرة الصف\n                                                        e.currentTarget.classList.remove('drag-over');\n                                                    },\n                                                    \"data-type\": // استخدام الحقل الإضافي mediaType إن وجد\n                                                    row.mediaType ? row.mediaType : ((_row_content = row.content) === null || _row_content === void 0 ? void 0 : _row_content.includes('PROMO')) ? 'PROMO' : ((_row_content1 = row.content) === null || _row_content1 === void 0 ? void 0 : _row_content1.includes('STING')) ? 'STING' : ((_row_content2 = row.content) === null || _row_content2 === void 0 ? void 0 : _row_content2.includes('FILL_IN')) ? 'FILL_IN' : ((_row_content3 = row.content) === null || _row_content3 === void 0 ? void 0 : _row_content3.includes('FILLER')) ? 'FILLER' : '',\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-row \".concat(row.type, \" \").concat(row.type === 'segment' ? row.isRerun ? 'rerun-content' : row.isTemporary ? 'temp-content' : 'primary-content' : row.type === 'filler' ? row.mediaType === 'PROMO' ? 'promo-content' : row.mediaType === 'STING' ? 'sting-content' : row.mediaType === 'FILLER' ? 'filler-content' : row.mediaType === 'FILL_IN' ? 'filler-content' : 'break-content' : 'primary-content'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-cell\",\n                                                            children: row.type === 'segment' || row.type === 'filler' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-code\",\n                                                                children: row.segmentCode || \"\".concat(row.type.toUpperCase(), \"_\").concat(row.id.slice(-6))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1395,\n                                                                columnNumber: 23\n                                                            }, this) : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1393,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-cell\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-time\",\n                                                                children: row.time || ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1401,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1400,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onDrop: (e)=>handleDrop(e, index),\n                                                            onDragOver: (e)=>e.preventDefault(),\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-cell\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-text\",\n                                                                children: row.content || ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1410,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1405,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-cell\",\n                                                            children: row.duration || ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1414,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-cell\",\n                                                            children: [\n                                                                row.type === 'segment' && row.isTemporary && '🟣 مؤقت',\n                                                                row.type === 'segment' && !row.isRerun && !row.isTemporary && (row.originalStartTime ? Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ? '⚠️ انحراف' : '✅ دقيق' : '🎯 أساسي'),\n                                                                row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة',\n                                                                row.type === 'filler' && '📺 فاصل',\n                                                                row.type === 'empty' && '⚪ فارغ'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-cell\",\n                                                            children: [\n                                                                row.type === 'empty' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"إضافة صف\",\n                                                                            onClick: ()=>addEmptyRow(index),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-row\",\n                                                                            children: \"➕\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1432,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"إضافة 8 صفوف\",\n                                                                            onClick: ()=>addMultipleEmptyRows(index, 8),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-multiple-rows\",\n                                                                            children: \"➕➕\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1439,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        row.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"حذف صف\",\n                                                                            onClick: ()=>deleteRow(row.id),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                            children: \"➖\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1447,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                row.type === 'filler' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"تحريك لأعلى\",\n                                                                            onClick: ()=>moveRowUp(index),\n                                                                            disabled: index === 0,\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-up\",\n                                                                            children: \"⬆️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1459,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"تحريك لأسفل\",\n                                                                            onClick: ()=>moveRowDown(index),\n                                                                            disabled: index === gridRows.length - 1,\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-down\",\n                                                                            children: \"⬇️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1467,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"حذف فاصل\",\n                                                                            onClick: ()=>{\n                                                                                // تحويل الفاصل إلى صف فارغ\n                                                                                const newRows = [\n                                                                                    ...gridRows\n                                                                                ];\n                                                                                newRows[index] = {\n                                                                                    id: \"empty_\".concat(Date.now()),\n                                                                                    type: 'empty',\n                                                                                    canDelete: true\n                                                                                };\n                                                                                // إعادة حساب الأوقات\n                                                                                recalculateTimes(newRows);\n                                                                            },\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                            children: \"\\uD83D\\uDDD1️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1475,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                row.type === 'segment' && row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    title: \"استبدال بمادة حقيقية\",\n                                                                    onClick: ()=>{\n                                                                        alert('💡 لاستبدال المادة المؤقتة:\\n\\n1. أضف المادة الحقيقية لقاعدة البيانات\\n2. احذف المادة المؤقتة\\n3. اسحب المادة الجديدة من القائمة الجانبية');\n                                                                    },\n                                                                    style: {\n                                                                        color: '#9c27b0'\n                                                                    },\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn replace-temp\",\n                                                                    children: \"\\uD83D\\uDD04\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1496,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                row.type === 'segment' && row.canDelete && !row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    title: \"حذف سيجمنت\",\n                                                                    onClick: ()=>deleteSegment(row.id),\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                    children: \"❌\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1508,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1429,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, row.id, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 17\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1344,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1334,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 1230,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 1174,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n            lineNumber: 1141,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n        lineNumber: 1140,\n        columnNumber: 5\n    }, this);\n}\n_s(DailySchedulePage, \"M66pQOFP557tt+EdowkwW2jKQZQ=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_5__.useAppTranslation\n    ];\n});\n_c = DailySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"DailySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/page.tsx\n"));

/***/ })

});