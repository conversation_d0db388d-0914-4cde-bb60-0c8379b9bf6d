/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fs.realpath";
exports.ids = ["vendor-chunks/fs.realpath"];
exports.modules = {

/***/ "(ssr)/./node_modules/fs.realpath/index.js":
/*!*******************************************!*\
  !*** ./node_modules/fs.realpath/index.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = realpath\nrealpath.realpath = realpath\nrealpath.sync = realpathSync\nrealpath.realpathSync = realpathSync\nrealpath.monkeypatch = monkeypatch\nrealpath.unmonkeypatch = unmonkeypatch\n\nvar fs = __webpack_require__(/*! fs */ \"fs\")\nvar origRealpath = fs.realpath\nvar origRealpathSync = fs.realpathSync\n\nvar version = process.version\nvar ok = /^v[0-5]\\./.test(version)\nvar old = __webpack_require__(/*! ./old.js */ \"(ssr)/./node_modules/fs.realpath/old.js\")\n\nfunction newError (er) {\n  return er && er.syscall === 'realpath' && (\n    er.code === 'ELOOP' ||\n    er.code === 'ENOMEM' ||\n    er.code === 'ENAMETOOLONG'\n  )\n}\n\nfunction realpath (p, cache, cb) {\n  if (ok) {\n    return origRealpath(p, cache, cb)\n  }\n\n  if (typeof cache === 'function') {\n    cb = cache\n    cache = null\n  }\n  origRealpath(p, cache, function (er, result) {\n    if (newError(er)) {\n      old.realpath(p, cache, cb)\n    } else {\n      cb(er, result)\n    }\n  })\n}\n\nfunction realpathSync (p, cache) {\n  if (ok) {\n    return origRealpathSync(p, cache)\n  }\n\n  try {\n    return origRealpathSync(p, cache)\n  } catch (er) {\n    if (newError(er)) {\n      return old.realpathSync(p, cache)\n    } else {\n      throw er\n    }\n  }\n}\n\nfunction monkeypatch () {\n  fs.realpath = realpath\n  fs.realpathSync = realpathSync\n}\n\nfunction unmonkeypatch () {\n  fs.realpath = origRealpath\n  fs.realpathSync = origRealpathSync\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fs.realpath/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fs.realpath/old.js":
/*!*****************************************!*\
  !*** ./node_modules/fs.realpath/old.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nvar pathModule = __webpack_require__(/*! path */ \"path\");\nvar isWindows = process.platform === 'win32';\nvar fs = __webpack_require__(/*! fs */ \"fs\");\n\n// JavaScript implementation of realpath, ported from node pre-v6\n\nvar DEBUG = process.env.NODE_DEBUG && /fs/.test(process.env.NODE_DEBUG);\n\nfunction rethrow() {\n  // Only enable in debug mode. A backtrace uses ~1000 bytes of heap space and\n  // is fairly slow to generate.\n  var callback;\n  if (DEBUG) {\n    var backtrace = new Error;\n    callback = debugCallback;\n  } else\n    callback = missingCallback;\n\n  return callback;\n\n  function debugCallback(err) {\n    if (err) {\n      backtrace.message = err.message;\n      err = backtrace;\n      missingCallback(err);\n    }\n  }\n\n  function missingCallback(err) {\n    if (err) {\n      if (process.throwDeprecation)\n        throw err;  // Forgot a callback but don't know where? Use NODE_DEBUG=fs\n      else if (!process.noDeprecation) {\n        var msg = 'fs: missing callback ' + (err.stack || err.message);\n        if (process.traceDeprecation)\n          console.trace(msg);\n        else\n          console.error(msg);\n      }\n    }\n  }\n}\n\nfunction maybeCallback(cb) {\n  return typeof cb === 'function' ? cb : rethrow();\n}\n\nvar normalize = pathModule.normalize;\n\n// Regexp that finds the next partion of a (partial) path\n// result is [base_with_slash, base], e.g. ['somedir/', 'somedir']\nif (isWindows) {\n  var nextPartRe = /(.*?)(?:[\\/\\\\]+|$)/g;\n} else {\n  var nextPartRe = /(.*?)(?:[\\/]+|$)/g;\n}\n\n// Regex to find the device root, including trailing slash. E.g. 'c:\\\\'.\nif (isWindows) {\n  var splitRootRe = /^(?:[a-zA-Z]:|[\\\\\\/]{2}[^\\\\\\/]+[\\\\\\/][^\\\\\\/]+)?[\\\\\\/]*/;\n} else {\n  var splitRootRe = /^[\\/]*/;\n}\n\nexports.realpathSync = function realpathSync(p, cache) {\n  // make p is absolute\n  p = pathModule.resolve(p);\n\n  if (cache && Object.prototype.hasOwnProperty.call(cache, p)) {\n    return cache[p];\n  }\n\n  var original = p,\n      seenLinks = {},\n      knownHard = {};\n\n  // current character position in p\n  var pos;\n  // the partial path so far, including a trailing slash if any\n  var current;\n  // the partial path without a trailing slash (except when pointing at a root)\n  var base;\n  // the partial path scanned in the previous round, with slash\n  var previous;\n\n  start();\n\n  function start() {\n    // Skip over roots\n    var m = splitRootRe.exec(p);\n    pos = m[0].length;\n    current = m[0];\n    base = m[0];\n    previous = '';\n\n    // On windows, check that the root exists. On unix there is no need.\n    if (isWindows && !knownHard[base]) {\n      fs.lstatSync(base);\n      knownHard[base] = true;\n    }\n  }\n\n  // walk down the path, swapping out linked pathparts for their real\n  // values\n  // NB: p.length changes.\n  while (pos < p.length) {\n    // find the next part\n    nextPartRe.lastIndex = pos;\n    var result = nextPartRe.exec(p);\n    previous = current;\n    current += result[0];\n    base = previous + result[1];\n    pos = nextPartRe.lastIndex;\n\n    // continue if not a symlink\n    if (knownHard[base] || (cache && cache[base] === base)) {\n      continue;\n    }\n\n    var resolvedLink;\n    if (cache && Object.prototype.hasOwnProperty.call(cache, base)) {\n      // some known symbolic link.  no need to stat again.\n      resolvedLink = cache[base];\n    } else {\n      var stat = fs.lstatSync(base);\n      if (!stat.isSymbolicLink()) {\n        knownHard[base] = true;\n        if (cache) cache[base] = base;\n        continue;\n      }\n\n      // read the link if it wasn't read before\n      // dev/ino always return 0 on windows, so skip the check.\n      var linkTarget = null;\n      if (!isWindows) {\n        var id = stat.dev.toString(32) + ':' + stat.ino.toString(32);\n        if (seenLinks.hasOwnProperty(id)) {\n          linkTarget = seenLinks[id];\n        }\n      }\n      if (linkTarget === null) {\n        fs.statSync(base);\n        linkTarget = fs.readlinkSync(base);\n      }\n      resolvedLink = pathModule.resolve(previous, linkTarget);\n      // track this, if given a cache.\n      if (cache) cache[base] = resolvedLink;\n      if (!isWindows) seenLinks[id] = linkTarget;\n    }\n\n    // resolve the link, then start over\n    p = pathModule.resolve(resolvedLink, p.slice(pos));\n    start();\n  }\n\n  if (cache) cache[original] = p;\n\n  return p;\n};\n\n\nexports.realpath = function realpath(p, cache, cb) {\n  if (typeof cb !== 'function') {\n    cb = maybeCallback(cache);\n    cache = null;\n  }\n\n  // make p is absolute\n  p = pathModule.resolve(p);\n\n  if (cache && Object.prototype.hasOwnProperty.call(cache, p)) {\n    return process.nextTick(cb.bind(null, null, cache[p]));\n  }\n\n  var original = p,\n      seenLinks = {},\n      knownHard = {};\n\n  // current character position in p\n  var pos;\n  // the partial path so far, including a trailing slash if any\n  var current;\n  // the partial path without a trailing slash (except when pointing at a root)\n  var base;\n  // the partial path scanned in the previous round, with slash\n  var previous;\n\n  start();\n\n  function start() {\n    // Skip over roots\n    var m = splitRootRe.exec(p);\n    pos = m[0].length;\n    current = m[0];\n    base = m[0];\n    previous = '';\n\n    // On windows, check that the root exists. On unix there is no need.\n    if (isWindows && !knownHard[base]) {\n      fs.lstat(base, function(err) {\n        if (err) return cb(err);\n        knownHard[base] = true;\n        LOOP();\n      });\n    } else {\n      process.nextTick(LOOP);\n    }\n  }\n\n  // walk down the path, swapping out linked pathparts for their real\n  // values\n  function LOOP() {\n    // stop if scanned past end of path\n    if (pos >= p.length) {\n      if (cache) cache[original] = p;\n      return cb(null, p);\n    }\n\n    // find the next part\n    nextPartRe.lastIndex = pos;\n    var result = nextPartRe.exec(p);\n    previous = current;\n    current += result[0];\n    base = previous + result[1];\n    pos = nextPartRe.lastIndex;\n\n    // continue if not a symlink\n    if (knownHard[base] || (cache && cache[base] === base)) {\n      return process.nextTick(LOOP);\n    }\n\n    if (cache && Object.prototype.hasOwnProperty.call(cache, base)) {\n      // known symbolic link.  no need to stat again.\n      return gotResolvedLink(cache[base]);\n    }\n\n    return fs.lstat(base, gotStat);\n  }\n\n  function gotStat(err, stat) {\n    if (err) return cb(err);\n\n    // if not a symlink, skip to the next path part\n    if (!stat.isSymbolicLink()) {\n      knownHard[base] = true;\n      if (cache) cache[base] = base;\n      return process.nextTick(LOOP);\n    }\n\n    // stat & read the link if not read before\n    // call gotTarget as soon as the link target is known\n    // dev/ino always return 0 on windows, so skip the check.\n    if (!isWindows) {\n      var id = stat.dev.toString(32) + ':' + stat.ino.toString(32);\n      if (seenLinks.hasOwnProperty(id)) {\n        return gotTarget(null, seenLinks[id], base);\n      }\n    }\n    fs.stat(base, function(err) {\n      if (err) return cb(err);\n\n      fs.readlink(base, function(err, target) {\n        if (!isWindows) seenLinks[id] = target;\n        gotTarget(err, target);\n      });\n    });\n  }\n\n  function gotTarget(err, target, base) {\n    if (err) return cb(err);\n\n    var resolvedLink = pathModule.resolve(previous, target);\n    if (cache) cache[base] = resolvedLink;\n    gotResolvedLink(resolvedLink);\n  }\n\n  function gotResolvedLink(resolvedLink) {\n    // resolve the link, then start over\n    p = pathModule.resolve(resolvedLink, p.slice(pos));\n    start();\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fs.realpath/old.js\n");

/***/ })

};
;