// مركز الترجمة الموحد - يضمن الترجمة الصحيحة في كلا اللغتين
export const translations = {
  ar: {
    // Navigation
    navigation: {
      dashboard: "لوحة التحكم",
      mediaList: "المواد الإعلامية", 
      addMedia: "إضافة مادة",
      weeklySchedule: "الخريطة البرامجية",
      dailySchedule: "الجدول الإذاعي اليومي",
      importSchedule: "استيراد جدول",
      statistics: "الإحصائيات",
      adminDashboard: "إدارة المستخدمين",
      reports: "تقارير البث",
      logout: "تسجيل الخروج"
    },
    
    // Common terms
    common: {
      name: "الاسم",
      type: "النوع", 
      status: "الحالة",
      actions: "الإجراءات",
      time: "الوقت",
      duration: "المدة",
      content: "المحتوى",
      code: "الكود",
      segments: "السيجمنتات"
    },
    
    // Media types - أسماء ثابتة حسب المطلوب
    mediaTypes: {
      ALL: "جميع الأنواع",
      PROGRAM: "برنامج",
      SERIES: "مسلسل", 
      FILM: "فيلم",
      SONG: "أغنية",
      PROMO: "إعلان ترويجي",
      STING: "فاصل",
      FILLER: "مادة مالئة",
      NEXT: "التالي",
      NOW: "الآن",
      MINI: "Mini", // يبقى بالإنجليزية
      CROSS: "Cross", // يبقى بالإنجليزية
      "سنعود": "سنعود", // يبقى بالعربية
      "عدنا": "عدنا" // يبقى بالعربية
    },
    
    // User roles
    roles: {
      ADMIN: "مدير النظام",
      CONTENT_MANAGER: "مدير المحتوى", 
      MEDIA_MANAGER: "مدير قاعدة البيانات",
      SCHEDULER: "مجدول البرامج",
      FULL_VIEWER: "مستخدم عرض كامل",
      DATA_ENTRY: "إدخال البيانات",
      MAP_SCHEDULER: "مدير الخريطة والجداول",
      VIEWER: "مستخدم عرض"
    },
    
    // Role descriptions
    roleDescriptions: {
      ADMIN: "صلاحيات كاملة لإدارة النظام والمستخدمين",
      CONTENT_MANAGER: "إدارة المحتوى والمواد الإعلامية",
      MEDIA_MANAGER: "إدارة قاعدة بيانات المواد",
      SCHEDULER: "إنشاء وتعديل الجداول البرامجية", 
      FULL_VIEWER: "عرض جميع البيانات والتقارير",
      DATA_ENTRY: "إدخال وتعديل البيانات الأساسية",
      MAP_SCHEDULER: "إدارة الخريطة البرامجية والجداول",
      VIEWER: "عرض البيانات الأساسية فقط"
    }
  },
  
  en: {
    // Navigation
    navigation: {
      dashboard: "Dashboard",
      mediaList: "Media List",
      addMedia: "Add Media", 
      weeklySchedule: "Weekly Schedule",
      dailySchedule: "Daily Schedule",
      importSchedule: "Import Schedule",
      statistics: "Statistics",
      adminDashboard: "User Management",
      reports: "Broadcast Reports",
      logout: "Logout"
    },
    
    // Common terms
    common: {
      name: "Name",
      type: "Type",
      status: "Status", 
      actions: "Actions",
      time: "Time",
      duration: "Duration",
      content: "Content",
      code: "Code",
      segments: "Segments"
    },
    
    // Media types - أسماء ثابتة حسب المطلوب
    mediaTypes: {
      ALL: "All Types",
      PROGRAM: "Program",
      SERIES: "Series",
      FILM: "Film", 
      SONG: "Song",
      PROMO: "Promo",
      STING: "Sting",
      FILLER: "Filler",
      NEXT: "Next",
      NOW: "Now",
      MINI: "Mini", // يبقى بالإنجليزية
      CROSS: "Cross", // يبقى بالإنجليزية
      "سنعود": "We'll Be Back", // ترجمة للإنجليزية
      "عدنا": "We're Back" // ترجمة للإنجليزية
    },
    
    // User roles
    roles: {
      ADMIN: "System Administrator",
      CONTENT_MANAGER: "Content Manager",
      MEDIA_MANAGER: "Database Manager", 
      SCHEDULER: "Program Scheduler",
      FULL_VIEWER: "Full View User",
      DATA_ENTRY: "Data Entry",
      MAP_SCHEDULER: "Map & Schedule Manager",
      VIEWER: "Viewer"
    },
    
    // Role descriptions
    roleDescriptions: {
      ADMIN: "Full system administration and user management permissions",
      CONTENT_MANAGER: "Manage content and media materials",
      MEDIA_MANAGER: "Manage media database",
      SCHEDULER: "Create and edit program schedules",
      FULL_VIEWER: "View all data and reports", 
      DATA_ENTRY: "Enter and edit basic data",
      MAP_SCHEDULER: "Manage program map and schedules",
      VIEWER: "View basic data only"
    }
  }
};

// دالة الترجمة المركزية
export const getTranslation = (key: string, language: 'ar' | 'en' = 'ar') => {
  const keys = key.split('.');
  let value: any = translations[language];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // إذا لم توجد الترجمة، ارجع المفتاح نفسه
      console.warn(`Translation missing for key: ${key} in language: ${language}`);
      return key;
    }
  }
  
  return typeof value === 'string' ? value : key;
};

// دالة مساعدة للحصول على ترجمة نوع المادة
export const getMediaTypeLabel = (type: string, language: 'ar' | 'en' = 'ar') => {
  return getTranslation(`mediaTypes.${type}`, language);
};

// دالة مساعدة للحصول على ترجمة الدور
export const getRoleLabel = (role: string, language: 'ar' | 'en' = 'ar') => {
  return getTranslation(`roles.${role}`, language);
};

// دالة مساعدة للحصول على وصف الدور
export const getRoleDescription = (role: string, language: 'ar' | 'en' = 'ar') => {
  return getTranslation(`roleDescriptions.${role}`, language);
};
