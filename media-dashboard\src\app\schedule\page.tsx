'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/Toast';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  segments: any[];
}

interface ScheduleItem {
  id: string;
  mediaItemId: string;
  mediaItem?: MediaItem;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  order: number;
}

export default function SchedulePage() {
  const router = useRouter();
  const { showToast, ToastContainer } = useToast();

  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([]);
  const [availableMedia, setAvailableMedia] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedDay, setSelectedDay] = useState<number | null>(null);
  const [newItem, setNewItem] = useState({
    mediaItemId: '',
    startTime: '18:00',
    endTime: '19:00'
  });
  const [primeTimeInfo, setPrimeTimeInfo] = useState<any>(null);

  const daysOfWeek = [
    { key: 0, label: 'الأحد' },
    { key: 1, label: 'الاثنين' },
    { key: 2, label: 'الثلاثاء' },
    { key: 3, label: 'الأربعاء' },
    { key: 4, label: 'الخميس' },
    { key: 5, label: 'الجمعة' },
    { key: 6, label: 'السبت' },
  ];

  useEffect(() => {
    fetchScheduleData();
  }, []);

  const fetchScheduleData = async () => {
    try {
      const response = await fetch('/api/schedule');
      const result = await response.json();

      if (result.success) {
        setScheduleItems(result.data);
        setAvailableMedia(result.availableMedia);
        setPrimeTimeInfo(result.primeTimeInfo);
      } else {
        showToast('فشل في جلب الخريطة البرامجية', 'error');
      }
    } catch (error) {
      console.error('Error fetching schedule:', error);
      showToast('حدث خطأ أثناء جلب البيانات', 'error');
    } finally {
      setLoading(false);
    }
  };

  const addToSchedule = async () => {
    if (!newItem.mediaItemId || selectedDay === null) {
      showToast('يرجى اختيار المادة واليوم', 'warning');
      return;
    }

    try {
      const response = await fetch('/api/schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          mediaItemId: newItem.mediaItemId,
          dayOfWeek: selectedDay,
          startTime: newItem.startTime,
          endTime: newItem.endTime
        })
      });

      const result = await response.json();

      if (result.success) {
        setScheduleItems([...scheduleItems, result.data]);
        setShowAddForm(false);
        setNewItem({ mediaItemId: '', startTime: '18:00', endTime: '19:00' });
        setSelectedDay(null);
        showToast('تم إضافة المادة للخريطة البرامجية بنجاح', 'success');
      } else {
        showToast(result.error, 'error');
      }
    } catch (error) {
      console.error('Error adding to schedule:', error);
      showToast('حدث خطأ أثناء إضافة المادة', 'error');
    }
  };

  const removeFromSchedule = async (id: string) => {
    if (!confirm('هل أنت متأكد من حذف هذه المادة من الخريطة؟')) return;

    try {
      const response = await fetch(`/api/schedule?id=${id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        setScheduleItems(scheduleItems.filter(item => item.id !== id));
        showToast('تم حذف المادة من الخريطة البرامجية', 'success');
      } else {
        showToast(result.error, 'error');
      }
    } catch (error) {
      console.error('Error removing from schedule:', error);
      showToast('حدث خطأ أثناء حذف المادة', 'error');
    }
  };

  const getItemsForDay = (dayIndex: number) => {
    return scheduleItems
      .filter(item => item.dayOfWeek === dayIndex)
      .sort((a, b) => a.startTime.localeCompare(b.startTime));
  };

  const getTypeLabel = (type: string) => {
    const types: { [key: string]: string } = {
      PROGRAM: 'برنامج',
      SERIES: 'مسلسل',
      MOVIE: 'فيلم',
      SONG: 'أغنية',
      STING: 'Sting',
      FILL_IN: 'Fill IN',
      FILLER: 'Filler',
      PROMO: 'Promo',
      MINI: 'Mini',
      CROSS: 'Cross'
    };
    return types[type] || type;
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>⏳ جاري تحميل الخريطة البرامجية...</div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto',
        background: 'white',
        borderRadius: '20px',
        padding: '40px',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '30px' }}>
          <h1 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            color: '#333',
            margin: 0,
            background: 'linear-gradient(45deg, #667eea, #764ba2)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            📅 الخريطة البرامجية الأسبوعية
          </h1>
          <button
            onClick={() => router.push('/')}
            style={{
              background: 'linear-gradient(45deg, #6c757d, #495057)',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '10px 20px',
              cursor: 'pointer',
              fontSize: '0.9rem'
            }}
          >
            🏠 العودة للرئيسية
          </button>
        </div>

        {/* نموذج إضافة مادة */}
        {showAddForm && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0,0,0,0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000
          }}>
            <div style={{
              background: 'white',
              borderRadius: '15px',
              padding: '30px',
              maxWidth: '500px',
              width: '90%',
              maxHeight: '80vh',
              overflow: 'auto'
            }}>
              <h2 style={{ color: '#2c3e50', marginBottom: '20px' }}>➕ إضافة مادة للخريطة البرامجية</h2>

              <div style={{
                background: '#e3f2fd',
                borderRadius: '8px',
                padding: '15px',
                marginBottom: '20px',
                border: '1px solid #2196f3'
              }}>
                <h4 style={{ color: '#1976d2', margin: '0 0 10px 0' }}>🎯 نظام الإعادات البسيط</h4>
                <p style={{ color: '#1976d2', margin: '0', fontSize: '0.9rem' }}>
                  • <strong>قاعدة واحدة بسيطة</strong>: أي مادة من 18:00 فما فوق تُعاد تلقائياً<br/>
                  • <strong>حساب بسيط</strong>: 18:00→00:00, 19:00→01:00, 20:00→02:00<br/>
                  • <strong>مثال</strong>: مادة 19:30 → إعادة 01:30 (نفس اليوم)<br/>
                  • <strong>واضح ومباشر</strong>: لا توجد حسابات معقدة
                </p>
              </div>

              <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#495057' }}>اختر المادة الإعلامية:</label>
                <select
                  value={newItem.mediaItemId}
                  onChange={(e) => setNewItem({...newItem, mediaItemId: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '10px',
                    border: '2px solid #e0e0e0',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl'
                  }}
                >
                  <option value="">اختر مادة إعلامية...</option>
                  {availableMedia.map(media => (
                    <option key={media.id} value={media.id}>
                      {media.name} ({getTypeLabel(media.type)})
                    </option>
                  ))}
                </select>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#495057' }}>وقت البداية:</label>
                  <input
                    type="time"
                    step="1"
                    value={newItem.startTime}
                    onChange={(e) => setNewItem({...newItem, startTime: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '1rem'
                    }}
                  />
                </div>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#495057' }}>وقت النهاية:</label>
                  <input
                    type="time"
                    step="1"
                    value={newItem.endTime}
                    onChange={(e) => setNewItem({...newItem, endTime: e.target.value})}
                    style={{
                      width: '100%',
                      padding: '10px',
                      border: '2px solid #e0e0e0',
                      borderRadius: '8px',
                      fontSize: '1rem'
                    }}
                  />
                </div>
              </div>

              <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
                <button
                  onClick={() => {
                    setShowAddForm(false);
                    setSelectedDay(null);
                    setNewItem({ mediaItemId: '', startTime: '18:00', endTime: '19:00' });
                  }}
                  style={{
                    background: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '10px 20px',
                    cursor: 'pointer'
                  }}
                >
                  إلغاء
                </button>
                <button
                  onClick={addToSchedule}
                  style={{
                    background: 'linear-gradient(45deg, #28a745, #20c997)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '10px 20px',
                    cursor: 'pointer'
                  }}
                >
                  إضافة
                </button>
              </div>
            </div>
          </div>
        )}

        <div style={{
          background: '#e3f2fd',
          borderRadius: '10px',
          padding: '20px',
          marginBottom: '30px',
          border: '2px solid #2196f3'
        }}>
          <h3 style={{ color: '#1976d2', margin: '0 0 15px 0', fontSize: '1.2rem' }}>
            📺 نظام الإعادات البسيط والواضح
          </h3>
          <div style={{
            background: 'white',
            borderRadius: '8px',
            padding: '15px',
            border: '1px solid #90caf9'
          }}>
            <h4 style={{ color: '#1976d2', margin: '0 0 10px 0' }}>🎯 القاعدة البسيطة:</h4>
            <p style={{ color: '#1976d2', margin: '0 0 15px 0', fontSize: '1rem', fontWeight: 'bold' }}>
              أي مادة تبدأ من <span style={{background: '#ffeb3b', padding: '2px 6px', borderRadius: '4px', color: '#333'}}>18:00</span> فما فوق → تُعاد تلقائياً في نفس اليوم من <span style={{background: '#4caf50', padding: '2px 6px', borderRadius: '4px', color: 'white'}}>00:00</span>
            </p>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '10px', fontSize: '0.9rem' }}>
              <div style={{ textAlign: 'center', padding: '8px', background: '#f5f5f5', borderRadius: '6px' }}>
                <strong>18:00</strong> → <strong style={{color: '#4caf50'}}>00:00</strong>
              </div>
              <div style={{ textAlign: 'center', padding: '8px', background: '#f5f5f5', borderRadius: '6px' }}>
                <strong>19:00</strong> → <strong style={{color: '#4caf50'}}>01:00</strong>
              </div>
              <div style={{ textAlign: 'center', padding: '8px', background: '#f5f5f5', borderRadius: '6px' }}>
                <strong>20:00</strong> → <strong style={{color: '#4caf50'}}>02:00</strong>
              </div>
            </div>
          </div>
        </div>

        {/* عرض جدولي للخريطة البرامجية */}
        <div style={{
          background: 'rgba(255,255,255,0.95)',
          borderRadius: '20px',
          padding: '25px',
          marginBottom: '25px',
          boxShadow: '0 8px 25px rgba(0,0,0,0.1)',
          overflow: 'auto'
        }}>
          <h2 style={{ color: '#2c3e50', marginBottom: '20px', fontSize: '1.3rem' }}>
            📅 الخريطة البرامجية الأسبوعية - عرض جدولي
          </h2>

          <table style={{
            width: '100%',
            borderCollapse: 'collapse',
            fontSize: '0.9rem',
            direction: 'rtl'
          }}>
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{ padding: '12px', border: '1px solid #dee2e6', fontWeight: 'bold' }}>اليوم</th>
                <th style={{ padding: '12px', border: '1px solid #dee2e6', fontWeight: 'bold' }}>البرايم تايم</th>
                <th style={{ padding: '12px', border: '1px solid #dee2e6', fontWeight: 'bold' }}>المواد المجدولة</th>
                <th style={{ padding: '12px', border: '1px solid #dee2e6', fontWeight: 'bold' }}>الإعادات</th>
                <th style={{ padding: '12px', border: '1px solid #dee2e6', fontWeight: 'bold' }}>إجراءات</th>
              </tr>
            </thead>
            <tbody>
              {daysOfWeek.map((day) => {
                const dayItems = getItemsForDay(day.key);
                const originalItems = dayItems.filter(item => !item.isRerun);
                const rerunItems = dayItems.filter(item => item.isRerun);

                return (
                  <tr key={day.key} style={{ borderBottom: '1px solid #dee2e6' }}>
                    <td style={{
                      padding: '15px',
                      border: '1px solid #dee2e6',
                      fontWeight: 'bold',
                      background: '#f8f9fa'
                    }}>
                      {day.label}
                    </td>
                    <td style={{ padding: '15px', border: '1px solid #dee2e6', textAlign: 'center' }}>
                      <span style={{
                        background: '#4caf50',
                        color: 'white',
                        padding: '4px 8px',
                        borderRadius: '12px',
                        fontSize: '0.8rem'
                      }}>
                        18:00-00:00
                      </span>
                      <div style={{ fontSize: '0.7rem', color: '#666', marginTop: '2px' }}>
                        إعادة: 00:00-08:00
                      </div>
                    </td>
                    <td style={{ padding: '15px', border: '1px solid #dee2e6' }}>
                      {originalItems.length === 0 ? (
                        <div style={{ textAlign: 'center', color: '#6c757d', fontStyle: 'italic' }}>
                          لا توجد مواد مجدولة
                        </div>
                      ) : (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                          {originalItems.map((item) => (
                            <div key={item.id} style={{
                              background: '#e3f2fd',
                              borderRadius: '8px',
                              padding: '8px',
                              border: '1px solid #90caf9',
                              display: 'flex',
                              justifyContent: 'space-between',
                              alignItems: 'center'
                            }}>
                              <div>
                                <strong>{item.mediaItem?.name || 'مادة محذوفة'}</strong>
                                <div style={{ fontSize: '0.8rem', color: '#1565c0' }}>
                                  {item.startTime} - {item.endTime} • {getTypeLabel(item.mediaItem?.type || '')}
                                </div>
                              </div>
                              <button
                                onClick={() => removeFromSchedule(item.id)}
                                style={{
                                  background: '#dc3545',
                                  color: 'white',
                                  border: 'none',
                                  borderRadius: '4px',
                                  padding: '4px 8px',
                                  cursor: 'pointer',
                                  fontSize: '0.7rem'
                                }}
                              >
                                🗑️
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </td>
                    <td style={{ padding: '15px', border: '1px solid #dee2e6' }}>
                      {rerunItems.length === 0 ? (
                        <div style={{ textAlign: 'center', color: '#6c757d', fontStyle: 'italic' }}>
                          لا توجد إعادات
                        </div>
                      ) : (
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                          {rerunItems.map((item) => (
                            <div key={item.id} style={{
                              background: '#f8f9fa',
                              borderRadius: '8px',
                              padding: '8px',
                              border: '1px solid #6c757d',
                              opacity: 0.8
                            }}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                                <span>🔄</span>
                                <strong style={{ color: '#6c757d' }}>
                                  {item.mediaItem?.name || 'مادة محذوفة'} (إعادة)
                                </strong>
                              </div>
                              <div style={{ fontSize: '0.8rem', color: '#6c757d' }}>
                                {item.startTime} - {item.endTime} • تلقائية
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </td>
                    <td style={{ padding: '15px', border: '1px solid #dee2e6', textAlign: 'center' }}>
                      <button
                        onClick={() => {
                          setSelectedDay(day.key);
                          setShowAddForm(true);
                        }}
                        style={{
                          background: 'linear-gradient(45deg, #007bff, #0056b3)',
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          padding: '8px 16px',
                          cursor: 'pointer',
                          fontSize: '0.8rem'
                        }}
                      >
                        ➕ إضافة
                      </button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* العرض التقليدي للأيام (اختياري) */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          {daysOfWeek.map((day) => (
            <div key={day.key} style={{
              background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
              borderRadius: '15px',
              padding: '20px',
              border: '1px solid #dee2e6',
              minHeight: '400px'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
                <h3 style={{ color: '#495057', margin: 0, fontSize: '1.2rem' }}>{day.label}</h3>
                <button
                  onClick={() => {
                    setSelectedDay(day.key);
                    setShowAddForm(true);
                  }}
                  style={{
                    background: 'linear-gradient(45deg, #007bff, #0056b3)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    padding: '8px 16px',
                    cursor: 'pointer',
                    fontSize: '0.8rem'
                  }}
                >
                  ➕ إضافة
                </button>
              </div>

              <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                {getItemsForDay(day.key).map((item) => (
                  <div key={item.id} style={{
                    background: item.isRerun ? '#f8f9fa' : 'white',
                    borderRadius: '10px',
                    padding: '15px',
                    border: item.isRerun ? '2px solid #6c757d20' : '2px solid #28a74520',
                    borderLeft: item.isRerun ? '4px solid #6c757d' : '4px solid #28a745',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    cursor: 'grab',
                    opacity: item.isRerun ? 0.8 : 1
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <h4 style={{ margin: '0 0 5px 0', color: item.isRerun ? '#6c757d' : '#333', fontSize: '1rem' }}>
                          {item.isRerun && '🔄 '}{item.mediaItem?.name || 'مادة محذوفة'}
                          {item.isRerun && ' (إعادة)'}
                        </h4>
                        <p style={{ margin: '0 0 5px 0', color: '#666', fontSize: '0.9rem' }}>
                          {item.startTime} - {item.endTime}
                        </p>
                        <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                          <span style={{
                            background: item.isRerun ? '#6c757d' : '#007bff',
                            color: 'white',
                            padding: '2px 8px',
                            borderRadius: '12px',
                            fontSize: '0.8rem'
                          }}>
                            {item.mediaItem ? getTypeLabel(item.mediaItem.type) : 'غير محدد'}
                          </span>
                          {item.isRerun && (
                            <span style={{
                              background: '#ffc107',
                              color: 'white',
                              padding: '2px 8px',
                              borderRadius: '12px',
                              fontSize: '0.7rem'
                            }}>
                              إعادة تلقائية
                            </span>
                          )}
                        </div>
                      </div>
                      <div style={{ display: 'flex', gap: '5px' }}>
                        {!item.isRerun && (
                          <button
                            onClick={() => removeFromSchedule(item.id)}
                            style={{
                            background: '#dc3545',
                            color: 'white',
                            border: 'none',
                            borderRadius: '5px',
                            padding: '5px 8px',
                            cursor: 'pointer',
                            fontSize: '0.8rem'
                          }}>
                            🗑️
                          </button>
                        )}
                        {item.isRerun && (
                          <span style={{
                            background: '#6c757d',
                            color: 'white',
                            padding: '5px 8px',
                            borderRadius: '5px',
                            fontSize: '0.8rem'
                          }}>
                            تلقائي
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {getItemsForDay(day.key).length === 0 && (
                  <div style={{
                    border: '2px dashed #ccc',
                    borderRadius: '10px',
                    padding: '40px',
                    textAlign: 'center',
                    color: '#999',
                    marginTop: '20px'
                  }}>
                    <div style={{ fontSize: '2rem', marginBottom: '10px' }}>📺</div>
                    <p>لا توجد مواد مجدولة لهذا اليوم</p>
                    <p style={{ fontSize: '0.9rem' }}>اضغط "إضافة" لإضافة مادة جديدة</p>
                    <p style={{ fontSize: '0.8rem', color: '#007bff' }}>
                      البرايم تايم: {day.key <= 3 ? '18:00-00:00' : '18:00-02:00'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div style={{ textAlign: 'center' }}>
          <div style={{
            background: 'linear-gradient(135d, #e3f2fd 0%, #bbdefb 100%)',
            borderRadius: '15px',
            padding: '20px',
            marginBottom: '20px',
            border: '1px solid #90caf9'
          }}>
            <h3 style={{ color: '#1565c0', marginBottom: '15px' }}>📊 إحصائيات الخريطة البرامجية</h3>
            <div style={{ display: 'flex', justifyContent: 'space-around', flexWrap: 'wrap', gap: '20px' }}>
              <div>
                <strong style={{ color: '#1565c0' }}>إجمالي المواد المجدولة:</strong>
                <span style={{ color: '#1565c0', marginLeft: '5px' }}>{scheduleItems.length}</span>
              </div>
              <div>
                <strong style={{ color: '#1565c0' }}>المواد المتاحة للإضافة:</strong>
                <span style={{ color: '#1565c0', marginLeft: '5px' }}>{availableMedia.length}</span>
              </div>
              <div>
                <strong style={{ color: '#1565c0' }}>الأيام المجدولة:</strong>
                <span style={{ color: '#1565c0', marginLeft: '5px' }}>
                  {new Set(scheduleItems.map(item => item.dayOfWeek)).size} من 7
                </span>
              </div>
            </div>
          </div>
        </div>

        <div style={{
          marginTop: '30px',
          padding: '20px',
          background: 'linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%)',
          borderRadius: '15px',
          border: '1px solid #ffeaa7',
          textAlign: 'center'
        }}>
          <h3 style={{ color: '#856404', marginBottom: '10px' }}>💡 نصائح للاستخدام</h3>
          <p style={{ color: '#856404', lineHeight: '1.6', margin: 0 }}>
            • اضغط "إضافة" في أي يوم لإضافة مادة إعلامية جديدة<br/>
            • تأكد من عدم وجود تعارض في الأوقات عند الإضافة<br/>
            • يمكنك حذف أي مادة بالضغط على زر الحذف<br/>
            • المواد المعروضة هي المواد الفعلية المضافة في النظام
          </p>
        </div>
      </div>
      <ToastContainer />
    </div>
  );
}