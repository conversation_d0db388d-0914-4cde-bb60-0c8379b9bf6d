# 🔐 دليل نظام المصادقة وإدارة المستخدمين

## 📋 نظرة عامة

تم تطوير نظام مصادقة متكامل مع إدارة أدوار وصلاحيات متقدمة لنظام إدارة المحتوى الإعلامي.

## 👥 أدوار المستخدمين

### 👑 مدير النظام (ADMIN)
- **الصلاحيات**: جميع الصلاحيات
- **الوصول**: جميع الصفحات والوظائف
- **المسؤوليات**: إدارة المستخدمين، إعدادات النظام، التصدير

### 📝 مدير المحتوى (MEDIA_MANAGER)
- **الصلاحيات**: 
  - MEDIA_CREATE: إضافة مواد جديدة
  - MEDIA_READ: عرض المواد
  - MEDIA_UPDATE: تعديل المواد
  - MEDIA_DELETE: حذف المواد
- **الوصول**: صفحات إدارة المحتوى
- **المسؤوليات**: إدارة المواد الإعلامية والسيجمانت

### 📅 مجدول البرامج (SCHEDULER)
- **الصلاحيات**:
  - SCHEDULE_CREATE: إنشاء جداول جديدة
  - SCHEDULE_READ: عرض الجداول
  - SCHEDULE_UPDATE: تعديل الجداول
  - SCHEDULE_DELETE: حذف الجداول
  - MEDIA_READ: عرض المواد (للجدولة)
- **الوصول**: الخريطة البرامجية، جدول الإذاعة
- **المسؤوليات**: إدارة الجداول الإذاعية والخريطة البرامجية

### 👁️ مستخدم عرض (VIEWER)
- **الصلاحيات**:
  - MEDIA_READ: عرض المواد فقط
  - SCHEDULE_READ: عرض الجداول فقط
- **الوصول**: عرض المحتوى بدون تعديل
- **المسؤوليات**: مراجعة وعرض المحتوى

## 🔑 بيانات تسجيل الدخول الافتراضية

```
👑 مدير النظام:
   اسم المستخدم: admin
   كلمة المرور: admin123

📝 مدير المحتوى:
   اسم المستخدم: media_manager
   كلمة المرور: media123

📅 مجدول البرامج:
   اسم المستخدم: scheduler
   كلمة المرور: schedule123

👁️ مستخدم عرض:
   اسم المستخدم: viewer
   كلمة المرور: view123
```

## 🛡️ نظام الحماية

### AuthGuard Component
```typescript
// حماية صفحة بصلاحية معينة
<AuthGuard requiredPermissions={['MEDIA_CREATE']}>
  <YourComponent />
</AuthGuard>

// حماية صفحة بدور معين
<AuthGuard requiredRole="ADMIN">
  <AdminComponent />
</AuthGuard>
```

### useAuth Hook
```typescript
const { user, hasPermission, logout } = useAuth();

// التحقق من صلاحية
if (hasPermission('MEDIA_CREATE')) {
  // عرض زر الإضافة
}

// التحقق من الدور
if (user?.role === 'ADMIN') {
  // عرض لوحة المدير
}
```

## 📁 هيكل الملفات

```
src/
├── app/
│   ├── login/page.tsx              # صفحة تسجيل الدخول
│   ├── admin-dashboard/page.tsx    # لوحة تحكم المدير
│   ├── unauthorized/page.tsx       # صفحة غير مصرح
│   └── api/
│       ├── auth/login/route.ts     # API المصادقة
│       └── users/route.ts          # API إدارة المستخدمين
├── components/
│   └── AuthGuard.tsx              # مكون الحماية
└── public/
    └── images/
        └── logo.jpeg              # شعار النظام
```

## 🔄 تدفق المصادقة

1. **تسجيل الدخول**: المستخدم يدخل بياناته في `/login`
2. **التحقق**: النظام يتحقق من البيانات عبر API
3. **حفظ الجلسة**: حفظ بيانات المستخدم في localStorage
4. **التوجيه**: توجيه المستخدم حسب دوره
5. **الحماية**: AuthGuard يحمي الصفحات حسب الصلاحيات

## 🎨 تخصيص اللوجو

تم تحديث النظام ليستخدم اللوجو المرفوع في:
- صفحة تسجيل الدخول
- الصفحة الرئيسية (شريط المستخدم)
- لوحة تحكم المدير

المسار: `/public/images/logo.jpeg`

## 🚀 الاستخدام

### إضافة مستخدم جديد
1. سجل دخول كمدير
2. اذهب للوحة تحكم المدير
3. اضغط "إضافة مستخدم جديد"
4. املأ البيانات واختر الدور
5. اضغط "إنشاء المستخدم"

### تعديل صلاحيات مستخدم
1. في لوحة المدير، ابحث عن المستخدم
2. اضغط زر التعديل (✏️)
3. غير الدور أو البيانات
4. احفظ التغييرات

### حذف مستخدم
1. في لوحة المدير، ابحث عن المستخدم
2. اضغط زر الحذف (🗑️)
3. أكد الحذف

## 🔧 التطوير المستقبلي

### قاعدة البيانات
- استبدال البيانات المؤقتة بقاعدة بيانات حقيقية
- إضافة تشفير كلمات المرور
- نظام JWT للتوكنات

### ميزات إضافية
- إعادة تعيين كلمة المرور
- تسجيل نشاط المستخدمين
- إعدادات الملف الشخصي
- نظام الإشعارات

### الأمان
- تشفير البيانات الحساسة
- حماية من CSRF
- تحديد معدل الطلبات
- مراجعة الأمان الدورية

## 📞 الدعم

للمساعدة أو الاستفسارات حول نظام المصادقة، يرجى التواصل مع فريق التطوير.

---

**ملاحظة**: هذا النظام مصمم للاستخدام الداخلي ويتطلب تحديثات أمنية إضافية للاستخدام في بيئة الإنتاج.
