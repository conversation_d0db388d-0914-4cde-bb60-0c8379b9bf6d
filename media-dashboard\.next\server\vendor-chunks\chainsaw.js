/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/chainsaw";
exports.ids = ["vendor-chunks/chainsaw"];
exports.modules = {

/***/ "(ssr)/./node_modules/chainsaw/index.js":
/*!****************************************!*\
  !*** ./node_modules/chainsaw/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Traverse = __webpack_require__(/*! traverse */ \"(ssr)/./node_modules/traverse/index.js\");\nvar EventEmitter = (__webpack_require__(/*! events */ \"events\").EventEmitter);\n\nmodule.exports = Chainsaw;\nfunction Chainsaw (builder) {\n    var saw = Chainsaw.saw(builder, {});\n    var r = builder.call(saw.handlers, saw);\n    if (r !== undefined) saw.handlers = r;\n    saw.record();\n    return saw.chain();\n};\n\nChainsaw.light = function ChainsawLight (builder) {\n    var saw = Chainsaw.saw(builder, {});\n    var r = builder.call(saw.handlers, saw);\n    if (r !== undefined) saw.handlers = r;\n    return saw.chain();\n};\n\nChainsaw.saw = function (builder, handlers) {\n    var saw = new EventEmitter;\n    saw.handlers = handlers;\n    saw.actions = [];\n\n    saw.chain = function () {\n        var ch = Traverse(saw.handlers).map(function (node) {\n            if (this.isRoot) return node;\n            var ps = this.path;\n\n            if (typeof node === 'function') {\n                this.update(function () {\n                    saw.actions.push({\n                        path : ps,\n                        args : [].slice.call(arguments)\n                    });\n                    return ch;\n                });\n            }\n        });\n\n        process.nextTick(function () {\n            saw.emit('begin');\n            saw.next();\n        });\n\n        return ch;\n    };\n\n    saw.pop = function () {\n        return saw.actions.shift();\n    };\n\n    saw.next = function () {\n        var action = saw.pop();\n\n        if (!action) {\n            saw.emit('end');\n        }\n        else if (!action.trap) {\n            var node = saw.handlers;\n            action.path.forEach(function (key) { node = node[key] });\n            node.apply(saw.handlers, action.args);\n        }\n    };\n\n    saw.nest = function (cb) {\n        var args = [].slice.call(arguments, 1);\n        var autonext = true;\n\n        if (typeof cb === 'boolean') {\n            var autonext = cb;\n            cb = args.shift();\n        }\n\n        var s = Chainsaw.saw(builder, {});\n        var r = builder.call(s.handlers, s);\n\n        if (r !== undefined) s.handlers = r;\n\n        // If we are recording...\n        if (\"undefined\" !== typeof saw.step) {\n            // ... our children should, too\n            s.record();\n        }\n\n        cb.apply(s.chain(), args);\n        if (autonext !== false) s.on('end', saw.next);\n    };\n\n    saw.record = function () {\n        upgradeChainsaw(saw);\n    };\n\n    ['trap', 'down', 'jump'].forEach(function (method) {\n        saw[method] = function () {\n            throw new Error(\"To use the trap, down and jump features, please \"+\n                            \"call record() first to start recording actions.\");\n        };\n    });\n\n    return saw;\n};\n\nfunction upgradeChainsaw(saw) {\n    saw.step = 0;\n\n    // override pop\n    saw.pop = function () {\n        return saw.actions[saw.step++];\n    };\n\n    saw.trap = function (name, cb) {\n        var ps = Array.isArray(name) ? name : [name];\n        saw.actions.push({\n            path : ps,\n            step : saw.step,\n            cb : cb,\n            trap : true\n        });\n    };\n\n    saw.down = function (name) {\n        var ps = (Array.isArray(name) ? name : [name]).join('/');\n        var i = saw.actions.slice(saw.step).map(function (x) {\n            if (x.trap && x.step <= saw.step) return false;\n            return x.path.join('/') == ps;\n        }).indexOf(true);\n\n        if (i >= 0) saw.step += i;\n        else saw.step = saw.actions.length;\n\n        var act = saw.actions[saw.step - 1];\n        if (act && act.trap) {\n            // It's a trap!\n            saw.step = act.step;\n            act.cb();\n        }\n        else saw.next();\n    };\n\n    saw.jump = function (step) {\n        saw.step = step;\n        saw.next();\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/chainsaw/index.js\n");

/***/ })

};
;