"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast)();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        description: '',\n        channel: '',\n        source: '',\n        status: '',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: '',\n        showInTX: false\n    });\n    // إضافة حالة لإظهار/إخفاء الحقول الخاصة حسب نوع المادة\n    const [showEpisodeNumber, setShowEpisodeNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSeasonNumber, setShowSeasonNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPartNumber, setShowPartNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الحقول المرئية عند تغيير نوع المادة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddMediaPage.useEffect\": ()=>{\n            console.log('🔍 نوع المادة المختار:', formData.type);\n            if (formData.type === 'FILM') {\n                // فيلم: يظهر رقم الجزء فقط\n                console.log('✅ عرض حقول الفيلم: رقم الجزء فقط');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'SERIES') {\n                // مسلسل: يظهر رقم الحلقة ورقم الجزء\n                console.log('✅ عرض حقول المسلسل: رقم الحلقة + رقم الجزء');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'PROGRAM') {\n                // برنامج: يظهر رقم الحلقة ورقم الموسم\n                console.log('✅ عرض حقول البرنامج: رقم الحلقة + رقم الموسم');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(true);\n                setShowPartNumber(false);\n            } else {\n                // باقي الأنواع: لا تظهر حقول إضافية\n                console.log('❌ إخفاء جميع الحقول الإضافية');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n            }\n            console.log('📊 حالة الحقول:', {\n                showEpisodeNumber,\n                showSeasonNumber,\n                showPartNumber\n            });\n        }\n    }[\"AddMediaPage.useEffect\"], [\n        formData.type\n    ]);\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: '',\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: field === 'showInTX' ? value === 'true' : value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n        // التحقق من كود السيجمانت عند تغييره\n        if (field === 'segmentCode' && value.trim()) {\n            validateSegmentCode(segmentId, value.trim());\n        } else if (field === 'segmentCode' && !value.trim()) {\n            // إزالة التحقق إذا كان الكود فارغ\n            setSegmentCodeValidation((prev)=>{\n                const newValidation = {\n                    ...prev\n                };\n                delete newValidation[segmentId];\n                return newValidation;\n            });\n        }\n    };\n    // دالة التحقق من كود السيجمانت\n    const validateSegmentCode = async (segmentId, code)=>{\n        // تحديث حالة التحقق\n        setSegmentCodeValidation((prev)=>({\n                ...prev,\n                [segmentId]: {\n                    isValid: true,\n                    message: '',\n                    isChecking: true\n                }\n            }));\n        try {\n            const response = await fetch(\"/api/validate-segment-code?code=\".concat(encodeURIComponent(code)));\n            const result = await response.json();\n            if (result.success) {\n                const isValid = !result.isDuplicate;\n                const message = result.isDuplicate ? \"الكود موجود في: \".concat(result.duplicates.map((d)=>d.mediaName).join(', ')) : 'الكود متاح';\n                setSegmentCodeValidation((prev)=>({\n                        ...prev,\n                        [segmentId]: {\n                            isValid,\n                            message,\n                            isChecking: false\n                        }\n                    }));\n            }\n        } catch (error) {\n            console.error('Error validating segment code:', error);\n            setSegmentCodeValidation((prev)=>({\n                    ...prev,\n                    [segmentId]: {\n                        isValid: true,\n                        message: 'خطأ في التحقق',\n                        isChecking: false\n                    }\n                }));\n        }\n    };\n    const calculateDuration = (segmentId)=>{\n        const segment = segments.find((s)=>s.id === segmentId);\n        if (!segment || !segment.timeIn || !segment.timeOut) return;\n        try {\n            const timeIn = segment.timeIn.split(':').map(Number);\n            const timeOut = segment.timeOut.split(':').map(Number);\n            // تحويل إلى ثواني\n            const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + timeIn[2];\n            const outSeconds = timeOut[0] * 3600 + timeOut[1] * 60 + timeOut[2];\n            // حساب الفرق\n            let durationSeconds = outSeconds - inSeconds;\n            if (durationSeconds < 0) {\n                durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب\n            }\n            // تحويل إلى تنسيق HH:MM:SS\n            const hours = Math.floor(durationSeconds / 3600);\n            const minutes = Math.floor(durationSeconds % 3600 / 60);\n            const seconds = durationSeconds % 60;\n            const duration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n            handleSegmentChange(segmentId, 'duration', duration);\n        } catch (error) {\n            console.error('Error calculating duration:', error);\n        }\n    };\n    const addSegment = ()=>{\n        const newId = segmentCount + 1;\n        setSegmentCount(newId);\n        setSegments((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    segmentCode: '',\n                    timeIn: '00:00:00',\n                    timeOut: '00:00:00',\n                    duration: '00:00:00'\n                }\n            ]);\n    };\n    const removeSegment = (id)=>{\n        if (segments.length <= 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegments((prev)=>prev.filter((segment)=>segment.id !== id));\n    };\n    const setSegmentCount2 = (count)=>{\n        if (count < 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            newSegments.push({\n                id: i,\n                segmentCode: '',\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [segmentCodeValidation, setSegmentCodeValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // التحقق من الحقول المطلوبة\n        const requiredFields = [];\n        if (!formData.hardDiskNumber.trim()) requiredFields.push('رقم الهارد');\n        if (!formData.type) requiredFields.push('نوع المادة');\n        if (!formData.channel) requiredFields.push('القناة');\n        if (!formData.status) requiredFields.push('الحالة');\n        // التحقق من كود السيجمانت\n        const segmentsWithoutCode = segments.filter((segment)=>!segment.segmentCode || segment.segmentCode.trim() === '');\n        if (segmentsWithoutCode.length > 0) {\n            requiredFields.push('كود السيجمانت (مطلوب لجميع السيجمانتات)');\n        }\n        // التحقق من Time Out للسيجمانت\n        const invalidSegments = segments.filter((segment)=>!segment.timeOut || segment.timeOut === '00:00:00');\n        if (invalidSegments.length > 0) {\n            requiredFields.push('Time Out للسيجمانت');\n        }\n        if (requiredFields.length > 0) {\n            showErrorToast('invalidData');\n            return;\n        }\n        // التحقق من تكرار أكواد السيجمانت\n        const segmentCodes = segments.map((s)=>s.segmentCode).filter((code)=>code && code.trim());\n        try {\n            const validationResponse = await fetch('/api/validate-segment-code', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    segmentCodes\n                })\n            });\n            const validationResult = await validationResponse.json();\n            if (validationResult.success && validationResult.hasAnyDuplicates) {\n                let errorMessage = 'تم العثور على أكواد مكررة:\\n\\n';\n                if (validationResult.hasInternalDuplicates) {\n                    errorMessage += \"\\uD83D\\uDD34 أكواد مكررة داخل نفس المادة: \".concat(validationResult.internalDuplicates.join(', '), \"\\n\\n\");\n                }\n                if (validationResult.hasExternalDuplicates) {\n                    errorMessage += '🔴 أكواد موجودة في مواد أخرى:\\n';\n                    validationResult.externalDuplicates.forEach((dup)=>{\n                        errorMessage += '- الكود \"'.concat(dup.segmentCode, '\" موجود في المادة: ').concat(dup.mediaName, \" (\").concat(dup.mediaType, \")\\n\");\n                    });\n                }\n                errorMessage += '\\nيرجى تغيير الأكواد المكررة قبل الحفظ.';\n                showErrorToast('invalidData');\n                return;\n            }\n        } catch (validationError) {\n            console.error('Error validating segment codes:', validationError);\n            showErrorToast('invalidData');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = \"token_\".concat(user.id || 'unknown', \"_\").concat(user.role || 'unknown');\n            console.log('Sending with token:', tokenWithRole);\n            console.log('User data:', user);\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(tokenWithRole)\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('mediaAdded');\n                // مسح جميع الحقول للبدء من جديد\n                setFormData({\n                    name: '',\n                    type: '',\n                    description: '',\n                    channel: '',\n                    source: '',\n                    status: '',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: '',\n                    showInTX: false\n                });\n                // مسح السيجمانتات والعودة لسيجمانت واحد\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: '',\n                        timeIn: '00:00:00',\n                        timeOut: '',\n                        duration: ''\n                    }\n                ]);\n                // إخفاء الحقول الخاصة\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n                console.log('✅ تم مسح جميع الحقول بنجاح');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error saving media:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // أنماط CSS\n    const inputStyle = {\n        width: '100%',\n        padding: '10px',\n        borderRadius: '5px',\n        marginBottom: '10px'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: t('media.addNew'),\n        subtitle: t('media.title'),\n        icon: \"➕\",\n        requiredPermissions: [\n            'MEDIA_CREATE'\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '15px',\n                    padding: '20px',\n                    marginBottom: '25px',\n                    border: '1px solid #6b7280',\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#ffffff',\n                            marginBottom: '10px',\n                            fontSize: '1.4rem',\n                            fontWeight: 'bold'\n                        },\n                        children: [\n                            \"➕ \",\n                            t('addMedia.title')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#e2e8f0',\n                            fontSize: '0.95rem',\n                            margin: 0\n                        },\n                        children: t('addMedia.subtitle')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDD \",\n                                    t('addMedia.basicInfo')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    fontWeight: 'bold',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCBE \",\n                                                    t('addMedia.hardDiskNumber'),\n                                                    \": \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 54\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('addMedia.hardDiskNumber'),\n                                                value: formData.hardDiskNumber,\n                                                onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    maxWidth: '200px',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"اسم المادة \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('media.type'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 39\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectType')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILM\",\n                                                                children: \"Film\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"Series\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"Program\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"Song\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEXT\",\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NOW\",\n                                                                children: \"Now\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"سنعود\",\n                                                                children: \"We'll Be Back\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"عدنا\",\n                                                                children: \"We're Back\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MINI\",\n                                                                children: \"Mini\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CROSS\",\n                                                                children: \"Cross\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('addMedia.channel'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectChannel')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.status'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectStatus')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: \"صالح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_CENSORSHIP\",\n                                                                children: \"مرفوض رقابياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_TECHNICAL\",\n                                                                children: \"مرفوض هندسياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"HOLD\",\n                                                                children: \"Hold\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.source')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: t('addMedia.source'),\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.startDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.startDate,\n                                                        onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.endDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.endDate,\n                                                        onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            showEpisodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.episodeNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.episodeNumber'),\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this),\n                                            showSeasonNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.seasonNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.seasonNumber'),\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this),\n                                            showPartNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.partNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.partNumber'),\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.description')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.description'),\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.notes')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.additionalNotes'),\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.showInTX,\n                                                        onChange: (e)=>handleInputChange('showInTX', e.target.checked.toString()),\n                                                        style: {\n                                                            marginLeft: '10px',\n                                                            width: '18px',\n                                                            height: '18px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            background: '#10b981',\n                                                            color: 'white',\n                                                            padding: '2px 8px',\n                                                            borderRadius: '4px',\n                                                            marginLeft: '10px',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"TX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t('addMedia.showInSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.8rem',\n                                                    color: '#9ca3af',\n                                                    marginTop: '5px',\n                                                    marginRight: '35px'\n                                                },\n                                                children: t('addMedia.txDescription')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('addMedia.segments')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: '#3b82f6',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    padding: '8px 15px',\n                                                    marginLeft: '10px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: t('addMedia.addSegment')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                value: segmentCount,\n                                                onChange: (e)=>setSegmentCount2(parseInt(e.target.value)),\n                                                style: {\n                                                    width: '60px',\n                                                    padding: '8px',\n                                                    borderRadius: '5px',\n                                                    border: '1px solid #6b7280',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    textAlign: 'center'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, this),\n                            segments.map((segment, index)=>{\n                                var _segmentCodeValidation_segment_id, _segmentCodeValidation_segment_id1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#374151',\n                                        borderRadius: '10px',\n                                        padding: '15px',\n                                        marginBottom: '15px',\n                                        border: '1px solid #4b5563'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'space-between',\n                                                alignItems: 'center',\n                                                marginBottom: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#f3f4f6',\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        t('addMedia.segment'),\n                                                        \" \",\n                                                        segment.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeSegment(segment.id),\n                                                    style: {\n                                                        background: '#ef4444',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '5px',\n                                                        padding: '5px 10px',\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: t('common.delete')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                gap: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.segmentCode')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: 'relative'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    placeholder: \"مثال: DDC000055-P1-3\",\n                                                                    required: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: segment.segmentCode ? '#1f2937' : '#7f1d1d',\n                                                                        color: 'white',\n                                                                        border: ((_segmentCodeValidation_segment_id = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id === void 0 ? void 0 : _segmentCodeValidation_segment_id.isValid) === false ? '2px solid #ef4444' : segment.segmentCode ? ((_segmentCodeValidation_segment_id1 = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id1 === void 0 ? void 0 : _segmentCodeValidation_segment_id1.isValid) === true ? '2px solid #10b981' : '1px solid #6b7280' : '2px solid #ef4444',\n                                                                        paddingRight: segmentCodeValidation[segment.id] ? '35px' : '12px'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                segmentCodeValidation[segment.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: 'absolute',\n                                                                        right: '8px',\n                                                                        top: '50%',\n                                                                        transform: 'translateY(-50%)',\n                                                                        fontSize: '16px'\n                                                                    },\n                                                                    children: segmentCodeValidation[segment.id].isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"⏳\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 29\n                                                                    }, this) : segmentCodeValidation[segment.id].isValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#10b981'\n                                                                        },\n                                                                        children: \"✅\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#ef4444'\n                                                                        },\n                                                                        children: \"❌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        segmentCodeValidation[segment.id] && segmentCodeValidation[segment.id].message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                marginTop: '4px',\n                                                                color: segmentCodeValidation[segment.id].isValid ? '#10b981' : '#ef4444'\n                                                            },\n                                                            children: segmentCodeValidation[segment.id].message\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: \"Time In\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: [\n                                                                \"Time Out \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#ef4444'\n                                                                    },\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            onFocus: (e)=>{\n                                                                // إزالة القيمة الوهمية عند النقر\n                                                                if (e.target.value === '00:00:00') {\n                                                                    handleSegmentChange(segment.id, 'timeOut', '');\n                                                                }\n                                                            },\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.durationAutoCalculated')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 899,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.duration,\n                                                            readOnly: true,\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280',\n                                                                opacity: '0.7',\n                                                                cursor: 'not-allowed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, segment.id, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'center',\n                            gap: '15px',\n                            marginTop: '20px',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#10b981',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: isSubmitting ? \"⏳ \".concat(t('common.saving')) : \"\\uD83D\\uDCBE \".concat(t('addMedia.saveAndAddNew'))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 922,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>router.push('/media-list'),\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#3b82f6',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCB \",\n                                    t('navigation.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    // مسح جميع الحقول يدوياً\n                                    setFormData({\n                                        name: '',\n                                        type: '',\n                                        description: '',\n                                        channel: '',\n                                        source: '',\n                                        status: '',\n                                        startDate: new Date().toISOString().split('T')[0],\n                                        endDate: '',\n                                        notes: '',\n                                        episodeNumber: '',\n                                        seasonNumber: '',\n                                        partNumber: '',\n                                        hardDiskNumber: '',\n                                        showInTX: false\n                                    });\n                                    setSegmentCount(1);\n                                    setSegments([\n                                        {\n                                            id: 1,\n                                            segmentCode: '',\n                                            timeIn: '00:00:00',\n                                            timeOut: '',\n                                            duration: ''\n                                        }\n                                    ]);\n                                    setShowEpisodeNumber(false);\n                                    setShowSeasonNumber(false);\n                                    setShowPartNumber(false);\n                                    showSuccessToast('changesSaved');\n                                },\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#f59e0b',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDDD1️ \",\n                                    t('addMedia.clearFields')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 959,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 921,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 1015,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 372,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"e+B/6U+sBCuqa5PR2qwxBJwteH8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast,\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});