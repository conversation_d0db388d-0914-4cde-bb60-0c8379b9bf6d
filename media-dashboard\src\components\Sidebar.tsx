'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from './AuthGuard';
import { useTranslation } from 'react-i18next';
import Logo from './Logo';


interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

interface MenuItem {
  name: string;
  icon: string;
  path: string;
  permission: string | null;
  adminOnly?: boolean;
}

export default function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, hasPermission } = useAuth();
  const { t, i18n } = useTranslation('common');

  // Get current language and direction
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';

  const menuItems: MenuItem[] = [
    {
      name: t('navigation.dashboard'),
      icon: '📊',
      path: '/dashboard',
      permission: null
    },
    {
      name: t('navigation.mediaList'),
      icon: '🎬',
      path: '/media-list',
      permission: 'MEDIA_READ'
    },
    {
      name: t('navigation.addMedia'),
      icon: '➕',
      path: '/add-media',
      permission: 'MEDIA_CREATE'
    },
    {
      name: t('navigation.weeklySchedule'),
      icon: '📅',
      path: '/weekly-schedule',
      permission: 'SCHEDULE_READ'
    },
    {
      name: t('navigation.dailySchedule'),
      icon: '📊',
      path: '/daily-schedule',
      permission: 'SCHEDULE_READ'
    },
    {
      name: t('navigation.reports'),
      icon: '📋',
      path: '/reports',
      permission: 'SCHEDULE_READ'
    },
    {
      name: 'استيراد جدول',
      icon: '📤',
      path: '/daily-schedule/import',
      permission: 'SCHEDULE_READ'
    },
    {
      name: t('navigation.adminDashboard'),
      icon: '👥',
      path: '/admin-dashboard',
      permission: null
    },
    {
      name: t('navigation.statistics'),
      icon: '📈',
      path: '/statistics',
      permission: null
    }
  ];

  const filteredMenuItems = menuItems.filter(item => {
    if (item.adminOnly && user?.role !== 'ADMIN') return false;
    if (item.permission && !hasPermission(item.permission)) return false;
    return true;
  });

  return (
    <div>
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            zIndex: 998,
            display: window.innerWidth <= 768 ? 'block' : 'none'
          }}
          onClick={onToggle}
        />
      )}

      <div
        style={{
          position: 'fixed',
          top: 0,
          ...(isRTL ? {
            right: isOpen ? 0 : '-280px',
            borderLeft: '1px solid #2d3748'
          } : {
            left: isOpen ? 0 : '-280px',
            borderRight: '1px solid #2d3748'
          }),
          width: '280px',
          height: '100vh',
          background: '#1a1d29',
          transition: `${isRTL ? 'right' : 'left'} 0.3s ease`,
          zIndex: 999,
          display: 'flex',
          flexDirection: 'column',
          fontFamily: 'Cairo, Arial, sans-serif'
        }}
      >
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <div>
              <Logo size="small" style={{ color: 'white' }} />
              <p style={{
                color: '#a0aec0',
                margin: 0,
                fontSize: '0.8rem'
              }}>
                {t('dashboard.subtitle')}
              </p>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <button
              onClick={onToggle}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.2rem',
                cursor: 'pointer',
                padding: '5px'
              }}
            >
              ✕
            </button>
          </div>
        </div>

        <div style={{
          flex: 1,
          padding: '20px 0',
          overflowY: 'auto'
        }}>
          {filteredMenuItems.map((item, index) => {
            const isActive = pathname === item.path;
            return (
              <button
                key={index}
                onClick={() => {
                  router.push(item.path);
                  if (window.innerWidth <= 768) {
                    onToggle();
                  }
                }}
                style={{
                  width: '100%',
                  background: isActive ? '#2d3748' : 'transparent',
                  color: isActive ? 'white' : '#a0aec0',
                  border: 'none',
                  borderTop: 'none',
                  borderBottom: 'none',
                  ...(isRTL ? {
                    borderLeft: 'none',
                    borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'
                  } : {
                    borderRight: 'none',
                    borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'
                  }),
                  padding: isRTL ? '12px 20px 12px 8px' : '12px 8px 12px 20px',
                  textAlign: isRTL ? 'right' : 'left',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease',
                  direction: isRTL ? 'rtl' : 'ltr'
                }}
              >
                {item.name}
              </button>
            );
          })}
        </div>

        <div style={{
          padding: '20px',
          borderTop: '1px solid #2d3748'
        }}>
          <button
            onClick={() => {
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              router.push('/login');
            }}
            style={{
              width: '100%',
              background: 'linear-gradient(45deg, #f56565, #e53e3e)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: '10px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              fontSize: '0.9rem',
              fontWeight: 'bold',
              marginBottom: '15px'
            }}
          >
            🚪 تسجيل الخروج
          </button>
        </div>
      </div>
    </div>
  );
}
