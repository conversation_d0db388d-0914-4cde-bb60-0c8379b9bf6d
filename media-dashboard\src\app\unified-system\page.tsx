'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useTranslation } from 'react-i18next';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  duration: string;
  segments?: Segment[];
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
}

interface Segment {
  id: string;
  segmentCode: string;
  tcIn: string;
  tcOut: string;
  duration: string;
}

export default function UnifiedSystemPage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const { t, i18n } = useTranslation('common');
  
  // Get current language and direction
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';
  
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  // متغيرات القائمة الجانبية
  const [availableMedia, setAvailableMedia] = useState<MediaItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('ALL');

  // التحقق من صلاحيات المستخدم
  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'ADMIN')) {
      alert(t('auth.accessDenied'));
      router.push('/dashboard');
      return;
    }
  }, [user, isLoading, router, t]);

  // تحميل المواد المتاحة
  useEffect(() => {
    fetchAvailableMedia();
  }, []);

  const fetchAvailableMedia = async () => {
    try {
      const response = await fetch('/api/media');
      const data = await response.json();
      if (data.success) {
        setAvailableMedia(data.data || []);
        console.log('📚 Loaded', data.data?.length || 0, 'available media items');
      }
    } catch (error) {
      console.error('❌ Error loading media:', error);
    }
  };

  // فلترة المواد
  const filteredMedia = availableMedia.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'ALL' || item.type === selectedType;
    return matchesSearch && matchesType;
  });

  const handleImport = async () => {
    if (!file) {
      alert(t('unified.selectFile'));
      return;
    }

    setLoading(true);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/import', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();
      if (data.success) {
        alert(`${t('unified.importSuccess')}! ${data.data.imported} ${t('unified.itemsImported')}`);
        setResult(data);
        fetchAvailableMedia(); // Reload media
      } else {
        alert(`${t('common.error')}: ${data.error}`);
      }
    } catch (error) {
      console.error('Error:', error);
      alert(t('unified.importError'));
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch('/api/export');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `media-export-${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        alert(t('unified.exportSuccess'));
      } else {
        alert(t('unified.exportError'));
      }
    } catch (error) {
      alert(t('unified.exportError'));
    }
  };

  // عرض شاشة تحميل أثناء التحقق من المصادقة
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        background: '#1a1d29',
        color: '#f3f4f6'
      }}>
        <div>{t('common.loading')}</div>
      </div>
    );
  }

  // إذا لم يكن المستخدم مسجل دخول أو ليس admin
  if (!user || user.role !== 'ADMIN') {
    return null; // Will redirect in useEffect
  }

  return (
    <DashboardLayout
      title={t('unified.title')}
      subtitle={t('unified.subtitle')}
      icon="🔄"
    >
      <div style={{ 
        display: 'flex', 
        gap: '20px', 
        height: 'calc(100vh - 200px)',
        direction: isRTL ? 'rtl' : 'ltr'
      }}>
        
        {/* القائمة الجانبية */}
        <div style={{
          width: '300px',
          background: '#2d3748',
          borderRadius: '8px',
          padding: '20px',
          overflowY: 'auto'
        }}>
          <h3 style={{ color: '#f3f4f6', marginBottom: '20px' }}>
            {t('unified.mediaLibrary')} ({filteredMedia.length})
          </h3>
          
          <input
            type="text"
            placeholder={t('unified.searchMedia')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '10px',
              marginBottom: '15px',
              border: 'none',
              borderRadius: '4px',
              background: '#4a5568',
              color: '#f3f4f6'
            }}
          />

          {loading ? (
            <div style={{ color: '#f3f4f6' }}>{t('unified.loadingMedia')}</div>
          ) : filteredMedia.length === 0 ? (
            <div style={{ color: '#9ca3af' }}>
              {availableMedia.length === 0 ? t('unified.noMedia') : t('reports.noResults')}
            </div>
          ) : (
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              {filteredMedia.map((item) => (
                <div
                  key={item.id}
                  style={{
                    padding: '10px',
                    marginBottom: '8px',
                    background: '#4a5568',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    color: '#f3f4f6'
                  }}
                >
                  <div style={{ fontWeight: 'bold' }}>{item.name}</div>
                  <div style={{ fontSize: '0.8rem', color: '#9ca3af' }}>
                    {item.type} • {item.duration}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* المحتوى الرئيسي */}
        <div style={{ flex: 1, background: '#2d3748', borderRadius: '8px', padding: '20px' }}>
          <h2 style={{ color: '#f3f4f6', marginBottom: '20px' }}>
            📥 {t('unified.importExport')}
          </h2>
          
          <div style={{ marginBottom: '20px' }}>
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={(e) => setFile(e.target.files?.[0] || null)}
              style={{
                padding: '10px',
                border: 'none',
                borderRadius: '4px',
                background: '#4a5568',
                color: '#f3f4f6',
                marginBottom: '10px',
                width: '100%'
              }}
            />
            
            {file && (
              <p style={{ color: '#10b981' }}>✅ {t('unified.fileSelected')}: {file.name}</p>
            )}
          </div>

          <button
            onClick={handleImport}
            disabled={!file || loading}
            style={{
              padding: '12px 24px',
              background: loading ? '#6b7280' : '#10b981',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: loading ? 'not-allowed' : 'pointer',
              marginRight: '10px'
            }}
          >
            {loading ? `⏳ ${t('unified.processing')}` : `📥 ${t('unified.uploadFile')}`}
          </button>

          <button
            onClick={handleExport}
            style={{
              padding: '12px 24px',
              background: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            📤 {t('reports.exportExcel')}
          </button>

          {result && (
            <div style={{
              marginTop: '20px',
              padding: '15px',
              background: '#4a5568',
              borderRadius: '4px',
              color: '#f3f4f6'
            }}>
              <h3>📊 {t('unified.result')}</h3>
              <pre style={{ fontSize: '0.8rem', overflow: 'auto' }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
