/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/weekly-schedule/route";
exports.ids = ["app/api/weekly-schedule/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_weekly_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/weekly-schedule/route.ts */ \"(rsc)/./src/app/api/weekly-schedule/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/weekly-schedule/route\",\n        pathname: \"/api/weekly-schedule\",\n        filename: \"route\",\n        bundlePath: \"app/api/weekly-schedule/route\"\n    },\n    resolvedPagePath: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\weekly-schedule\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_weekly_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/shared-data.ts":
/*!************************************!*\
  !*** ./src/app/api/shared-data.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMediaItem: () => (/* binding */ addMediaItem),\n/* harmony export */   clearAllMediaItems: () => (/* binding */ clearAllMediaItems),\n/* harmony export */   getAllMediaItems: () => (/* binding */ getAllMediaItems),\n/* harmony export */   getMediaItemById: () => (/* binding */ getMediaItemById),\n/* harmony export */   removeMediaItem: () => (/* binding */ removeMediaItem),\n/* harmony export */   saveImportedData: () => (/* binding */ saveImportedData),\n/* harmony export */   updateMediaItem: () => (/* binding */ updateMediaItem)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// ملف مشترك للبيانات - يضمن التزامن بين جميع APIs\n\n\n// مسار ملف البيانات المؤقت\nconst DATA_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'temp-data.json');\n// قاعدة بيانات مشتركة للمواد الإعلامية\nlet mediaItems = [];\n// تحميل البيانات من الملف عند بدء التشغيل\nfunction loadData() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(DATA_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(DATA_FILE, 'utf8');\n            mediaItems = JSON.parse(data);\n            console.log(`📂 تم تحميل ${mediaItems.length} مادة من الملف المؤقت`);\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل البيانات:', error);\n        mediaItems = [];\n    }\n}\n// حفظ البيانات في الملف\nfunction saveData() {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(DATA_FILE, JSON.stringify(mediaItems, null, 2));\n        console.log(`💾 تم حفظ ${mediaItems.length} مادة في الملف المؤقت`);\n    } catch (error) {\n        console.error('❌ خطأ في حفظ البيانات:', error);\n    }\n}\n// تحميل البيانات عند استيراد الملف\nloadData();\n// دالة لإضافة مادة جديدة\nfunction addMediaItem(item) {\n    mediaItems.push(item);\n    saveData(); // حفظ فوري\n    console.log(`✅ تم إضافة مادة جديدة: ${item.name} (المجموع: ${mediaItems.length})`);\n}\n// دالة لحذف مادة\nfunction removeMediaItem(id) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        const removed = mediaItems.splice(index, 1)[0];\n        saveData(); // حفظ فوري\n        console.log(`🗑️ تم حذف المادة: ${removed.name} (المجموع: ${mediaItems.length})`);\n        return true;\n    }\n    return false;\n}\n// دالة للحصول على جميع المواد\nfunction getAllMediaItems() {\n    return mediaItems;\n}\n// دالة للحصول على مادة بالمعرف\nfunction getMediaItemById(id) {\n    return mediaItems.find((item)=>item.id === id);\n}\n// دالة لتحديث مادة\nfunction updateMediaItem(id, updatedItem) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        mediaItems[index] = {\n            ...mediaItems[index],\n            ...updatedItem\n        };\n        saveData(); // حفظ فوري\n        console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);\n        return true;\n    }\n    return false;\n}\n// دالة لحذف جميع المواد\nfunction clearAllMediaItems() {\n    const count = mediaItems.length;\n    mediaItems = [];\n    saveData(); // حفظ فوري\n    console.log(`🗑️ تم حذف جميع المواد (${count} مادة)`);\n    return count;\n}\n// دالة لحفظ البيانات المستوردة\nfunction saveImportedData(items) {\n    mediaItems = items;\n    saveData(); // حفظ فوري\n    console.log(`📥 تم حفظ ${items.length} مادة مستوردة`);\n    return items.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shared-data.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/weekly-schedule/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/weekly-schedule/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared-data */ \"(rsc)/./src/app/api/shared-data.ts\");\n\n\n\n// استيراد البيانات المشتركة\n\n// بيانات الجداول الأسبوعية مع التواريخ\nlet weeklySchedules = new Map();\n// بيانات المواد المؤقتة لكل أسبوع\nlet tempItems = new Map();\n// مسار ملف الحفظ\nconst SCHEDULES_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'weekly-schedules.json');\nconst TEMP_ITEMS_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data', 'temp-items.json');\n// التأكد من وجود مجلد البيانات\nasync function ensureDataDir() {\n    const dataDir = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'data');\n    try {\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.access(dataDir);\n    } catch  {\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.mkdir(dataDir, {\n            recursive: true\n        });\n    }\n}\n// حفظ البيانات في ملف\nasync function saveSchedulesToFile() {\n    try {\n        await ensureDataDir();\n        const schedulesData = Object.fromEntries(weeklySchedules);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(SCHEDULES_FILE, JSON.stringify(schedulesData, null, 2));\n        console.log('💾 تم حفظ الجداول في الملف');\n    } catch (error) {\n        console.error('❌ خطأ في حفظ الجداول:', error);\n    }\n}\n// حفظ المواد المؤقتة في ملف\nasync function saveTempItemsToFile() {\n    try {\n        await ensureDataDir();\n        const tempData = Object.fromEntries(tempItems);\n        await fs__WEBPACK_IMPORTED_MODULE_1__.promises.writeFile(TEMP_ITEMS_FILE, JSON.stringify(tempData, null, 2));\n        console.log('💾 تم حفظ المواد المؤقتة في الملف');\n    } catch (error) {\n        console.error('❌ خطأ في حفظ المواد المؤقتة:', error);\n    }\n}\n// تحميل البيانات من الملف\nasync function loadSchedulesFromFile() {\n    try {\n        const data = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(SCHEDULES_FILE, 'utf8');\n        const schedulesData = JSON.parse(data);\n        weeklySchedules = new Map(Object.entries(schedulesData));\n        console.log('📂 تم تحميل الجداول من الملف');\n    } catch (error) {\n        console.log('📂 لا يوجد ملف جداول محفوظ، بدء جديد');\n    }\n}\n// تحميل المواد المؤقتة من الملف\nasync function loadTempItemsFromFile() {\n    try {\n        const data = await fs__WEBPACK_IMPORTED_MODULE_1__.promises.readFile(TEMP_ITEMS_FILE, 'utf8');\n        const tempData = JSON.parse(data);\n        tempItems = new Map(Object.entries(tempData));\n        console.log('📂 تم تحميل المواد المؤقتة من الملف');\n    } catch (error) {\n        console.log('📂 لا يوجد ملف مواد مؤقتة محفوظ، بدء جديد');\n    }\n}\n// تحميل البيانات عند بدء التشغيل\nlet dataLoaded = false;\nasync function initializeData() {\n    if (!dataLoaded) {\n        await loadSchedulesFromFile();\n        await loadTempItemsFromFile();\n        dataLoaded = true;\n    }\n}\n// Helper functions\nfunction getWeekStart(date) {\n    const d = new Date(date);\n    d.setDate(date.getDate() - date.getDay());\n    return d;\n}\nfunction formatDate(date) {\n    return date.toISOString().split('T')[0];\n}\nfunction timeToMinutes(time) {\n    if (!time || typeof time !== 'string') {\n        console.warn(`⚠️ وقت غير صحيح: ${time}`);\n        return 0;\n    }\n    const [hours, minutes] = time.split(':').map(Number);\n    return hours * 60 + minutes;\n}\n// حساب المدة الإجمالية للمادة\nfunction calculateTotalDuration(segments) {\n    if (!segments || segments.length === 0) return '01:00:00';\n    let totalSeconds = 0;\n    segments.forEach((segment)=>{\n        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n        totalSeconds += hours * 3600 + minutes * 60 + seconds;\n    });\n    const hours = Math.floor(totalSeconds / 3600);\n    const minutes = Math.floor(totalSeconds % 3600 / 60);\n    const secs = totalSeconds % 60;\n    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n}\nfunction addMinutesToTime(timeStr, minutes) {\n    const totalMinutes = timeToMinutes(timeStr) + minutes;\n    const hours = Math.floor(totalMinutes / 60) % 24;\n    const mins = totalMinutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\nfunction calculateDuration(startTime, endTime) {\n    const startMinutes = timeToMinutes(startTime);\n    const endMinutes = timeToMinutes(endTime);\n    let duration = endMinutes - startMinutes;\n    if (duration < 0) duration += 24 * 60;\n    return duration;\n}\nfunction isPrimeTime(startTime, dayOfWeek) {\n    const startMinutes = timeToMinutes(startTime);\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: 18:00-00:00\n        return startMinutes >= timeToMinutes('18:00');\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: 18:00-02:00 (اليوم التالي)\n        return startMinutes >= timeToMinutes('18:00') || startMinutes < timeToMinutes('02:00');\n    }\n    return false;\n}\n// دالة توليد الإعادات التلقائية (متتالية)\nfunction generateReruns(scheduleItems, weekStart, tempItems = []) {\n    const reruns = [];\n    console.log(`🔄 توليد الإعادات للأسبوع: ${weekStart}`);\n    // دمج المواد العادية والمؤقتة مع فلترة المواد المحذوفة\n    const permanentDeletedSet = deletedReruns.get('permanent_temp_deletions') || new Set();\n    console.log(`🔍 فحص المواد المحذوفة نهائياً: ${permanentDeletedSet.size} مادة`);\n    const allItems = [\n        ...scheduleItems,\n        ...tempItems\n    ];\n    const regularItems = allItems.filter((item)=>{\n        // استبعاد الإعادات والمواد المؤقتة\n        if (item.isRerun || item.isTemporary) return false;\n        // استبعاد المواد المحذوفة بالمعرف\n        if (permanentDeletedSet.has(item.mediaItemId) || permanentDeletedSet.has(item.id)) {\n            console.log(`🚫 استبعاد مادة محذوفة: ${item.mediaItem?.name || 'غير معروف'} (${item.id})`);\n            return false;\n        }\n        // استبعاد المواد المحذوفة بالاسم\n        if (item.mediaItem && permanentDeletedSet.has(`name_${item.mediaItem.name}`)) {\n            console.log(`🚫 استبعاد مادة محذوفة بالاسم: ${item.mediaItem.name}`);\n            return false;\n        }\n        // استبعاد المواد المحذوفة بالموقع\n        if (item.dayOfWeek !== undefined && item.startTime && permanentDeletedSet.has(`pos_${item.dayOfWeek}_${item.startTime}`)) {\n            console.log(`🚫 استبعاد مادة محذوفة بالموقع: يوم ${item.dayOfWeek} وقت ${item.startTime}`);\n            return false;\n        }\n        return true;\n    });\n    const temporaryItems = allItems.filter((item)=>{\n        // فقط المواد المؤقتة وليست إعادات\n        if (!item.isTemporary || item.isRerun) return false;\n        // استبعاد المواد المحذوفة بالمعرف\n        if (permanentDeletedSet.has(item.mediaItemId) || permanentDeletedSet.has(item.id)) {\n            console.log(`🚫 استبعاد مادة مؤقتة محذوفة: ${item.mediaItem?.name || 'غير معروف'} (${item.id})`);\n            return false;\n        }\n        // استبعاد المواد المحذوفة بالاسم\n        if (item.mediaItem && permanentDeletedSet.has(`name_${item.mediaItem.name}`)) {\n            console.log(`🚫 استبعاد مادة مؤقتة محذوفة بالاسم: ${item.mediaItem.name}`);\n            return false;\n        }\n        // استبعاد المواد المحذوفة بالموقع\n        if (item.dayOfWeek !== undefined && item.startTime && permanentDeletedSet.has(`pos_${item.dayOfWeek}_${item.startTime}`)) {\n            console.log(`🚫 استبعاد مادة مؤقتة محذوفة بالموقع: يوم ${item.dayOfWeek} وقت ${item.startTime}`);\n            return false;\n        }\n        return true;\n    });\n    console.log(`📊 المواد: ${regularItems.length} عادية + ${temporaryItems.length} مؤقتة (بعد فلترة المحذوفة)`);\n    // التحقق من وجود مواد للإعادة\n    if (regularItems.length === 0 && temporaryItems.length === 0) {\n        console.log('⚠️ لا توجد مواد لتوليد إعادات منها');\n        return reruns;\n    }\n    // تجميع المواد حسب اليوم لتوليد إعادات متتالية\n    const itemsByDay = new Map();\n    // إضافة المواد العادية في البرايم تايم\n    regularItems.filter((item)=>isPrimeTime(item.startTime, item.dayOfWeek)).forEach((item)=>{\n        if (!itemsByDay.has(item.dayOfWeek)) {\n            itemsByDay.set(item.dayOfWeek, []);\n        }\n        itemsByDay.get(item.dayOfWeek).push(item);\n    });\n    // إضافة المواد المؤقتة في البرايم تايم (تجاهل المواد المؤقتة في القائمة الجانبية)\n    temporaryItems.filter((item)=>item.startTime && item.dayOfWeek !== undefined && item.startTime !== undefined && isPrimeTime(item.startTime, item.dayOfWeek)).forEach((item)=>{\n        if (!itemsByDay.has(item.dayOfWeek)) {\n            itemsByDay.set(item.dayOfWeek, []);\n        }\n        itemsByDay.get(item.dayOfWeek).push({\n            ...item,\n            isTemporary: true\n        });\n    });\n    // توليد إعادات متتالية لكل يوم\n    itemsByDay.forEach((dayItems, dayOfWeek)=>{\n        console.log(`📅 اليوم ${dayOfWeek}: ${dayItems.length} مادة في البرايم`);\n        // ترتيب المواد حسب وقت البداية في البرايم (مع مراعاة انتقال اليوم)\n        dayItems.sort((a, b)=>{\n            let timeA = timeToMinutes(a.startTime);\n            let timeB = timeToMinutes(b.startTime);\n            // للخميس-السبت: إذا كان الوقت أقل من 02:00، فهو في اليوم التالي\n            if ([\n                4,\n                5,\n                6\n            ].includes(dayOfWeek)) {\n                if (timeA < timeToMinutes('02:00')) timeA += 24 * 60; // إضافة 24 ساعة\n                if (timeB < timeToMinutes('02:00')) timeB += 24 * 60;\n            }\n            return timeA - timeB;\n        });\n        console.log(`📋 ترتيب المواد حسب وقت البث: ${dayItems.map((item, i)=>`${i + 1}:${item.mediaItem?.name || 'مؤقت'}(${item.startTime})`).join(', ')}`);\n        const dayReruns = generateSequentialReruns(dayItems, weekStart, dayOfWeek);\n        reruns.push(...dayReruns);\n    });\n    console.log(`✅ تم توليد ${reruns.length} إعادة إجمالية`);\n    return reruns;\n}\n// دالة توليد إعادات متتالية لمواد يوم واحد (ترتيب مستمر مضمون)\nfunction generateSequentialReruns(dayItems, weekStart, dayOfWeek) {\n    const reruns = [];\n    if (dayItems.length === 0) return reruns;\n    console.log(`🔄 توليد إعادات لـ ${dayItems.length} مادة من اليوم ${dayOfWeek}`);\n    console.log(`📋 ترتيب المواد: ${dayItems.map((item, i)=>`${i + 1}:${item.mediaItem?.name || 'مؤقت'}`).join(', ')}`);\n    // تحديد أوقات الإعادات حسب اليوم\n    let rerunStartTime;\n    let rerunDay;\n    if ([\n        0,\n        1,\n        2,\n        3\n    ].includes(dayOfWeek)) {\n        // الأحد-الأربعاء: الإعادات تبدأ من 00:00 في نفس اليوم\n        rerunStartTime = '00:00';\n        rerunDay = dayOfWeek;\n    } else if ([\n        4,\n        5,\n        6\n    ].includes(dayOfWeek)) {\n        // الخميس-السبت: الإعادات تبدأ من 02:00 في نفس اليوم (بعد انتهاء البرايم)\n        rerunStartTime = '02:00';\n        rerunDay = dayOfWeek;\n    } else {\n        return reruns;\n    }\n    // إنشاء قائمة مستمرة من المواد (تكرار لانهائي)\n    function getNextItem(index) {\n        return dayItems[index % dayItems.length];\n    }\n    let currentTime = rerunStartTime;\n    let itemSequenceIndex = 0; // فهرس التسلسل المستمر - يبدأ من 0 (المادة الأولى)\n    console.log(`🚀 بدء الجزء الأول من ${rerunStartTime} حتى 08:00 - بدءاً من المادة الأولى: ${dayItems[0].mediaItem?.name || 'مؤقت'}`);\n    // الجزء الأول: حتى 08:00\n    while(timeToMinutes(currentTime) < timeToMinutes('08:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('08:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${rerunDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: rerunDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: weekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                rerunPart: 1,\n                rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,\n                mediaItem: item.mediaItem,\n                originalId: item.id\n            });\n            console.log(`✅ جزء 1: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${itemSequenceIndex % dayItems.length + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            console.log(`⏰ توقف الجزء الأول عند ${currentTime} - المادة التالية: ${item.mediaItem?.name || 'مؤقت'}`);\n            break;\n        }\n    }\n    console.log(`📊 انتهى الجزء الأول: فهرس التسلسل ${itemSequenceIndex}, المادة التالية: ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'}`);\n    // حساب اليوم التالي مع مراعاة الأسبوع الجديد\n    let nextDay;\n    let nextWeekStart = weekStart;\n    if (dayOfWeek === 6) {\n        nextDay = 0; // الأحد في الأسبوع التالي\n        const nextWeekDate = new Date(weekStart);\n        nextWeekDate.setDate(nextWeekDate.getDate() + 7);\n        nextWeekStart = formatDate(nextWeekDate);\n        console.log(`🔄 إعادات السبت تذهب للأحد في الأسبوع التالي: ${nextWeekStart}`);\n    } else {\n        nextDay = (rerunDay + 1) % 7;\n    }\n    currentTime = '08:00';\n    console.log(`🚀 بدء الجزء الثاني: العمود ${nextDay} - استكمال من المادة ${getNextItem(itemSequenceIndex).mediaItem?.name || 'مؤقت'} (فهرس ${itemSequenceIndex})`);\n    // الجزء الثاني: استكمال من حيث توقف الجزء الأول\n    while(timeToMinutes(currentTime) < timeToMinutes('18:00')){\n        const item = getNextItem(itemSequenceIndex);\n        const durationMinutes = timeToMinutes(item.endTime) - timeToMinutes(item.startTime);\n        const rerunEndTime = addMinutesToTime(currentTime, durationMinutes);\n        if (timeToMinutes(rerunEndTime) <= timeToMinutes('18:00')) {\n            reruns.push({\n                id: `rerun_${item.id}_seq${itemSequenceIndex}_${nextDay}`,\n                mediaItemId: item.mediaItemId,\n                dayOfWeek: nextDay,\n                startTime: currentTime,\n                endTime: rerunEndTime,\n                weekStart: nextWeekStart,\n                isRerun: true,\n                isTemporary: item.isTemporary || false,\n                rerunPart: 2,\n                rerunCycle: Math.floor(itemSequenceIndex / dayItems.length) + 1,\n                mediaItem: item.mediaItem,\n                originalId: item.id,\n                isNextWeekRerun: dayOfWeek === 6\n            });\n            console.log(`✅ جزء 2: [${itemSequenceIndex}] ${item.mediaItem?.name || 'مؤقت'} (مادة ${itemSequenceIndex % dayItems.length + 1}/${dayItems.length}, دورة ${Math.floor(itemSequenceIndex / dayItems.length) + 1}) ${currentTime}-${rerunEndTime}`);\n            currentTime = rerunEndTime;\n            itemSequenceIndex++;\n        } else {\n            console.log(`⏰ توقف الجزء الثاني عند ${currentTime} لتجاوز 18:00`);\n            break;\n        }\n    }\n    return reruns;\n}\n// قائمة الإعادات المحذوفة (لترك الحقول فارغة)\nconst deletedReruns = new Map(); // weekStart -> Set of rerun IDs\n// GET - جلب الجدول الأسبوعي\nasync function GET(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const { searchParams } = new URL(request.url);\n        const weekStartParam = searchParams.get('weekStart');\n        const tempItemsParam = searchParams.get('tempItems');\n        const weekStart = weekStartParam ? new Date(weekStartParam) : getWeekStart(new Date());\n        const weekStartStr = formatDate(weekStart);\n        console.log('📅 جلب الجدول الأسبوعي لتاريخ:', weekStartStr);\n        // الحصول على الجدول أو إنشاء جدول فارغ\n        let scheduleItems = weeklySchedules.get(weekStartStr) || [];\n        // الحصول على المواد المؤقتة المحفوظة\n        let weekTempItems = tempItems.get(weekStartStr) || [];\n        console.log(`📦 المواد المؤقتة المحفوظة: ${weekTempItems.length}`);\n        // توليد الإعادات التلقائية\n        console.log('🚀 بدء توليد الإعادات...');\n        const reruns = generateReruns(scheduleItems, weekStartStr, weekTempItems);\n        console.log(`📊 تم توليد ${reruns.length} إعادة`);\n        // فلترة الإعادات المحذوفة\n        const deletedSet = deletedReruns.get(weekStartStr) || new Set();\n        // فصل الإعادات حسب الأسبوع وفلترة المحذوفة\n        const currentWeekReruns = reruns.filter((rerun)=>{\n            if (rerun.weekStart !== weekStartStr) return false;\n            if (deletedSet.has(rerun.id)) return false;\n            // فلترة إضافية: التحقق من عدم وجود مادة أخرى في نفس المكان\n            const hasConflict = [\n                ...scheduleItems,\n                ...weekTempItems\n            ].some((item)=>!item.isRerun && item.dayOfWeek === rerun.dayOfWeek && item.startTime === rerun.startTime);\n            if (hasConflict) {\n                console.log(`🚫 تم تجاهل إعادة ${rerun.mediaItem.name} في ${rerun.startTime} بسبب وجود مادة أخرى`);\n                return false;\n            }\n            return true;\n        });\n        // إضافة إعادات السبت من الأسبوع السابق (تظهر في الأحد)\n        const prevWeekDate = new Date(weekStartStr);\n        prevWeekDate.setDate(prevWeekDate.getDate() - 7);\n        const prevWeekStr = formatDate(prevWeekDate);\n        // توليد إعادات الأسبوع السابق للحصول على إعادات السبت\n        const prevWeekScheduleItems = weeklySchedules.get(prevWeekStr) || [];\n        const prevWeekTempItems = tempItems.get(prevWeekStr) || [];\n        // التحقق من وجود مواد في السبت من الأسبوع السابق\n        const saturdayItems = [\n            ...prevWeekScheduleItems,\n            ...prevWeekTempItems\n        ].filter((item)=>!item.isRerun && item.dayOfWeek === 6);\n        let saturdayReruns = [];\n        if (saturdayItems.length > 0) {\n            console.log(`📅 وجدت ${saturdayItems.length} مادة في السبت من الأسبوع السابق`);\n            const prevWeekReruns = generateReruns(prevWeekScheduleItems, prevWeekStr, prevWeekTempItems);\n            // فلترة إعادات السبت التي تذهب للأسبوع التالي (الأحد)\n            saturdayReruns = prevWeekReruns.filter((rerun)=>rerun.isNextWeekRerun && rerun.weekStart === weekStartStr && rerun.dayOfWeek === 0 && !deletedSet.has(rerun.id));\n            console.log(`📅 إعادات السبت من الأسبوع السابق: ${saturdayReruns.length}`);\n        } else {\n            console.log(`📅 لا توجد مواد في السبت من الأسبوع السابق - لن يتم توليد إعادات`);\n        }\n        // دمج جميع المواد: عادية + مؤقتة + إعادات\n        const allItems = [\n            ...scheduleItems,\n            ...weekTempItems,\n            ...currentWeekReruns,\n            ...saturdayReruns\n        ];\n        // فلترة المواد الكبيرة للعرض مع إضافة المدة المحسوبة\n        const mediaItems = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getAllMediaItems)();\n        const bigMediaTypes = [\n            'PROGRAM',\n            'SERIES',\n            'FILM'\n        ];\n        const availableMedia = mediaItems.filter((item)=>{\n            // التحقق من نوع المادة\n            const isValidType = bigMediaTypes.includes(item.type);\n            if (!isValidType) return false;\n            // التحقق من خاصية TX\n            const showInTX = item.showInTX === true;\n            if (!showInTX) {\n                console.log(`⚠️ المادة ${item.name} (${item.id}) غير مرئية في TX`);\n                return false;\n            }\n            // التحقق من قائمة المحذوفات الدائمة\n            const permanentDeletedSet = deletedReruns.get('permanent_temp_deletions') || new Set();\n            if (permanentDeletedSet.has(item.id)) {\n                console.log(`🚫 المادة ${item.name} (${item.id}) موجودة في قائمة المحذوفات الدائمة`);\n                return false;\n            }\n            // التحقق من الاسم في قائمة المحذوفات\n            if (permanentDeletedSet.has(`name_${item.name}`)) {\n                console.log(`🚫 المادة ${item.name} موجودة في قائمة المحذوفات الدائمة (بالاسم)`);\n                return false;\n            }\n            console.log(`✅ المادة ${item.name} (${item.id}) متاحة للخريطة`);\n            return true;\n        }).map((item)=>({\n                ...item,\n                duration: calculateTotalDuration(item.segments || [])\n            }));\n        // إضافة المواد المؤقتة للاستجابة (فقط المواد في القائمة الجانبية، ليس في الجدول)\n        const currentTempItems = tempItems.get(weekStartStr) || [];\n        // فلترة المواد المؤقتة المحذوفة\n        const permanentDeletedSet = deletedReruns.get('permanent_temp_deletions') || new Set();\n        const sidebarTempItems = currentTempItems.filter((item)=>{\n            // فقط المواد في القائمة الجانبية (بدون يوم أو وقت)\n            if (item.dayOfWeek !== undefined || item.startTime || item.endTime) {\n                return false;\n            }\n            // استبعاد المواد المحذوفة بالمعرف\n            if (permanentDeletedSet.has(item.id) || permanentDeletedSet.has(item.mediaItemId)) {\n                console.log(`🚫 استبعاد مادة مؤقتة محذوفة من القائمة الجانبية: ${item.name || 'غير معروف'} (${item.id})`);\n                return false;\n            }\n            // استبعاد المواد المحذوفة بالاسم\n            if (permanentDeletedSet.has(`name_${item.name}`)) {\n                console.log(`🚫 استبعاد مادة مؤقتة محذوفة من القائمة الجانبية بالاسم: ${item.name}`);\n                return false;\n            }\n            return true;\n        });\n        console.log(`📦 إرسال ${sidebarTempItems.length} مادة مؤقتة في الاستجابة (من أصل ${currentTempItems.length})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                weekStart: weekStartStr,\n                scheduleItems: allItems,\n                availableMedia,\n                tempItems: sidebarTempItems\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب الجدول:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الجدول الأسبوعي'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مادة للجدول أو حفظ مادة مؤقتة\nasync function POST(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const body = await request.json();\n        const { mediaItemId, dayOfWeek, startTime, endTime, weekStart, episodeNumber, seasonNumber, partNumber, isTemporary, mediaItem: tempMediaItem } = body;\n        // التعامل مع المواد المؤقتة\n        if (isTemporary && tempMediaItem) {\n            // تنظيف وتصحيح البيانات\n            const cleanTempItem = {\n                ...tempMediaItem,\n                name: tempMediaItem.name || tempMediaItem.title || 'مادة مؤقتة',\n                id: tempMediaItem.id || `temp_${Date.now()}`,\n                type: tempMediaItem.type || 'PROGRAM',\n                duration: tempMediaItem.duration || '01:00:00'\n            };\n            console.log('💾 حفظ مادة مؤقتة في API:', cleanTempItem.name);\n            console.log('📋 بيانات المادة المؤقتة المنظفة:', JSON.stringify(cleanTempItem, null, 2));\n            // الحصول على المواد المؤقتة الحالية\n            let currentTempItems = tempItems.get(weekStart) || [];\n            // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها\n            const tempItemsToRemove = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            const regularItemsToRemove = scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n            // حذف الإعادات المرتبطة بجميع المواد التي سيتم استبدالها\n            const allItemsToRemove = [\n                ...tempItemsToRemove,\n                ...regularItemsToRemove\n            ];\n            for (const itemToRemove of allItemsToRemove){\n                if (!deletedReruns.has(weekStart)) {\n                    deletedReruns.set(weekStart, new Set());\n                }\n                const deletedSet = deletedReruns.get(weekStart);\n                // البحث عن جميع الإعادات المرتبطة بهذه المادة\n                const allItems = [\n                    ...scheduleItems,\n                    ...currentTempItems\n                ];\n                const relatedReruns = allItems.filter((item)=>item.isRerun && (item.mediaItemId === itemToRemove.mediaItemId || item.id.includes(itemToRemove.id) || item.originalStartTime === itemToRemove.startTime));\n                relatedReruns.forEach((rerun)=>deletedSet.add(rerun.id));\n                console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name || 'مؤقتة'}`);\n            }\n            // حذف أي مواد موجودة في نفس الوقت والمكان\n            currentTempItems = currentTempItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n            // حذف المواد العادية من نفس الوقت والمكان\n            scheduleItems = scheduleItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n            weeklySchedules.set(weekStart, scheduleItems);\n            console.log(`🔄 تم تنظيف المكان للمادة المؤقتة في اليوم ${dayOfWeek} الوقت ${startTime}`);\n            // إضافة المادة المؤقتة الجديدة مع معرف فريد\n            const tempItem = {\n                id: `temp_${Date.now()}`,\n                mediaItemId: cleanTempItem.id,\n                dayOfWeek,\n                startTime,\n                endTime,\n                weekStart,\n                isRerun: false,\n                isTemporary: true,\n                mediaItem: cleanTempItem,\n                createdAt: new Date().toISOString()\n            };\n            console.log('✅ تم إنشاء المادة المؤقتة:', {\n                id: tempItem.id,\n                name: tempItem.mediaItem.name,\n                type: tempItem.mediaItem.type,\n                duration: tempItem.mediaItem.duration\n            });\n            currentTempItems.push(tempItem);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملفات\n            await saveTempItemsToFile();\n            await saveSchedulesToFile();\n            console.log(`✅ تم حفظ المادة المؤقتة. إجمالي المواد المؤقتة: ${currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: tempItem\n            });\n        }\n        if (!mediaItemId || dayOfWeek === undefined || !startTime || !endTime || !weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'جميع البيانات مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من وجود المادة\n        const mediaItem = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getMediaItemById)(mediaItemId);\n        if (!mediaItem) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المادة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        // الحصول على الجدول الحالي\n        let scheduleItems = weeklySchedules.get(weekStart) || [];\n        // التحقق من التداخل\n        const startMinutes = timeToMinutes(startTime);\n        const endMinutes = timeToMinutes(endTime);\n        const conflict = scheduleItems.find((item)=>{\n            if (item.dayOfWeek !== dayOfWeek) return false;\n            const itemStart = timeToMinutes(item.startTime);\n            const itemEnd = timeToMinutes(item.endTime);\n            return startMinutes < itemEnd && endMinutes > itemStart;\n        });\n        if (conflict) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'يوجد تداخل في الأوقات'\n            }, {\n                status: 400\n            });\n        }\n        // البحث عن المواد الموجودة في نفس الوقت والمكان لحذف إعاداتها\n        const itemsToRemove = scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        // حذف الإعادات المرتبطة بالمواد التي سيتم استبدالها\n        for (const itemToRemove of itemsToRemove){\n            // إضافة جميع الإعادات المرتبطة بهذه المادة للقائمة المحذوفة\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            const deletedSet = deletedReruns.get(weekStart);\n            // البحث عن جميع الإعادات المرتبطة بهذه المادة\n            const allItems = [\n                ...scheduleItems,\n                ...tempItems.get(weekStart) || []\n            ];\n            const relatedReruns = allItems.filter((item)=>item.isRerun && (item.mediaItemId === itemToRemove.mediaItemId || item.id.includes(itemToRemove.id) || item.originalStartTime === itemToRemove.startTime));\n            relatedReruns.forEach((rerun)=>deletedSet.add(rerun.id));\n            console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة ${itemToRemove.mediaItem?.name}`);\n        }\n        // حذف أي مواد موجودة في نفس الوقت والمكان (بما في ذلك المواد المؤقتة)\n        scheduleItems = scheduleItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        // حذف المواد المؤقتة من نفس الوقت والمكان\n        let currentTempItems = tempItems.get(weekStart) || [];\n        const tempItemsToRemove = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        console.log(`🔍 البحث عن مواد مؤقتة للحذف في اليوم ${dayOfWeek} الوقت ${startTime}: وجد ${tempItemsToRemove.length} مادة`);\n        // حذف إعادات المواد المؤقتة أيضاً\n        for (const tempItem of tempItemsToRemove){\n            console.log(`🗑️ حذف المادة المؤقتة: ${tempItem.mediaItem?.name} (ID: ${tempItem.id})`);\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            // البحث عن إعادات المادة المؤقتة في جميع الأسابيع وحذفها نهائياً\n            const allWeeks = Array.from(weeklySchedules.keys());\n            for (const week of allWeeks){\n                const weekItems = weeklySchedules.get(week) || [];\n                const relatedReruns = weekItems.filter((item)=>item.isRerun && item.isTemporary && (item.mediaItemId === tempItem.mediaItemId || item.id.includes(tempItem.id) || item.originalId === tempItem.id || item.mediaItem && item.mediaItem.name === tempItem.mediaItem?.name));\n                // إضافة الإعادات للقائمة المحذوفة\n                relatedReruns.forEach((rerun)=>{\n                    if (!deletedReruns.has(week)) {\n                        deletedReruns.set(week, new Set());\n                    }\n                    deletedReruns.get(week).add(rerun.id);\n                });\n                // حذف الإعادات من الجدول نهائياً\n                const updatedWeekItems = weekItems.filter((item)=>!relatedReruns.some((rerun)=>rerun.id === item.id));\n                weeklySchedules.set(week, updatedWeekItems);\n                console.log(`🗑️ تم حذف ${relatedReruns.length} إعادة مرتبطة بالمادة المؤقتة ${tempItem.mediaItem?.name} في الأسبوع ${week} نهائياً`);\n            }\n            // إضافة معرف المادة المؤقتة لقائمة المحذوفات الدائمة\n            if (!deletedReruns.has('permanent_temp_deletions')) {\n                deletedReruns.set('permanent_temp_deletions', new Set());\n            }\n            deletedReruns.get('permanent_temp_deletions').add(tempItem.mediaItemId || tempItem.id);\n        }\n        // حذف المواد المؤقتة من tempItems نهائياً\n        const beforeCount = currentTempItems.length;\n        const tempItemsToDelete = currentTempItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime);\n        // طباعة تفاصيل المواد التي سيتم حذفها\n        tempItemsToDelete.forEach((item)=>{\n            console.log(`🗑️ سيتم حذف المادة المؤقتة نهائياً: ${item.mediaItem?.name} (ID: ${item.id})`);\n        });\n        currentTempItems = currentTempItems.filter((item)=>!(item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        tempItems.set(weekStart, currentTempItems);\n        // حذف المواد المؤقتة من weeklySchedules أيضاً\n        scheduleItems = scheduleItems.filter((item)=>!(item.isTemporary && item.dayOfWeek === dayOfWeek && item.startTime === startTime));\n        weeklySchedules.set(weekStart, scheduleItems);\n        // حذف المواد المؤقتة من جميع الأسابيع في weeklySchedules\n        const allWeeks = Array.from(weeklySchedules.keys());\n        for (const week of allWeeks){\n            let weekItems = weeklySchedules.get(week) || [];\n            const beforeWeekCount = weekItems.length;\n            // حذف المواد المؤقتة والإعادات المرتبطة بها\n            weekItems = weekItems.filter((item)=>{\n                // حذف المواد المؤقتة التي تطابق المواد المحذوفة\n                if (item.isTemporary) {\n                    const shouldDelete = tempItemsToDelete.some((deletedItem)=>item.mediaItem?.name === deletedItem.mediaItem?.name && item.dayOfWeek === deletedItem.dayOfWeek && item.startTime === deletedItem.startTime || item.id === deletedItem.id || item.mediaItemId === deletedItem.mediaItemId);\n                    if (shouldDelete) {\n                        console.log(`🗑️ حذف مادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);\n                        return false;\n                    }\n                }\n                // حذف الإعادات المرتبطة بالمواد المؤقتة المحذوفة\n                if (item.isRerun && item.isTemporary) {\n                    const shouldDelete = tempItemsToDelete.some((deletedItem)=>item.mediaItem?.name === deletedItem.mediaItem?.name || item.originalId === deletedItem.id || item.mediaItemId === deletedItem.mediaItemId);\n                    if (shouldDelete) {\n                        console.log(`🗑️ حذف إعادة مؤقتة من weeklySchedules: ${item.mediaItem?.name} (${item.startTime})`);\n                        return false;\n                    }\n                }\n                return true;\n            });\n            weeklySchedules.set(week, weekItems);\n            if (beforeWeekCount !== weekItems.length) {\n                console.log(`🗑️ تم حذف ${beforeWeekCount - weekItems.length} مادة/إعادة مؤقتة من الأسبوع ${week}`);\n            }\n        }\n        console.log(`🗑️ تم حذف ${beforeCount - currentTempItems.length} مادة مؤقتة من tempItems نهائياً`);\n        // حفظ المواد المؤقتة المحدثة\n        await saveTempItemsToFile();\n        await saveSchedulesToFile();\n        console.log(`💾 تم حفظ الملفات بعد حذف المواد المؤقتة. المواد المتبقية: ${currentTempItems.length}`);\n        console.log(`🔄 تم تنظيف المكان في اليوم ${dayOfWeek} الوقت ${startTime}`);\n        // إضافة المادة الجديدة مع تفاصيل الحلقة/الجزء\n        const newItem = {\n            id: `schedule_${Date.now()}`,\n            mediaItemId,\n            dayOfWeek,\n            startTime,\n            endTime,\n            weekStart,\n            isRerun: false,\n            // حفظ تفاصيل الحلقة/الجزء على مستوى العنصر\n            episodeNumber,\n            seasonNumber,\n            partNumber,\n            // إنشاء نسخة محدثة من المادة مع التفاصيل\n            mediaItem: {\n                ...mediaItem,\n                episodeNumber: episodeNumber || mediaItem.episodeNumber,\n                seasonNumber: seasonNumber || mediaItem.seasonNumber,\n                partNumber: partNumber || mediaItem.partNumber\n            },\n            createdAt: new Date().toISOString()\n        };\n        scheduleItems.push(newItem);\n        weeklySchedules.set(weekStart, scheduleItems);\n        // حفظ في الملف\n        await saveSchedulesToFile();\n        console.log('✅ تم إضافة المادة:', mediaItem.name);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: newItem\n        });\n    } catch (error) {\n        console.error('❌ خطأ في إضافة المادة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في إضافة المادة'\n        }, {\n            status: 500\n        });\n    }\n}\n// دعم إضافي للمواد المؤقتة في القائمة الجانبية\nasync function PATCH(request) {\n    try {\n        await initializeData();\n        const body = await request.json();\n        const { action } = body;\n        // حفظ مادة مؤقتة في القائمة الجانبية\n        if (action === 'saveTempToSidebar') {\n            const { tempMedia, weekStart: weekStartParam } = body;\n            const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));\n            console.log(`💾 حفظ مادة مؤقتة في القائمة الجانبية: ${tempMedia.name}`);\n            // إضافة المادة المؤقتة إلى tempItems\n            let currentTempItems = tempItems.get(weekStart) || [];\n            currentTempItems.push(tempMedia);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملف\n            await saveTempItemsToFile();\n            console.log(`✅ تم حفظ المادة المؤقتة في القائمة الجانبية. إجمالي: ${currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حفظ المادة المؤقتة في القائمة الجانبية'\n            });\n        }\n        // حذف مادة مؤقتة من القائمة الجانبية\n        if (action === 'deleteTempFromSidebar') {\n            const { tempMediaId, weekStart: weekStartParam } = body;\n            const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));\n            console.log(`🗑️ حذف مادة مؤقتة من القائمة الجانبية: ${tempMediaId}`);\n            // حذف المادة المؤقتة من tempItems\n            let currentTempItems = tempItems.get(weekStart) || [];\n            const beforeCount = currentTempItems.length;\n            currentTempItems = currentTempItems.filter((item)=>item.id !== tempMediaId);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ في الملف\n            await saveTempItemsToFile();\n            console.log(`✅ تم حذف المادة المؤقتة من القائمة الجانبية. المحذوف: ${beforeCount - currentTempItems.length}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف المادة المؤقتة من القائمة الجانبية'\n            });\n        }\n        // حذف مادة مؤقتة من الجدول\n        if (action === 'deleteTempItem') {\n            const { tempItemId, mediaItemId, dayOfWeek, startTime, endTime, mediaItemName, weekStart: weekStartParam } = body;\n            const weekStart = weekStartParam || formatDate(getWeekStart(new Date()));\n            console.log(`🗑️ بدء حذف المادة المؤقتة من الجدول:`, {\n                tempItemId,\n                mediaItemId,\n                dayOfWeek,\n                startTime,\n                endTime,\n                mediaItemName,\n                weekStart\n            });\n            // حذف المادة من الجدول الأسبوعي\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            // البحث عن المادة بعدة طرق\n            let itemToDelete = scheduleItems.find((item)=>item.id === tempItemId);\n            // إذا لم يتم العثور على المادة بالمعرف، نبحث بمعايير أخرى\n            if (!itemToDelete && mediaItemId) {\n                console.log(`🔍 البحث عن المادة بواسطة mediaItemId: ${mediaItemId}`);\n                itemToDelete = scheduleItems.find((item)=>item.mediaItemId === mediaItemId && item.isTemporary === true);\n            }\n            // البحث بواسطة اليوم والوقت\n            if (!itemToDelete && dayOfWeek !== undefined && startTime) {\n                console.log(`🔍 البحث عن المادة بواسطة اليوم والوقت: يوم ${dayOfWeek} وقت ${startTime}`);\n                itemToDelete = scheduleItems.find((item)=>item.dayOfWeek === dayOfWeek && item.startTime === startTime && item.isTemporary === true);\n            }\n            // البحث بواسطة اسم المادة\n            if (!itemToDelete && mediaItemName) {\n                console.log(`🔍 البحث عن المادة بواسطة الاسم: ${mediaItemName}`);\n                itemToDelete = scheduleItems.find((item)=>item.mediaItem?.name === mediaItemName && item.isTemporary === true);\n            }\n            // إذا لم يتم العثور على المادة بأي طريقة، نقوم بحذف أي مادة مؤقتة في نفس اليوم والوقت\n            if (!itemToDelete && dayOfWeek !== undefined && startTime) {\n                console.log(`⚠️ لم يتم العثور على المادة المؤقتة المحددة، سنحذف أي مادة مؤقتة في نفس اليوم والوقت`);\n                // إنشاء كائن افتراضي للحذف\n                itemToDelete = {\n                    id: tempItemId || `temp_${Date.now()}`,\n                    mediaItemId: mediaItemId || `media_${Date.now()}`,\n                    dayOfWeek,\n                    startTime,\n                    endTime: endTime || addMinutesToTime(startTime, 60),\n                    isTemporary: true,\n                    mediaItem: {\n                        name: mediaItemName || 'مادة مؤقتة'\n                    }\n                };\n                console.log(`🔧 تم إنشاء كائن افتراضي للحذف:`, itemToDelete);\n            }\n            if (!itemToDelete) {\n                console.log(`⚠️ لم يتم العثور على المادة المؤقتة في الجدول بأي طريقة`);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'لم يتم العثور على المادة المؤقتة في الجدول'\n                }, {\n                    status: 404\n                });\n            }\n            console.log(`✅ تم العثور على المادة المؤقتة: ${itemToDelete.mediaItem?.name || 'غير معروف'}`);\n            // حذف المادة وجميع إعاداتها\n            const updatedScheduleItems = scheduleItems.filter((item)=>{\n                // حذف المادة نفسها\n                if (item.id === tempItemId) {\n                    console.log(`🗑️ حذف المادة المؤقتة الأصلية: ${item.id}`);\n                    return false;\n                }\n                // حذف المواد المؤقتة في نفس اليوم والوقت\n                if (item.isTemporary && item.dayOfWeek === itemToDelete.dayOfWeek && item.startTime === itemToDelete.startTime) {\n                    console.log(`🗑️ حذف مادة مؤقتة في نفس الموقع: ${item.id}`);\n                    return false;\n                }\n                // حذف إعادات المادة\n                if (item.isRerun && (item.originalId === tempItemId || item.mediaItemId === itemToDelete.mediaItemId || item.mediaItem && itemToDelete.mediaItem && item.mediaItem.name === itemToDelete.mediaItem.name || item.isTemporary && item.dayOfWeek === itemToDelete.dayOfWeek)) {\n                    console.log(`🗑️ حذف إعادة للمادة المؤقتة: ${item.id}`);\n                    return false;\n                }\n                return true;\n            });\n            // حفظ التغييرات\n            weeklySchedules.set(weekStart, updatedScheduleItems);\n            // إضافة المادة إلى قائمة المحذوفات الدائمة\n            if (!deletedReruns.has('permanent_temp_deletions')) {\n                deletedReruns.set('permanent_temp_deletions', new Set());\n            }\n            // إضافة جميع المعرفات المحتملة للمادة المؤقتة\n            const permanentDeletions = deletedReruns.get('permanent_temp_deletions');\n            permanentDeletions.add(itemToDelete.mediaItemId || itemToDelete.id);\n            permanentDeletions.add(tempItemId);\n            if (itemToDelete.mediaItem && itemToDelete.mediaItem.name) {\n                // إضافة اسم المادة كمعرف إضافي للحذف\n                permanentDeletions.add(`name_${itemToDelete.mediaItem.name}`);\n            }\n            if (dayOfWeek !== undefined && startTime) {\n                // إضافة موقع المادة كمعرف إضافي للحذف\n                permanentDeletions.add(`pos_${dayOfWeek}_${startTime}`);\n            }\n            console.log(`✅ تمت إضافة المادة المؤقتة إلى قائمة المحذوفات الدائمة`);\n            // حذف من المواد المؤقتة أيضًا إذا كانت موجودة هناك\n            let currentTempItems = tempItems.get(weekStart) || [];\n            const originalTempCount = currentTempItems.length;\n            currentTempItems = currentTempItems.filter((item)=>{\n                // حذف بالمعرف\n                if (item.id === tempItemId || item.mediaItemId === itemToDelete.mediaItemId) {\n                    console.log(`🗑️ حذف المادة المؤقتة من القائمة الجانبية: ${item.id}`);\n                    return false;\n                }\n                // حذف بالاسم\n                if (itemToDelete.mediaItem && item.name === itemToDelete.mediaItem.name) {\n                    console.log(`🗑️ حذف المادة المؤقتة من القائمة الجانبية بالاسم: ${item.name}`);\n                    return false;\n                }\n                // حذف بالموقع (إذا كانت في الجدول)\n                if (item.dayOfWeek === itemToDelete.dayOfWeek && item.startTime === itemToDelete.startTime) {\n                    console.log(`🗑️ حذف المادة المؤقتة من القائمة الجانبية بالموقع: يوم ${item.dayOfWeek} وقت ${item.startTime}`);\n                    return false;\n                }\n                return true;\n            });\n            console.log(`🗑️ تم حذف ${originalTempCount - currentTempItems.length} مادة مؤقتة من القائمة الجانبية`);\n            tempItems.set(weekStart, currentTempItems);\n            // حفظ التغييرات\n            await saveSchedulesToFile();\n            await saveTempItemsToFile();\n            console.log(`✅ تم حذف المادة المؤقتة ${tempItemId} وإعاداتها بنجاح`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف المادة المؤقتة وإعاداتها بنجاح'\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'إجراء غير مدعوم'\n        }, {\n            status: 400\n        });\n    } catch (error) {\n        console.error('❌ خطأ في PATCH:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في العملية'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مادة\nasync function DELETE(request) {\n    try {\n        // تحميل البيانات من الملفات\n        await initializeData();\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        const weekStart = searchParams.get('weekStart');\n        if (!id || !weekStart) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المعرف والتاريخ مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق إذا كانت المادة مؤقتة\n        if (id.startsWith('temp_')) {\n            console.log('🗑️ حذف مادة مؤقتة:', id);\n            // حذف من المواد المؤقتة\n            let currentTempItems = tempItems.get(weekStart) || [];\n            const originalLength = currentTempItems.length;\n            currentTempItems = currentTempItems.filter((item)=>item.id !== id && item.originalId !== id);\n            tempItems.set(weekStart, currentTempItems);\n            // حذف من الجدول الأساسي أيضاً\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            scheduleItems = scheduleItems.filter((item)=>item.id !== id && item.mediaItemId !== id && !(item.mediaItemId && item.mediaItemId.startsWith('temp_') && item.mediaItemId === id));\n            weeklySchedules.set(weekStart, scheduleItems);\n            console.log(`🗑️ حذف المادة المؤقتة من tempItems: ${id}`);\n            console.log(`🗑️ حذف المادة المؤقتة من weeklySchedules: ${id}`);\n            // حفظ في الملفات\n            await saveTempItemsToFile();\n            await saveSchedulesToFile();\n            console.log(`✅ تم حذف المادة المؤقتة نهائياً. المواد المتبقية: ${currentTempItems.length} (كان ${originalLength})`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true\n            });\n        }\n        // التحقق إذا كانت المادة إعادة\n        if (id.startsWith('rerun_')) {\n            // إضافة الإعادة للقائمة المحذوفة\n            if (!deletedReruns.has(weekStart)) {\n                deletedReruns.set(weekStart, new Set());\n            }\n            deletedReruns.get(weekStart).add(id);\n            console.log('🗑️ تم حذف الإعادة (ترك فارغ):', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف الإعادة بنجاح'\n            });\n        } else {\n            // حذف المادة الأصلية\n            let scheduleItems = weeklySchedules.get(weekStart) || [];\n            const index = scheduleItems.findIndex((item)=>item.id === id);\n            if (index === -1) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'المادة غير موجودة'\n                }, {\n                    status: 404\n                });\n            }\n            scheduleItems.splice(index, 1);\n            weeklySchedules.set(weekStart, scheduleItems);\n            // حفظ في الملف\n            await saveSchedulesToFile();\n            console.log('🗑️ تم حذف المادة الأصلية:', id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'تم حذف المادة بنجاح'\n            });\n        }\n    } catch (error) {\n        console.error('❌ خطأ في حذف المادة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف المادة'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/weekly-schedule/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fweekly-schedule%2Froute&page=%2Fapi%2Fweekly-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fweekly-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();