'use client';

import React from 'react';
import { useTranslation } from 'react-i18next';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();

  const toggleLanguage = () => {
    const newLang = i18n.language === 'ar' ? 'en' : 'ar';

    // Show loading state
    const button = document.querySelector('.language-switcher') as HTMLElement;
    if (button) {
      button.style.opacity = '0.6';
      button.style.pointerEvents = 'none';
    }

    i18n.changeLanguage(newLang);

    // Force page reload to ensure all components update
    setTimeout(() => {
      window.location.reload();
    }, 200);
  };

  const currentLang = i18n.language || 'ar';
  const isArabic = currentLang === 'ar';

  return (
    <button
      onClick={toggleLanguage}
      className="language-switcher"
      title={isArabic ? 'Switch to English' : 'التبديل إلى العربية'}
    >
      <span className="flag">{isArabic ? '🇺🇸' : '🇸🇦'}</span>
      <span className="lang-text">{isArabic ? 'EN' : 'عر'}</span>
      
      <style jsx>{`
        .language-switcher {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 10px 14px;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 8px;
          color: white;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          font-size: 14px;
          font-weight: 600;
          backdrop-filter: blur(10px);
          -webkit-backdrop-filter: blur(10px);
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          position: relative;
          overflow: hidden;
        }

        .language-switcher::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        .language-switcher:hover {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
          border-color: rgba(255, 255, 255, 0.4);
          transform: translateY(-2px) scale(1.05);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .language-switcher:hover::before {
          left: 100%;
        }

        .language-switcher:active {
          transform: translateY(-1px) scale(1.02);
        }

        .flag {
          font-size: 18px;
          filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
          transition: transform 0.3s ease;
        }

        .language-switcher:hover .flag {
          transform: scale(1.1);
        }

        .lang-text {
          font-size: 13px;
          font-weight: bold;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          letter-spacing: 0.5px;
        }
      `}</style>
    </button>
  );
};

export default LanguageSwitcher;
