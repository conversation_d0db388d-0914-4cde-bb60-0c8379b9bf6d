"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_app_daily-schedule_import-list_tsx",{

/***/ "(app-pages-browser)/./src/app/daily-schedule/import-list.tsx":
/*!************************************************!*\
  !*** ./src/app/daily-schedule/import-list.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var _daily_schedule_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./daily-schedule.css */ \"(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction DailySchedulePage() {\n    _s();\n    const { user, isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_6__.useTranslatedToast)();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [startTime, setStartTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('08:00:00');\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [gridRows, setGridRows] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('ALL');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isImported, setIsImported] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false); // حالة الاستيراد\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [showWeeklySchedule, setShowWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // تهيئة التاريخ الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            setSelectedDate(today.toISOString().split('T')[0]);\n        }\n    }[\"DailySchedulePage.useEffect\"], []);\n    // لا نجلب البيانات تلقائياً - فقط عند الاستيراد\n    // useEffect(() => {\n    //   if (selectedDate) {\n    //     fetchScheduleData();\n    //   }\n    // }, [selectedDate]);\n    // جلب بيانات الجدول الإذاعي\n    const fetchScheduleData = async ()=>{\n        setLoading(true);\n        try {\n            console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);\n            console.log('🌐 URL المطلوب:', \"/api/daily-schedule?date=\".concat(selectedDate));\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة:', data);\n            if (data.success) {\n                var _data_data_scheduleItems, _data_data_availableMedia, _data_data_scheduleRows, _data_data_availableMedia1, _data_data_availableMedia2;\n                console.log('✅ استجابة ناجحة من API:', {\n                    scheduleItemsCount: ((_data_data_scheduleItems = data.data.scheduleItems) === null || _data_data_scheduleItems === void 0 ? void 0 : _data_data_scheduleItems.length) || 0,\n                    availableMediaCount: ((_data_data_availableMedia = data.data.availableMedia) === null || _data_data_availableMedia === void 0 ? void 0 : _data_data_availableMedia.length) || 0,\n                    scheduleRowsCount: ((_data_data_scheduleRows = data.data.scheduleRows) === null || _data_data_scheduleRows === void 0 ? void 0 : _data_data_scheduleRows.length) || 0,\n                    date: data.data.date,\n                    dayOfWeek: data.data.dayOfWeek\n                });\n                setScheduleItems(data.data.scheduleItems);\n                setAvailableMedia(data.data.availableMedia || []);\n                console.log(\"\\uD83D\\uDCDA تم تحديد \".concat(((_data_data_availableMedia1 = data.data.availableMedia) === null || _data_data_availableMedia1 === void 0 ? void 0 : _data_data_availableMedia1.length) || 0, \" مادة متاحة في القائمة الجانبية\"));\n                // التحقق من وجود صفوف في الجدول\n                if (data.data.scheduleRows && data.data.scheduleRows.length > 0) {\n                    // تطبيق الحماية على المواد الأساسية المحفوظة\n                    const protectedRows = data.data.scheduleRows.map((row)=>{\n                        if (row.type === 'segment' && !row.isTemporary) {\n                            return {\n                                ...row,\n                                canDelete: false\n                            }; // حماية المواد الأساسية\n                        }\n                        return row;\n                    });\n                    setGridRows(protectedRows);\n                    console.log('📝 تم تحميل', protectedRows.length, 'صف من الجدول المحفوظ مع تطبيق الحماية');\n                } else {\n                    // إذا لم تكن هناك صفوف، نحاول بناء الجدول من الخريطة البرامجية\n                    console.log('⚠️ لم يتم العثور على جدول محفوظ، سيتم محاولة بناء الجدول من الخريطة البرامجية');\n                    await buildScheduleFromWeekly();\n                }\n                if (data.fromSavedFile) {\n                    console.log('📂 تم تحميل جدول محفوظ مسبقاً');\n                    console.log('💾 تاريخ الحفظ:', data.savedAt);\n                    console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);\n                } else {\n                    console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');\n                    console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');\n                }\n                console.log('📦 Available media:', ((_data_data_availableMedia2 = data.data.availableMedia) === null || _data_data_availableMedia2 === void 0 ? void 0 : _data_data_availableMedia2.length) || 0);\n                // عرض عينة من المواد المتاحة\n                if (data.data.availableMedia && data.data.availableMedia.length > 0) {\n                    console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));\n                } else {\n                    console.log('⚠️ لا توجد مواد متاحة في القائمة الجانبية');\n                }\n            } else {\n                console.log('❌ فشل في جلب البيانات:', data.error || 'خطأ غير محدد');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n            // جلب الجدول الأسبوعي للمراجعة\n            await fetchWeeklySchedule();\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        } finally{\n            setLoading(false);\n            console.log('✅ انتهى تحميل البيانات');\n        }\n    };\n    // البحث عن الاستنجات المطابقة لمادة معينة\n    const findMatchingStings = (mediaName, stingType)=>{\n        return mediaItems.filter((item)=>item.name === mediaName && item.type === stingType && item.status === 'VALID' && item.tx === true);\n    };\n    // إضافة استنجات تلقائية بين السيجمنت\n    const addAutomaticStings = (scheduleRows, mediaName)=>{\n        console.log(\"\\uD83D\\uDD0D البحث عن استنجات للمادة: \".concat(mediaName));\n        // البحث عن استنجات \"سنعود\" و \"عدنا\" لهذه المادة\n        const snawodStings = findMatchingStings(mediaName, 'سنعود');\n        const odnaStings = findMatchingStings(mediaName, 'عدنا');\n        console.log(\"\\uD83D\\uDCE6 وجدت \".concat(snawodStings.length, ' استنج \"سنعود\" و ').concat(odnaStings.length, ' استنج \"عدنا\" للمادة: ').concat(mediaName));\n        if (snawodStings.length === 0 && odnaStings.length === 0) {\n            console.log(\"⚠️ لا توجد استنجات للمادة: \".concat(mediaName));\n            return scheduleRows; // لا توجد استنجات متطابقة\n        }\n        // البحث عن المادة الأساسية في الجدول\n        const mediaRows = scheduleRows.map((row, index)=>({\n                ...row,\n                originalIndex: index\n            })).filter((row)=>row.type === 'segment' && row.content && row.content.includes(mediaName) && !row.isAutoGenerated // تجنب المواد المولدة تلقائياً\n        );\n        console.log(\"\\uD83C\\uDFAC وجدت \".concat(mediaRows.length, \" صف للمادة: \").concat(mediaName));\n        if (mediaRows.length <= 1) {\n            console.log(\"⚠️ المادة \".concat(mediaName, \" لها سيجمنت واحد فقط - لا حاجة للاستنجات\"));\n            return scheduleRows; // لا حاجة للاستنجات إذا كان سيجمنت واحد فقط\n        }\n        const newRows = [\n            ...scheduleRows\n        ];\n        const insertions = [];\n        // إضافة استنجات بين كل سيجمنت والتالي\n        for(let i = 0; i < mediaRows.length - 1; i++){\n            const currentRow = mediaRows[i];\n            // إضافة \"سنعود\" بعد السيجمنت الحالي\n            if (snawodStings.length > 0) {\n                const snawodSting = snawodStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex,\n                    sting: {\n                        id: \"auto_snawod_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(snawodSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(snawodSting),\n                        mediaType: 'سنعود',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n            // إضافة \"عدنا\" قبل السيجمنت التالي (بعد \"سنعود\")\n            if (odnaStings.length > 0) {\n                const odnaSting = odnaStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex + (snawodStings.length > 0 ? 1 : 0),\n                    sting: {\n                        id: \"auto_odna_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(odnaSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(odnaSting),\n                        mediaType: 'عدنا',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n        }\n        // إدراج الاستنجات من النهاية للبداية لتجنب تغيير الفهارس\n        insertions.sort((a, b)=>b.afterIndex - a.afterIndex);\n        insertions.forEach((insertion)=>{\n            newRows.splice(insertion.afterIndex + 1, 0, insertion.sting);\n        });\n        console.log(\"✨ تم إضافة \".concat(insertions.length, \" استنج تلقائي للمادة: \").concat(mediaName));\n        return newRows;\n    };\n    // بناء الجدول من الخريطة البرامجية\n    const buildScheduleFromWeekly = async ()=>{\n        try {\n            console.log('🔄 محاولة بناء الجدول من الخريطة البرامجية للتاريخ:', selectedDate);\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            console.log('📅 تفاصيل التاريخ:');\n            console.log('  - التاريخ الأصلي:', selectedDate);\n            console.log('  - التاريخ المحول:', date.toISOString().split('T')[0]);\n            console.log('  - يوم الأسبوع:', date.getDay(), '(0=أحد, 1=اثنين, 2=ثلاثاء, 3=أربعاء, 4=خميس, 5=جمعة, 6=سبت)');\n            console.log('  - بداية الأسبوع:', weekStartStr);\n            console.log('  - نهاية الأسبوع:', new Date(new Date(weekStartStr).getTime() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);\n            console.log('🌐 طلب البيانات من:', \"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status);\n                return;\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة من weekly-schedule:', data);\n            if (data.success && data.data && data.data.scheduleItems) {\n                const dayOfWeek = date.getDay();\n                console.log('📅 اليوم المطلوب:', dayOfWeek);\n                console.log('📦 إجمالي المواد في الخريطة:', data.data.scheduleItems.length);\n                // عرض جميع المواد للتشخيص\n                console.log('🔍 جميع المواد في الخريطة:');\n                data.data.scheduleItems.forEach((item, index)=>{\n                    var _item_mediaItem;\n                    console.log(\"  \".concat(index + 1, \". \").concat(((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime, \" - إعادة: \").concat(item.isRerun));\n                });\n                // فلترة المواد الخاصة بهذا اليوم\n                const dayItems = data.data.scheduleItems.filter((item)=>{\n                    const matches = item.dayOfWeek === dayOfWeek && item.mediaItem;\n                    if (matches) {\n                        var _item_mediaItem;\n                        console.log(\"✅ مادة متطابقة: \".concat((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime));\n                    } else {\n                        var _item_mediaItem1;\n                        console.log(\"❌ مادة غير متطابقة: \".concat(((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" (مطلوب: \").concat(dayOfWeek, \") - mediaItem: \").concat(!!item.mediaItem));\n                    }\n                    return matches;\n                });\n                console.log('📋 عدد المواد المطابقة لهذا اليوم:', dayItems.length);\n                if (dayItems.length > 0) {\n                    // ترتيب المواد حسب وقت البداية\n                    dayItems.sort((a, b)=>a.startTime.localeCompare(b.startTime));\n                    // بناء صفوف الجدول\n                    const newRows = [];\n                    let currentTime = '08:00:00';\n                    // إضافة صف فارغ في البداية\n                    newRows.push({\n                        id: \"empty_start_\".concat(Date.now()),\n                        type: 'empty',\n                        time: currentTime,\n                        canDelete: true\n                    });\n                    // إضافة المواد\n                    for (const item of dayItems){\n                        // إضافة المادة\n                        const mediaItem = item.mediaItem;\n                        const itemName = mediaItem.name;\n                        const itemType = mediaItem.type;\n                        const itemId = mediaItem.id;\n                        // إضافة تفاصيل المادة\n                        const details = [];\n                        if (mediaItem.episodeNumber) details.push(\"ح\".concat(mediaItem.episodeNumber));\n                        if (mediaItem.seasonNumber && mediaItem.seasonNumber > 0) details.push(\"م\".concat(mediaItem.seasonNumber));\n                        if (mediaItem.partNumber) details.push(\"ج\".concat(mediaItem.partNumber));\n                        const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n                        // تحديد نوع المادة\n                        let rowType = 'segment';\n                        let itemContent = \"\".concat(itemName).concat(detailsText);\n                        if ([\n                            'PROMO',\n                            'STING',\n                            'FILLER',\n                            'FILL_IN'\n                        ].includes(itemType)) {\n                            rowType = 'filler';\n                            itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                        }\n                        // حساب المدة\n                        let itemDuration = '00:01:00';\n                        if (mediaItem.duration) {\n                            itemDuration = mediaItem.duration;\n                        } else if (mediaItem.segments && mediaItem.segments.length > 0) {\n                            let totalSeconds = 0;\n                            mediaItem.segments.forEach((segment)=>{\n                                if (segment.duration) {\n                                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                                }\n                            });\n                            if (totalSeconds > 0) {\n                                const hours = Math.floor(totalSeconds / 3600);\n                                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                                const secs = totalSeconds % 60;\n                                itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                            }\n                        }\n                        // إضافة الصف\n                        const newRow = {\n                            id: \"\".concat(rowType, \"_\").concat(Date.now(), \"_\").concat(itemId),\n                            type: rowType,\n                            time: item.startTime,\n                            content: itemContent,\n                            mediaItemId: itemId,\n                            duration: itemDuration,\n                            canDelete: false,\n                            isRerun: item.isRerun,\n                            isTemporary: false,\n                            originalStartTime: item.startTime\n                        };\n                        console.log('🔒 إضافة مادة أساسية محمية:', newRow.content, 'canDelete:', newRow.canDelete);\n                        newRows.push(newRow);\n                        // تحديث الوقت الحالي\n                        currentTime = calculateNextTime(item.startTime, itemDuration);\n                    }\n                    // إضافة الاستنجات التلقائية لكل مادة\n                    let finalRows = [\n                        ...newRows\n                    ];\n                    const processedMediaNames = new Set();\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem;\n                        const mediaName = (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name;\n                        if (mediaName && !processedMediaNames.has(mediaName)) {\n                            processedMediaNames.add(mediaName);\n                            finalRows = addAutomaticStings(finalRows, mediaName);\n                        }\n                    });\n                    // إضافة صفوف فارغة في النهاية\n                    for(let i = 0; i < 8; i++){\n                        finalRows.push({\n                            id: \"empty_end_\".concat(Date.now(), \"_\").concat(i),\n                            type: 'empty',\n                            canDelete: true\n                        });\n                    }\n                    // تحديث الجدول\n                    setGridRows(finalRows);\n                    console.log('✅ تم بناء الجدول من الخريطة البرامجية:', finalRows.length, 'صف');\n                    // إعادة حساب الأوقات\n                    recalculateTimes(finalRows);\n                } else {\n                    console.log('⚠️ لا توجد مواد لهذا اليوم في الخريطة البرامجية');\n                    console.log('💡 تحقق من:');\n                    console.log('  - وجود مواد في الخريطة البرامجية لهذا اليوم');\n                    console.log('  - صحة يوم الأسبوع المحسوب');\n                    console.log('  - وجود mediaItem في كل مادة');\n                }\n            } else {\n                console.log('❌ فشل في جلب بيانات الخريطة البرامجية');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في بناء الجدول من الخريطة البرامجية:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        }\n    };\n    // جلب الجدول الأسبوعي للمراجعة\n    const fetchWeeklySchedule = async ()=>{\n        try {\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const data = await response.json();\n            console.log('📊 استجابة API للجدول الأسبوعي:', data);\n            if (data.success && data.data) {\n                // استخراج مواد اليوم المحدد من scheduleItems\n                const dayOfWeek = new Date(selectedDate).getDay();\n                const daySchedule = [];\n                console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);\n                console.log('📦 البيانات المتاحة:', data.data);\n                // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)\n                if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {\n                    console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);\n                    const dayItems = data.data.scheduleItems.filter((item)=>{\n                        var _item_mediaItem;\n                        console.log('🔍 فحص المادة:', (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);\n                        return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء\n                    }).sort((a, b)=>{\n                        // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59\n                        const timeA = a.startTime;\n                        const timeB = b.startTime;\n                        const getTimeOrder = (time)=>{\n                            const hour = parseInt(time.split(':')[0]);\n                            if (hour >= 8 && hour < 18) return 1; // صباح ومساء\n                            if (hour >= 18) return 2; // برايم تايم\n                            return 3; // منتصف الليل والفجر\n                        };\n                        const orderA = getTimeOrder(timeA);\n                        const orderB = getTimeOrder(timeB);\n                        if (orderA !== orderB) return orderA - orderB;\n                        return timeA.localeCompare(timeB);\n                    });\n                    console.log('✅ مواد اليوم المفلترة:', dayItems.length);\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3;\n                        const scheduleItem = {\n                            time: item.startTime,\n                            name: ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'مادة غير محددة',\n                            episodeNumber: item.episodeNumber || ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.episodeNumber),\n                            partNumber: item.partNumber || ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.partNumber),\n                            seasonNumber: item.seasonNumber || ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.seasonNumber),\n                            isRerun: item.isRerun || false\n                        };\n                        daySchedule.push(scheduleItem);\n                        console.log('📺 إضافة مادة للخريطة:', scheduleItem);\n                    });\n                } else {\n                    console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');\n                }\n                setWeeklySchedule(daySchedule);\n                console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');\n            } else {\n                console.log('❌ فشل في جلب البيانات أو البيانات فارغة');\n                setWeeklySchedule([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);\n            setWeeklySchedule([]);\n        }\n    };\n    // Filter available media\n    const filteredMedia = availableMedia.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesType = filterType === 'ALL' || item.type === filterType;\n        return matchesSearch && matchesType;\n    });\n    // أنواع المواد للفلترة\n    const mediaTypes = [\n        'ALL',\n        'PROGRAM',\n        'SERIES',\n        'FILM',\n        'SONG',\n        'PROMO',\n        'STING',\n        'FILLER',\n        'NEXT',\n        'NOW',\n        'سنعود',\n        'عدنا',\n        'MINI',\n        'CROSS'\n    ];\n    // دالة ترجمة أنواع المواد\n    const getTypeLabel = (type)=>{\n        const typeLabels = {\n            'ALL': t('schedule.allTypes'),\n            'PROGRAM': t('schedule.types.program'),\n            'SERIES': t('schedule.types.series'),\n            'FILM': t('schedule.types.film'),\n            'MOVIE': t('schedule.types.film'),\n            'SONG': t('schedule.types.song'),\n            'STING': t('schedule.types.sting'),\n            'FILL_IN': t('schedule.types.fillIn'),\n            'FILLER': t('schedule.types.filler'),\n            'PROMO': t('schedule.types.promo'),\n            'NEXT': t('schedule.types.next'),\n            'NOW': t('schedule.types.now'),\n            'سنعود': t('mediaTypes.سنعود'),\n            'عدنا': t('mediaTypes.عدنا'),\n            'MINI': t('mediaTypes.MINI'),\n            'CROSS': t('mediaTypes.CROSS')\n        };\n        return typeLabels[type] || type;\n    };\n    // إضافة صف فارغ\n    const addEmptyRow = (afterIndex)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        const newRow = {\n            id: \"empty_\".concat(Date.now()),\n            type: 'empty',\n            canDelete: true\n        };\n        newRows.splice(afterIndex + 1, 0, newRow);\n        setGridRows(newRows);\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // إضافة صفوف فارغة متعددة\n    const addMultipleEmptyRows = function(afterIndex) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 8;\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        for(let i = 0; i < count; i++){\n            const newRow = {\n                id: \"empty_\".concat(Date.now(), \"_\").concat(i),\n                type: 'empty',\n                canDelete: true\n            };\n            newRows.splice(afterIndex + 1 + i, 0, newRow);\n        }\n        setGridRows(newRows);\n        console.log(\"✅ تم إضافة \".concat(count, \" صف فارغ\"));\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // التحقق من الحاجة لإضافة صفوف فارغة\n    const checkAndAddEmptyRows = (currentIndex)=>{\n        const currentRows = gridRows;\n        const nextEmptyIndex = currentRows.findIndex((row, index)=>index > currentIndex && row.type === 'empty');\n        // إذا لم توجد صفوف فارغة بعد الفاصل الحالي\n        if (nextEmptyIndex === -1) {\n            console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 8 صفوف');\n            addMultipleEmptyRows(currentIndex, 8);\n        } else {\n            console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);\n        }\n    };\n    // حذف صف فارغ\n    const deleteRow = (rowId)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        // استعادة موضع التمرير بعد الحذف\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 100);\n    };\n    // حذف سيجمنت مع حماية المواد الأساسية\n    const deleteSegment = (rowId)=>{\n        const row = gridRows.find((r)=>r.id === rowId);\n        // حماية مضاعفة للمواد الأساسية\n        if (row && row.type === 'segment' && (!row.isTemporary || !row.canDelete)) {\n            showErrorToast('permissionDenied');\n            return;\n        }\n        // التأكد من أن المادة قابلة للحذف\n        if (row && !row.canDelete) {\n            showErrorToast('permissionDenied');\n            return;\n        }\n        if (confirm(t('schedule.confirmDeleteSegment'))) {\n            const newRows = gridRows.filter((row)=>row.id !== rowId);\n            recalculateTimes(newRows);\n            console.log('🗑️ تم حذف السيجمنت');\n        }\n    };\n    // حذف فاصل بدون تأكيد\n    const deleteFiller = (rowId)=>{\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        console.log('🗑️ تم حذف الفاصل');\n    };\n    // تحريك صف لأعلى\n    const moveRowUp = (index)=>{\n        if (index <= 0) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index - 1], newRows[index]] = [\n            newRows[index],\n            newRows[index - 1]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬆️ تم تحريك الصف لأعلى');\n    };\n    // تحريك صف لأسفل\n    const moveRowDown = (index)=>{\n        if (index >= gridRows.length - 1) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index], newRows[index + 1]] = [\n            newRows[index + 1],\n            newRows[index]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬇️ تم تحريك الصف لأسفل');\n    };\n    // معالجة سحب الصفوف داخل الجدول\n    const handleRowDragStart = (e, index)=>{\n        e.dataTransfer.setData('text/plain', index.toString());\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    // معالجة إسقاط الصفوف داخل الجدول\n    const handleRowDrop = (e, targetIndex)=>{\n        e.preventDefault();\n        const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));\n        if (sourceIndex === targetIndex) return;\n        const newRows = [\n            ...gridRows\n        ];\n        const [movedRow] = newRows.splice(sourceIndex, 1);\n        newRows.splice(targetIndex, 0, movedRow);\n        recalculateTimes(newRows);\n        console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);\n        // تثبيت الجدول بعد السحب\n        setTimeout(()=>{\n            const gridElement = document.querySelector('.schedule-grid');\n            if (gridElement) {\n                gridElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'start'\n                });\n            }\n        }, 100);\n    };\n    // معالجة إسقاط المواد\n    const handleDrop = (e, rowIndex)=>{\n        e.preventDefault();\n        // حفظ موضع التمرير الحالي قبل أي تغيير\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const rowElement = e.currentTarget.closest('.grid-row');\n        const rowOffsetTop = (rowElement === null || rowElement === void 0 ? void 0 : rowElement.getBoundingClientRect().top) || 0;\n        try {\n            var _mediaData_segments, _mediaData_segments1;\n            // محاولة الحصول على البيانات بطرق مختلفة\n            let mediaData;\n            const jsonData = e.dataTransfer.getData('application/json');\n            const textData = e.dataTransfer.getData('text/plain');\n            console.log('📥 البيانات المسحوبة:', {\n                jsonData,\n                textData\n            });\n            // التحقق من أن هذا ليس سحب صف داخلي\n            if (textData && !jsonData && /^\\d+$/.test(textData)) {\n                console.log('🔄 هذا سحب صف داخلي، تجاهل');\n                return;\n            }\n            if (jsonData) {\n                mediaData = JSON.parse(jsonData);\n            } else if (textData) {\n                try {\n                    mediaData = JSON.parse(textData);\n                } catch (e) {\n                    console.error('❌ لا يمكن تحليل البيانات المسحوبة');\n                    return;\n                }\n            } else {\n                console.error('❌ لا توجد بيانات مسحوبة');\n                return;\n            }\n            // التحقق من أن الصف فارغ\n            const targetRow = gridRows[rowIndex];\n            if (targetRow.type !== 'empty') {\n                showErrorToast('invalidData');\n                return;\n            }\n            console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);\n            // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر\n            if (!mediaData || typeof mediaData !== 'object' || typeof mediaData === 'string' || Array.isArray(mediaData)) {\n                console.error('❌ بيانات المادة غير صحيحة:', mediaData);\n                console.error('❌ نوع البيانات:', typeof mediaData);\n                console.error('❌ هل هو مصفوفة؟', Array.isArray(mediaData));\n                return;\n            }\n            // التأكد من وجود الاسم\n            const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';\n            const itemType = mediaData.type || 'UNKNOWN';\n            const itemId = mediaData.id || Date.now().toString();\n            console.log('📥 معلومات المادة:', {\n                name: itemName,\n                type: itemType,\n                id: itemId,\n                segments: ((_mediaData_segments = mediaData.segments) === null || _mediaData_segments === void 0 ? void 0 : _mediaData_segments.length) || 0\n            });\n            // تحديد نوع المادة المسحوبة\n            let dragItemType = 'filler';\n            let itemContent = itemName;\n            // إضافة تفاصيل المادة\n            const details = [];\n            if (mediaData.episodeNumber) details.push(\"ح\".concat(mediaData.episodeNumber));\n            if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(\"م\".concat(mediaData.seasonNumber));\n            if (mediaData.partNumber) details.push(\"ج\".concat(mediaData.partNumber));\n            const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n            // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات\n            if ([\n                'PROMO',\n                'STING',\n                'FILLER',\n                'FILL_IN'\n            ].includes(itemType)) {\n                dragItemType = 'filler';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                // تخزين نوع المادة لاستخدامه في تمييز الألوان\n                mediaData.mediaType = itemType;\n            } else {\n                dragItemType = 'segment';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" (مادة إضافية)\");\n            }\n            // حساب المدة الحقيقية للمادة\n            let itemDuration = '00:01:00'; // مدة افتراضية\n            console.log('🔍 تحليل مدة المادة:', {\n                name: itemName,\n                hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),\n                segmentsCount: ((_mediaData_segments1 = mediaData.segments) === null || _mediaData_segments1 === void 0 ? void 0 : _mediaData_segments1.length) || 0,\n                hasDuration: !!mediaData.duration,\n                directDuration: mediaData.duration\n            });\n            if (mediaData.segments && mediaData.segments.length > 0) {\n                // حساب إجمالي مدة جميع السيجمنتات\n                let totalSeconds = 0;\n                mediaData.segments.forEach((segment, index)=>{\n                    if (segment.duration) {\n                        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                        const segmentSeconds = hours * 3600 + minutes * 60 + seconds;\n                        totalSeconds += segmentSeconds;\n                        console.log(\"  \\uD83D\\uDCFA سيجمنت \".concat(index + 1, \": \").concat(segment.duration, \" (\").concat(segmentSeconds, \" ثانية)\"));\n                    }\n                });\n                if (totalSeconds > 0) {\n                    const hours = Math.floor(totalSeconds / 3600);\n                    const minutes = Math.floor(totalSeconds % 3600 / 60);\n                    const secs = totalSeconds % 60;\n                    itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                }\n                console.log('📊 حساب مدة المادة من السيجمنتات:', {\n                    name: itemName,\n                    segments: mediaData.segments.length,\n                    totalSeconds,\n                    finalDuration: itemDuration\n                });\n            } else if (mediaData.duration) {\n                // استخدام المدة المباشرة إذا كانت موجودة\n                itemDuration = mediaData.duration;\n                console.log('📊 استخدام مدة مباشرة:', itemDuration);\n            } else {\n                console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);\n            }\n            // إنشاء كود للمادة المسحوبة\n            let itemCode = '';\n            if (mediaData.segmentCode) {\n                itemCode = mediaData.segmentCode;\n            } else if (mediaData.id) {\n                itemCode = \"\".concat(itemType, \"_\").concat(mediaData.id);\n            } else {\n                itemCode = \"\".concat(itemType, \"_\").concat(Date.now().toString().slice(-6));\n            }\n            // إنشاء صف جديد مع المدة الحقيقية\n            const newRow = {\n                id: \"dropped_\".concat(Date.now()),\n                type: dragItemType,\n                content: itemContent,\n                mediaItemId: itemId,\n                segmentCode: itemCode,\n                duration: itemDuration,\n                canDelete: true\n            };\n            // إضافة نوع المادة للفواصل لتمييزها بألوان مختلفة\n            if (dragItemType === 'filler' && [\n                'PROMO',\n                'STING',\n                'FILL_IN',\n                'FILLER'\n            ].includes(itemType)) {\n                newRow.mediaType = itemType;\n                console.log(\"\\uD83C\\uDFA8 تم تعيين نوع المادة: \".concat(itemType, \" للصف الجديد\"));\n            }\n            // التأكد من أن الصف المستهدف فارغ\n            if (gridRows[rowIndex].type !== 'empty') {\n                console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);\n                return;\n            }\n            // استبدال الصف الفارغ مباشرة\n            const newRows = [\n                ...gridRows\n            ];\n            newRows[rowIndex] = newRow;\n            console.log('✅ تم إضافة المادة:', {\n                name: itemName,\n                type: dragItemType,\n                duration: itemDuration,\n                position: rowIndex,\n                content: itemContent,\n                beforeType: gridRows[rowIndex].type,\n                afterType: newRow.type\n            });\n            // تحديث الصفوف مباشرة\n            setGridRows(newRows);\n            // إعادة حساب الأوقات بعد تأخير قصير\n            setTimeout(()=>{\n                recalculateTimes(newRows);\n                // التحقق من الحاجة لإضافة صفوف فارغة\n                checkAndAddEmptyRows(rowIndex);\n                // تثبيت الجدول بعد إسقاط المادة\n                setTimeout(()=>{\n                    const gridElement = document.querySelector('.schedule-grid');\n                    if (gridElement) {\n                        gridElement.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'start'\n                        });\n                        console.log('📍 تم تثبيت الجدول بعد إسقاط المادة');\n                    }\n                }, 100);\n            }, 50);\n        } catch (error) {\n            console.error('❌ خطأ في إسقاط المادة:', error);\n            // في حالة حدوث خطأ، نستعيد موضع التمرير على أي حال\n            setTimeout(()=>{\n                if (gridBody) {\n                    gridBody.scrollTop = currentScrollTop;\n                }\n            }, 100);\n        }\n    };\n    // إعادة حساب الأوقات مثل Excel\n    const recalculateTimes = (rows)=>{\n        const newRows = [\n            ...rows\n        ];\n        // استخدام وقت البداية المحدد في الاستيراد أو 08:00:00 كافتراضي\n        let currentTime = startTime || '08:00:00';\n        let hasFillers = false; // هل تم إضافة فواصل؟\n        // التحقق من وجود فواصل\n        hasFillers = rows.some((row)=>row.type === 'filler');\n        console.log('🔄 بدء إعادة حساب الأوقات من', currentTime, hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');\n        for(let i = 0; i < newRows.length; i++){\n            const row = newRows[i];\n            if (row.type === 'segment' || row.type === 'filler') {\n                // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل\n                if (i === 0 || hasFillers) {\n                    newRows[i] = {\n                        ...row,\n                        time: currentTime\n                    };\n                } else {\n                    newRows[i] = {\n                        ...row,\n                        time: undefined\n                    };\n                }\n                if (row.duration) {\n                    // حساب الوقت التالي بناءً على المدة\n                    const nextTime = calculateNextTime(currentTime, row.duration);\n                    console.log(\"⏰ \".concat(row.type, ': \"').concat(row.content, '\" - من ').concat(currentTime, \" إلى \").concat(nextTime, \" (مدة: \").concat(row.duration, \")\"));\n                    currentTime = nextTime;\n                }\n            } else if (row.type === 'empty') {\n                // الصفوف الفارغة لا تؤثر على الوقت\n                newRows[i] = {\n                    ...row,\n                    time: undefined\n                };\n            }\n            // التحقق من الوصول لوقت مادة أساسية\n            if (row.originalStartTime && hasFillers) {\n                const targetMinutes = timeToMinutes(row.originalStartTime);\n                const currentMinutes = timeToMinutes(currentTime);\n                const difference = targetMinutes - currentMinutes;\n                if (Math.abs(difference) > 5) {\n                    console.log('⚠️ انحراف زمني: المادة \"'.concat(row.content, '\" مجدولة في ').concat(row.originalStartTime, \" لكن ستدخل في \").concat(currentTime, \" (فرق: \").concat(difference, \" دقيقة)\"));\n                } else {\n                    console.log('✅ توقيت صحيح: المادة \"'.concat(row.content, '\" ستدخل في ').concat(currentTime, \" (مجدولة: \").concat(row.originalStartTime, \")\"));\n                }\n            }\n        }\n        console.log(\"\\uD83C\\uDFC1 انتهاء الحساب - الوقت النهائي: \".concat(currentTime));\n        // حفظ موضع التمرير قبل التحديث\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        // تحديث الصفوف دائماً لضمان التحديث الصحيح\n        setGridRows(newRows);\n        // استعادة موضع التمرير بعد التحديث\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // تحويل الوقت إلى دقائق\n    const timeToMinutes = (time)=>{\n        const [hours, minutes] = time.split(':').map(Number);\n        return hours * 60 + minutes;\n    };\n    // حساب المدة الإجمالية للمادة بدقة\n    const calculateTotalDuration = (item)=>{\n        if (item.segments && item.segments.length > 0) {\n            let totalSeconds = 0;\n            item.segments.forEach((segment)=>{\n                if (segment.duration) {\n                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                }\n            });\n            if (totalSeconds > 0) {\n                const hours = Math.floor(totalSeconds / 3600);\n                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                const secs = totalSeconds % 60;\n                return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n            }\n        }\n        return item.duration || '00:01:00';\n    };\n    // Export broadcast schedule to Excel\n    const exportDailySchedule = async ()=>{\n        try {\n            console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');\n            if (!selectedDate) {\n                showErrorToast('invalidData');\n                return;\n            }\n            // فلترة البيانات بناءً على الوقت المحدد\n            let filteredRows = gridRows.filter((row)=>(row.type === 'segment' || row.type === 'filler' && row.content) && !row.isTemporary);\n            // إذا تم تحديد وقت بداية، فلتر البيانات من ذلك الوقت\n            if (startTime && startTime !== '08:00:00') {\n                filteredRows = filteredRows.filter((row)=>{\n                    if (!row.time) return false;\n                    return row.time >= startTime;\n                });\n                console.log(\"\\uD83D\\uDCCA تصدير البيانات بدءًا من الوقت \".concat(startTime, \" - عدد المواد: \").concat(filteredRows.length));\n            }\n            // إرسال البيانات المفلترة مع طلب التصدير\n            const currentData = {\n                date: selectedDate,\n                startTime: startTime || '08:00:00',\n                scheduleRows: filteredRows\n            };\n            const response = await fetch(\"/api/export-daily-schedule-new?date=\".concat(selectedDate), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(currentData)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"Daily_Schedule_\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('✅ تم تصدير الجدول الإذاعي بنجاح');\n            showSuccessToast('exportSuccess');\n        } catch (error) {\n            console.error('❌ خطأ في تصدير الجدول:', error);\n            showErrorToast('exportFailed');\n        }\n    };\n    // حساب الوقت التالي بناءً على المدة (بدقة الثواني)\n    const calculateNextTime = (startTime, duration)=>{\n        // تحليل وقت البداية\n        const startParts = startTime.split(':');\n        const startHours = parseInt(startParts[0]);\n        const startMins = parseInt(startParts[1]);\n        const startSecs = parseInt(startParts[2] || '0');\n        // تحليل المدة\n        const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n        // حساب إجمالي الثواني\n        let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;\n        totalSeconds += durHours * 3600 + durMins * 60 + durSecs;\n        // تحويل إلى ساعات ودقائق وثواني\n        const hours = Math.floor(totalSeconds / 3600) % 24;\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    // استيراد الجدول من وقت محدد\n    const importFromTime = async ()=>{\n        if (!selectedDate) {\n            showErrorToast('invalidData');\n            return;\n        }\n        if (!startTime) {\n            showErrorToast('invalidData');\n            return;\n        }\n        // التحقق من صحة تنسيق الوقت\n        const timePattern = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$/;\n        if (!timePattern.test(startTime)) {\n            showErrorToast('timeFormatError');\n            return;\n        }\n        setLoading(true);\n        try {\n            console.log('🔍 جلب وفلترة المواد بدءًا من الوقت:', startTime);\n            // جلب البيانات من الخادم أولاً\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            const data = await response.json();\n            if (data.success) {\n                // فلترة الصفوف التي تبدأ من الوقت المحدد أو بعده\n                const allRows = data.data.scheduleRows || [];\n                const filteredRows = allRows.filter((row)=>{\n                    if (!row.time) return false;\n                    return row.time >= startTime;\n                });\n                console.log('✅ تم استيراد', filteredRows.length, 'صف من الوقت', startTime);\n                // إضافة صفوف فارغة في النهاية للتعديل\n                const emptyRows = Array.from({\n                    length: 15\n                }, (_, i)=>({\n                        id: \"empty_import_\".concat(Date.now(), \"_\").concat(i),\n                        type: 'empty',\n                        canDelete: true\n                    }));\n                const finalRows = [\n                    ...filteredRows,\n                    ...emptyRows\n                ];\n                setGridRows(finalRows);\n                setAvailableMedia(data.data.availableMedia || []);\n                setIsImported(true);\n                // إعادة حساب الأوقات بناءً على وقت البداية المحدد\n                setTimeout(()=>{\n                    recalculateTimes(finalRows);\n                }, 100);\n                // جلب الخريطة الأسبوعية للمراجعة\n                await fetchWeeklySchedule();\n                showSuccessToast('importSuccess');\n            } else {\n                showErrorToast('importFailed');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في استيراد الجدول:', error);\n            showErrorToast('importFailed');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            title: t('schedule.importTitle'),\n            subtitle: t('schedule.importSubtitle'),\n            icon: \"\\uD83D\\uDCCA\",\n            fullWidth: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"1b542d50d800f28e\",\n                    children: \".grid-row.empty.jsx-1b542d50d800f28e{background:#b8dce8!important;background-color:#b8dce8!important;min-height:50px!important;height:50px!important;border-color:#a0c4d4!important}.grid-row.empty.jsx-1b542d50d800f28e .action-btn.jsx-1b542d50d800f28e{font-size:.75rem!important;padding:5px 8px!important;min-width:30px!important;height:30px!important;line-height:1.2!important;-webkit-border-radius:4px!important;-moz-border-radius:4px!important;border-radius:4px!important;margin:2px!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important;overflow:hidden!important;white-space:nowrap!important}.grid-row.empty.jsx-1b542d50d800f28e .actions-cell.jsx-1b542d50d800f28e{padding:8px!important;gap:5px!important;display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important;-webkit-box-align:center!important;-webkit-align-items:center!important;-moz-box-align:center!important;-ms-flex-align:center!important;align-items:center!important;-webkit-box-pack:center!important;-webkit-justify-content:center!important;-moz-box-pack:center!important;-ms-flex-pack:center!important;justify-content:center!important;height:50px!important;max-height:50px!important;overflow:hidden!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important}\"\n                }, void 0, false, void 0, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#b8dce8',\n                        minHeight: '100vh',\n                        margin: '-2rem',\n                        padding: '1rem'\n                    },\n                    className: \"jsx-1b542d50d800f28e\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: 'linear-gradient(45deg, #e3f2fd, #bbdefb)',\n                                padding: '8px 15px',\n                                borderRadius: '6px',\n                                marginBottom: '10px',\n                                border: '1px solid #2196f3'\n                            },\n                            className: \"jsx-1b542d50d800f28e\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                style: {\n                                    margin: '0',\n                                    color: '#424242',\n                                    fontSize: '14px'\n                                },\n                                className: \"jsx-1b542d50d800f28e\",\n                                children: [\n                                    \"\\uD83D\\uDCCB \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        className: \"jsx-1b542d50d800f28e\",\n                                        children: t('schedule.importInstructions')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                        lineNumber: 1244,\n                                        columnNumber: 14\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                lineNumber: 1243,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                            lineNumber: 1236,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-controls\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"date-selector\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"schedule-date\",\n                                            className: \"jsx-1b542d50d800f28e\",\n                                            children: [\n                                                t('common.selectDate'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1251,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"schedule-date\",\n                                            type: \"date\",\n                                            value: selectedDate,\n                                            onChange: (e)=>setSelectedDate(e.target.value),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1252,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginRight: '20px'\n                                    },\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-selector\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"start-time\",\n                                            className: \"jsx-1b542d50d800f28e\",\n                                            children: [\n                                                t('schedule.startTime'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1262,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"start-time\",\n                                            type: \"text\",\n                                            placeholder: \"08:00:00\",\n                                            value: startTime,\n                                            onChange: (e)=>setStartTime(e.target.value),\n                                            style: {\n                                                width: '120px',\n                                                textAlign: 'center'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1263,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                    lineNumber: 1261,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        marginRight: '20px'\n                                    },\n                                    className: \"jsx-1b542d50d800f28e\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: importFromTime,\n                                        disabled: !selectedDate || !startTime || loading,\n                                        style: {\n                                            background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                            color: 'white',\n                                            fontWeight: 'bold'\n                                        },\n                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button primary\",\n                                        children: loading ? \"⏳ \".concat(t('common.loading')) : \"\\uD83D\\uDCE5 \".concat(t('schedule.importFromTime'))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                        lineNumber: 1275,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                    lineNumber: 1274,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"header-buttons\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: exportDailySchedule,\n                                            style: {\n                                                background: 'linear-gradient(45deg, #17a2b8, #138496)',\n                                                color: 'white'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button export\",\n                                            children: [\n                                                \"\\uD83D\\uDCCA \",\n                                                t('common.export'),\n                                                \" Excel\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1290,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowWeeklySchedule(!showWeeklySchedule),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button\",\n                                            children: showWeeklySchedule ? \"\\uD83D\\uDCCB \".concat(t('schedule.hideSchedule')) : \"\\uD83D\\uDCC5 \".concat(t('schedule.showSchedule'))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1301,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                    lineNumber: 1289,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                            lineNumber: 1249,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-content\",\n                            children: [\n                                showWeeklySchedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: t('schedule.weeklyScheduleTitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1314,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-schedule-list\",\n                                            children: Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? weeklySchedule.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-time\",\n                                                            children: item.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-name\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1321,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ح\",\n                                                                        item.episodeNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1323,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ج\",\n                                                                        item.partNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1326,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1320,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-status\",\n                                                            children: item.isRerun ? '🔄' : '🎯'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1329,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1318,\n                                                    columnNumber: 19\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"no-data\",\n                                                children: t('schedule.noWeeklyData')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                lineNumber: 1335,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1315,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                    lineNumber: 1313,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: \"المواد المتاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1343,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-controls\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filterType,\n                                                    onChange: (e)=>setFilterType(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"filter-select\",\n                                                    children: mediaTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type,\n                                                            className: \"jsx-1b542d50d800f28e\",\n                                                            children: getTypeLabel(type)\n                                                        }, type, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1353,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1347,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث في المواد...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"search-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1359,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1346,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-header\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                            children: \"الاسم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1373,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                            children: \"المدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1374,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                            children: \"الكود\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1375,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1371,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-body\",\n                                                    children: filteredMedia.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: (e)=>{\n                                                                e.dataTransfer.setData('application/json', JSON.stringify(item));\n                                                                // إضافة class للتصغير أثناء السحب\n                                                                e.currentTarget.classList.add('dragging');\n                                                            },\n                                                            onDragEnd: (e)=>{\n                                                                // إزالة class بعد انتهاء السحب\n                                                                e.currentTarget.classList.remove('dragging');\n                                                            },\n                                                            \"data-type\": item.type,\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-row \".concat(item.type.toLowerCase()),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    title: \"\".concat(item.name, \" - \").concat(getTypeLabel(item.type)),\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-name-text\",\n                                                                            children: item.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                            lineNumber: 1397,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-tags\",\n                                                                            children: [\n                                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"episode-tag\",\n                                                                                    children: [\n                                                                                        \"ح\",\n                                                                                        item.episodeNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                    lineNumber: 1399,\n                                                                                    columnNumber: 46\n                                                                                }, this),\n                                                                                item.seasonNumber && item.seasonNumber > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"season-tag\",\n                                                                                    children: [\n                                                                                        \"م\",\n                                                                                        item.seasonNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                    lineNumber: 1400,\n                                                                                    columnNumber: 70\n                                                                                }, this),\n                                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"part-tag\",\n                                                                                    children: [\n                                                                                        \"ج\",\n                                                                                        item.partNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                    lineNumber: 1401,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                            lineNumber: 1398,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1396,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                                    children: getTypeLabel(item.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1404,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                                    children: calculateTotalDuration(item)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1405,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                                    children: item.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1406,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, item.id, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                            lineNumber: 1381,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1379,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1369,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                    lineNumber: 1342,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-grid\",\n                                    children: !isImported ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'center',\n                                            height: '70vh',\n                                            background: 'linear-gradient(45deg, #f5f5f5, #e0e0e0)',\n                                            borderRadius: '8px',\n                                            border: '2px dashed #ccc'\n                                        },\n                                        className: \"jsx-1b542d50d800f28e\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                textAlign: 'center',\n                                                color: '#666'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-1b542d50d800f28e\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCE5 \",\n                                                        t('schedule.importSchedule')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-1b542d50d800f28e\",\n                                                    children: t('schedule.importInstructionsLong')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1427,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                            lineNumber: 1425,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                        lineNumber: 1416,\n                                        columnNumber: 13\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-header\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-column\",\n                                                        children: \"الكود\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                        lineNumber: 1433,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-column\",\n                                                        children: \"الوقت\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                        lineNumber: 1434,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-column\",\n                                                        children: \"المحتوى\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                        lineNumber: 1435,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-column\",\n                                                        children: \"المدة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                        lineNumber: 1436,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-column\",\n                                                        children: \"الحالة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                        lineNumber: 1437,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-column\",\n                                                        children: \"الإجراءات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                        lineNumber: 1438,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                lineNumber: 1432,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-body\",\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"loading\",\n                                                    children: t('common.loading')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                    lineNumber: 1443,\n                                                    columnNumber: 15\n                                                }, this) : gridRows.map((row, index)=>{\n                                                    var _row_content, _row_content1, _row_content2, _row_content3;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        draggable: row.type === 'filler' || row.type === 'empty' || row.type === 'segment' && row.canDelete,\n                                                        onDragStart: (e)=>handleRowDragStart(e, index),\n                                                        onDrop: (e)=>{\n                                                            handleRowDrop(e, index);\n                                                            // إزالة تأثير drag-over\n                                                            e.currentTarget.classList.remove('drag-over');\n                                                        },\n                                                        onDragOver: (e)=>{\n                                                            e.preventDefault();\n                                                            // إضافة تأثير بصري عند السحب فوق الصف الفارغ\n                                                            if (row.type === 'empty') {\n                                                                e.currentTarget.classList.add('drag-over');\n                                                            }\n                                                        },\n                                                        onDragLeave: (e)=>{\n                                                            // إزالة تأثير drag-over عند مغادرة الصف\n                                                            e.currentTarget.classList.remove('drag-over');\n                                                        },\n                                                        \"data-type\": // استخدام الحقل الإضافي mediaType إن وجد\n                                                        row.mediaType ? row.mediaType : ((_row_content = row.content) === null || _row_content === void 0 ? void 0 : _row_content.includes('PROMO')) ? 'PROMO' : ((_row_content1 = row.content) === null || _row_content1 === void 0 ? void 0 : _row_content1.includes('STING')) ? 'STING' : ((_row_content2 = row.content) === null || _row_content2 === void 0 ? void 0 : _row_content2.includes('FILL_IN')) ? 'FILL_IN' : ((_row_content3 = row.content) === null || _row_content3 === void 0 ? void 0 : _row_content3.includes('FILLER')) ? 'FILLER' : '',\n                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-row \".concat(row.type, \" \").concat(row.type === 'segment' ? row.isRerun ? 'rerun-content' : row.isTemporary ? 'temp-content' : 'primary-content' : row.type === 'filler' ? row.mediaType === 'PROMO' ? 'promo-content' : row.mediaType === 'STING' ? 'sting-content' : row.mediaType === 'FILLER' ? 'filler-content' : row.mediaType === 'FILL_IN' ? 'filler-content' : 'break-content' : 'primary-content'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-cell\",\n                                                                children: row.type === 'segment' || row.type === 'filler' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-code\",\n                                                                    children: row.segmentCode || \"\".concat(row.type.toUpperCase(), \"_\").concat(row.id.slice(-6))\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1492,\n                                                                    columnNumber: 23\n                                                                }, this) : ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                lineNumber: 1490,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-time\",\n                                                                    children: row.time || ''\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1498,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                lineNumber: 1497,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                onDrop: (e)=>handleDrop(e, index),\n                                                                onDragOver: (e)=>e.preventDefault(),\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-text\",\n                                                                    children: row.content || ''\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                    lineNumber: 1507,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                lineNumber: 1502,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-cell\",\n                                                                children: row.duration || ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                lineNumber: 1511,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-cell\",\n                                                                children: [\n                                                                    row.type === 'segment' && row.isTemporary && '🟣 مؤقت',\n                                                                    row.type === 'segment' && !row.isRerun && !row.isTemporary && (row.originalStartTime ? Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ? '⚠️ انحراف' : '✅ دقيق' : '🎯 أساسي'),\n                                                                    row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة',\n                                                                    row.type === 'filler' && '📺 فاصل',\n                                                                    row.type === 'empty' && '⚪ فارغ'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                lineNumber: 1514,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-cell\",\n                                                                children: [\n                                                                    row.type === 'empty' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                title: \"إضافة صف\",\n                                                                                onClick: ()=>addEmptyRow(index),\n                                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-row\",\n                                                                                children: \"➕\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                lineNumber: 1529,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                title: \"إضافة 8 صفوف\",\n                                                                                onClick: ()=>addMultipleEmptyRows(index, 8),\n                                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-multiple-rows\",\n                                                                                children: \"➕➕\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                lineNumber: 1536,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            row.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                title: \"حذف صف\",\n                                                                                onClick: ()=>deleteRow(row.id),\n                                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                                children: \"➖\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                lineNumber: 1544,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true),\n                                                                    row.type === 'filler' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                title: \"تحريك لأعلى\",\n                                                                                onClick: ()=>moveRowUp(index),\n                                                                                disabled: index === 0,\n                                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-up\",\n                                                                                children: \"⬆️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                lineNumber: 1556,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                title: \"تحريك لأسفل\",\n                                                                                onClick: ()=>moveRowDown(index),\n                                                                                disabled: index === gridRows.length - 1,\n                                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-down\",\n                                                                                children: \"⬇️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                lineNumber: 1564,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                title: \"حذف فاصل\",\n                                                                                onClick: ()=>{\n                                                                                    // تحويل الفاصل إلى صف فارغ\n                                                                                    const newRows = [\n                                                                                        ...gridRows\n                                                                                    ];\n                                                                                    newRows[index] = {\n                                                                                        id: \"empty_\".concat(Date.now()),\n                                                                                        type: 'empty',\n                                                                                        canDelete: true\n                                                                                    };\n                                                                                    // إعادة حساب الأوقات\n                                                                                    recalculateTimes(newRows);\n                                                                                },\n                                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                                children: \"\\uD83D\\uDDD1️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                                lineNumber: 1572,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true),\n                                                                    row.type === 'segment' && row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        title: \"استبدال بمادة حقيقية\",\n                                                                        onClick: ()=>{\n                                                                            showErrorToast('invalidData');\n                                                                        },\n                                                                        style: {\n                                                                            color: '#9c27b0'\n                                                                        },\n                                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn replace-temp\",\n                                                                        children: \"\\uD83D\\uDD04\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                        lineNumber: 1593,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    row.type === 'segment' && row.canDelete && !row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        title: \"حذف سيجمنت\",\n                                                                        onClick: ()=>deleteSegment(row.id),\n                                                                        className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                        children: \"❌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                        lineNumber: 1605,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                                lineNumber: 1526,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, row.id, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                        lineNumber: 1446,\n                                                        columnNumber: 17\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                                lineNumber: 1441,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                                    lineNumber: 1414,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                            lineNumber: 1310,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                    lineNumber: 1233,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {\n                    className: \"jsx-1b542d50d800f28e\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n                    lineNumber: 1623,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n            lineNumber: 1200,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\import-list.tsx\",\n        lineNumber: 1199,\n        columnNumber: 5\n    }, this);\n}\n_s(DailySchedulePage, \"2OFRtsGRYDCQuSbQpgSvIbkMskk=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_6__.useTranslatedToast\n    ];\n});\n_c = DailySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"DailySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/import-list.tsx\n"));

/***/ })

});