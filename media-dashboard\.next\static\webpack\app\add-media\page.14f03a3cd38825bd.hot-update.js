"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast)();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        description: '',\n        channel: '',\n        source: '',\n        status: '',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: '',\n        showInTX: false\n    });\n    // إضافة حالة لإظهار/إخفاء الحقول الخاصة حسب نوع المادة\n    const [showEpisodeNumber, setShowEpisodeNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSeasonNumber, setShowSeasonNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPartNumber, setShowPartNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الحقول المرئية عند تغيير نوع المادة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddMediaPage.useEffect\": ()=>{\n            console.log('🔍 نوع المادة المختار:', formData.type);\n            if (formData.type === 'FILM') {\n                // فيلم: يظهر رقم الجزء فقط\n                console.log('✅ عرض حقول الفيلم: رقم الجزء فقط');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'SERIES') {\n                // مسلسل: يظهر رقم الحلقة ورقم الجزء\n                console.log('✅ عرض حقول المسلسل: رقم الحلقة + رقم الجزء');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'PROGRAM') {\n                // برنامج: يظهر رقم الحلقة ورقم الموسم\n                console.log('✅ عرض حقول البرنامج: رقم الحلقة + رقم الموسم');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(true);\n                setShowPartNumber(false);\n            } else {\n                // باقي الأنواع: لا تظهر حقول إضافية\n                console.log('❌ إخفاء جميع الحقول الإضافية');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n            }\n            console.log('📊 حالة الحقول:', {\n                showEpisodeNumber,\n                showSeasonNumber,\n                showPartNumber\n            });\n        }\n    }[\"AddMediaPage.useEffect\"], [\n        formData.type\n    ]);\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: '',\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: field === 'showInTX' ? value === 'true' : value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n        // التحقق من كود السيجمانت عند تغييره\n        if (field === 'segmentCode' && value.trim()) {\n            validateSegmentCode(segmentId, value.trim());\n        } else if (field === 'segmentCode' && !value.trim()) {\n            // إزالة التحقق إذا كان الكود فارغ\n            setSegmentCodeValidation((prev)=>{\n                const newValidation = {\n                    ...prev\n                };\n                delete newValidation[segmentId];\n                return newValidation;\n            });\n        }\n    };\n    // دالة التحقق من كود السيجمانت\n    const validateSegmentCode = async (segmentId, code)=>{\n        // تحديث حالة التحقق\n        setSegmentCodeValidation((prev)=>({\n                ...prev,\n                [segmentId]: {\n                    isValid: true,\n                    message: '',\n                    isChecking: true\n                }\n            }));\n        try {\n            const response = await fetch(\"/api/validate-segment-code?code=\".concat(encodeURIComponent(code)));\n            const result = await response.json();\n            if (result.success) {\n                const isValid = !result.isDuplicate;\n                const message = result.isDuplicate ? \"الكود موجود في: \".concat(result.duplicates.map((d)=>d.mediaName).join(', ')) : 'الكود متاح';\n                setSegmentCodeValidation((prev)=>({\n                        ...prev,\n                        [segmentId]: {\n                            isValid,\n                            message,\n                            isChecking: false\n                        }\n                    }));\n            }\n        } catch (error) {\n            console.error('Error validating segment code:', error);\n            setSegmentCodeValidation((prev)=>({\n                    ...prev,\n                    [segmentId]: {\n                        isValid: true,\n                        message: 'خطأ في التحقق',\n                        isChecking: false\n                    }\n                }));\n        }\n    };\n    const calculateDuration = (segmentId)=>{\n        const segment = segments.find((s)=>s.id === segmentId);\n        if (!segment || !segment.timeIn || !segment.timeOut) return;\n        try {\n            const timeIn = segment.timeIn.split(':').map(Number);\n            const timeOut = segment.timeOut.split(':').map(Number);\n            // تحويل إلى ثواني\n            const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + timeIn[2];\n            const outSeconds = timeOut[0] * 3600 + timeOut[1] * 60 + timeOut[2];\n            // حساب الفرق\n            let durationSeconds = outSeconds - inSeconds;\n            if (durationSeconds < 0) {\n                durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب\n            }\n            // تحويل إلى تنسيق HH:MM:SS\n            const hours = Math.floor(durationSeconds / 3600);\n            const minutes = Math.floor(durationSeconds % 3600 / 60);\n            const seconds = durationSeconds % 60;\n            const duration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n            handleSegmentChange(segmentId, 'duration', duration);\n        } catch (error) {\n            console.error('Error calculating duration:', error);\n        }\n    };\n    const addSegment = ()=>{\n        const newId = segmentCount + 1;\n        setSegmentCount(newId);\n        setSegments((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    segmentCode: '',\n                    timeIn: '00:00:00',\n                    timeOut: '00:00:00',\n                    duration: '00:00:00'\n                }\n            ]);\n    };\n    const removeSegment = (id)=>{\n        if (segments.length <= 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegments((prev)=>prev.filter((segment)=>segment.id !== id));\n    };\n    const setSegmentCount2 = (count)=>{\n        if (count < 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            newSegments.push({\n                id: i,\n                segmentCode: '',\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [segmentCodeValidation, setSegmentCodeValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // التحقق من الحقول المطلوبة\n        const requiredFields = [];\n        if (!formData.hardDiskNumber.trim()) requiredFields.push('رقم الهارد');\n        if (!formData.type) requiredFields.push('نوع المادة');\n        if (!formData.channel) requiredFields.push('القناة');\n        if (!formData.status) requiredFields.push('الحالة');\n        // التحقق من كود السيجمانت\n        const segmentsWithoutCode = segments.filter((segment)=>!segment.segmentCode || segment.segmentCode.trim() === '');\n        if (segmentsWithoutCode.length > 0) {\n            requiredFields.push('كود السيجمانت (مطلوب لجميع السيجمانتات)');\n        }\n        // التحقق من Time Out للسيجمانت\n        const invalidSegments = segments.filter((segment)=>!segment.timeOut || segment.timeOut === '00:00:00');\n        if (invalidSegments.length > 0) {\n            requiredFields.push('Time Out للسيجمانت');\n        }\n        if (requiredFields.length > 0) {\n            showErrorToast('invalidData');\n            return;\n        }\n        // التحقق من تكرار أكواد السيجمانت\n        const segmentCodes = segments.map((s)=>s.segmentCode).filter((code)=>code && code.trim());\n        try {\n            const validationResponse = await fetch('/api/validate-segment-code', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    segmentCodes\n                })\n            });\n            const validationResult = await validationResponse.json();\n            if (validationResult.success && validationResult.hasAnyDuplicates) {\n                let errorMessage = 'تم العثور على أكواد مكررة:\\n\\n';\n                if (validationResult.hasInternalDuplicates) {\n                    errorMessage += \"\\uD83D\\uDD34 أكواد مكررة داخل نفس المادة: \".concat(validationResult.internalDuplicates.join(', '), \"\\n\\n\");\n                }\n                if (validationResult.hasExternalDuplicates) {\n                    errorMessage += '🔴 أكواد موجودة في مواد أخرى:\\n';\n                    validationResult.externalDuplicates.forEach((dup)=>{\n                        errorMessage += '- الكود \"'.concat(dup.segmentCode, '\" موجود في المادة: ').concat(dup.mediaName, \" (\").concat(dup.mediaType, \")\\n\");\n                    });\n                }\n                errorMessage += '\\nيرجى تغيير الأكواد المكررة قبل الحفظ.';\n                showErrorToast('invalidData');\n                return;\n            }\n        } catch (validationError) {\n            console.error('Error validating segment codes:', validationError);\n            showErrorToast('invalidData');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = \"token_\".concat(user.id || 'unknown', \"_\").concat(user.role || 'unknown');\n            console.log('Sending with token:', tokenWithRole);\n            console.log('User data:', user);\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(tokenWithRole)\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('mediaAdded');\n                // مسح جميع الحقول للبدء من جديد\n                setFormData({\n                    name: '',\n                    type: '',\n                    description: '',\n                    channel: '',\n                    source: '',\n                    status: '',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: '',\n                    showInTX: false\n                });\n                // مسح السيجمانتات والعودة لسيجمانت واحد\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: '',\n                        timeIn: '00:00:00',\n                        timeOut: '',\n                        duration: ''\n                    }\n                ]);\n                // إخفاء الحقول الخاصة\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n                console.log('✅ تم مسح جميع الحقول بنجاح');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error saving media:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // أنماط CSS\n    const inputStyle = {\n        width: '100%',\n        padding: '10px',\n        borderRadius: '5px',\n        marginBottom: '10px'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: t('media.addNew'),\n        subtitle: t('media.title'),\n        icon: \"➕\",\n        requiredPermissions: [\n            'MEDIA_CREATE'\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '15px',\n                    padding: '20px',\n                    marginBottom: '25px',\n                    border: '1px solid #6b7280',\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#ffffff',\n                            marginBottom: '10px',\n                            fontSize: '1.4rem',\n                            fontWeight: 'bold'\n                        },\n                        children: [\n                            \"➕ \",\n                            t('addMedia.title')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#e2e8f0',\n                            fontSize: '0.95rem',\n                            margin: 0\n                        },\n                        children: t('addMedia.subtitle')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 380,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDD \",\n                                    t('addMedia.basicInfo')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    fontWeight: 'bold',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCBE \",\n                                                    t('addMedia.hardDiskNumber'),\n                                                    \": \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 54\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('addMedia.hardDiskNumber'),\n                                                value: formData.hardDiskNumber,\n                                                onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    maxWidth: '200px',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"اسم المادة \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 30\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"اسم المادة\",\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            \"نوع المادة \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 32\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectType')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILM\",\n                                                                children: \"Film\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: \"Series\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: \"Program\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: \"Song\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: \"Filler\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: \"Sting\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: \"Promo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEXT\",\n                                                                children: \"Next\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NOW\",\n                                                                children: \"Now\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"سنعود\",\n                                                                children: \"We'll Be Back\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 472,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"عدنا\",\n                                                                children: \"We're Back\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MINI\",\n                                                                children: \"Mini\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CROSS\",\n                                                                children: \"Cross\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 475,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('addMedia.channel'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectChannel')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: \"الوثائقية\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: \"الأخبار\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: \"أخرى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.status'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectStatus')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: \"صالح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_CENSORSHIP\",\n                                                                children: \"مرفوض رقابياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_TECHNICAL\",\n                                                                children: \"مرفوض هندسياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"HOLD\",\n                                                                children: \"Hold\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.source')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: t('addMedia.source'),\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.startDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.startDate,\n                                                        onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.endDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.endDate,\n                                                        onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            showEpisodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.episodeNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.episodeNumber'),\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this),\n                                            showSeasonNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.seasonNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.seasonNumber'),\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this),\n                                            showPartNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.partNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 629,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.partNumber'),\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 628,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 583,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.description')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 649,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.description'),\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.notes')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.additionalNotes'),\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 669,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.showInTX,\n                                                        onChange: (e)=>handleInputChange('showInTX', e.target.checked.toString()),\n                                                        style: {\n                                                            marginLeft: '10px',\n                                                            width: '18px',\n                                                            height: '18px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            background: '#10b981',\n                                                            color: 'white',\n                                                            padding: '2px 8px',\n                                                            borderRadius: '4px',\n                                                            marginLeft: '10px',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"TX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t('addMedia.showInSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.8rem',\n                                                    color: '#9ca3af',\n                                                    marginTop: '5px',\n                                                    marginRight: '35px'\n                                                },\n                                                children: t('addMedia.txDescription')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 716,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('addMedia.segments')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: '#3b82f6',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    padding: '8px 15px',\n                                                    marginLeft: '10px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: t('addMedia.addSegment')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                value: segmentCount,\n                                                onChange: (e)=>setSegmentCount2(parseInt(e.target.value)),\n                                                style: {\n                                                    width: '60px',\n                                                    padding: '8px',\n                                                    borderRadius: '5px',\n                                                    border: '1px solid #6b7280',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    textAlign: 'center'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, this),\n                            segments.map((segment, index)=>{\n                                var _segmentCodeValidation_segment_id, _segmentCodeValidation_segment_id1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#374151',\n                                        borderRadius: '10px',\n                                        padding: '15px',\n                                        marginBottom: '15px',\n                                        border: '1px solid #4b5563'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'space-between',\n                                                alignItems: 'center',\n                                                marginBottom: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#f3f4f6',\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        t('addMedia.segment'),\n                                                        \" \",\n                                                        segment.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeSegment(segment.id),\n                                                    style: {\n                                                        background: '#ef4444',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '5px',\n                                                        padding: '5px 10px',\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: t('common.delete')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 782,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 780,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                gap: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.segmentCode')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 800,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: 'relative'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    placeholder: \"مثال: DDC000055-P1-3\",\n                                                                    required: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: segment.segmentCode ? '#1f2937' : '#7f1d1d',\n                                                                        color: 'white',\n                                                                        border: ((_segmentCodeValidation_segment_id = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id === void 0 ? void 0 : _segmentCodeValidation_segment_id.isValid) === false ? '2px solid #ef4444' : segment.segmentCode ? ((_segmentCodeValidation_segment_id1 = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id1 === void 0 ? void 0 : _segmentCodeValidation_segment_id1.isValid) === true ? '2px solid #10b981' : '1px solid #6b7280' : '2px solid #ef4444',\n                                                                        paddingRight: segmentCodeValidation[segment.id] ? '35px' : '12px'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                segmentCodeValidation[segment.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: 'absolute',\n                                                                        right: '8px',\n                                                                        top: '50%',\n                                                                        transform: 'translateY(-50%)',\n                                                                        fontSize: '16px'\n                                                                    },\n                                                                    children: segmentCodeValidation[segment.id].isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"⏳\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 29\n                                                                    }, this) : segmentCodeValidation[segment.id].isValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#10b981'\n                                                                        },\n                                                                        children: \"✅\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 835,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#ef4444'\n                                                                        },\n                                                                        children: \"❌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        segmentCodeValidation[segment.id] && segmentCodeValidation[segment.id].message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                marginTop: '4px',\n                                                                color: segmentCodeValidation[segment.id].isValid ? '#10b981' : '#ef4444'\n                                                            },\n                                                            children: segmentCodeValidation[segment.id].message\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 843,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 799,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: \"Time In\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 857,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: [\n                                                                \"Time Out \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#ef4444'\n                                                                    },\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 874,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 873,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            onFocus: (e)=>{\n                                                                // إزالة القيمة الوهمية عند النقر\n                                                                if (e.target.value === '00:00:00') {\n                                                                    handleSegmentChange(segment.id, 'timeOut', '');\n                                                                }\n                                                            },\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 876,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.durationAutoCalculated')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 899,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.duration,\n                                                            readOnly: true,\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280',\n                                                                opacity: '0.7',\n                                                                cursor: 'not-allowed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 798,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, segment.id, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                    lineNumber: 773,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'center',\n                            gap: '15px',\n                            marginTop: '20px',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#10b981',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: isSubmitting ? \"⏳ \".concat(t('common.saving')) : \"\\uD83D\\uDCBE \".concat(t('addMedia.saveAndAddNew'))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 922,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>router.push('/media-list'),\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#3b82f6',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCB \",\n                                    t('navigation.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    // مسح جميع الحقول يدوياً\n                                    setFormData({\n                                        name: '',\n                                        type: '',\n                                        description: '',\n                                        channel: '',\n                                        source: '',\n                                        status: '',\n                                        startDate: new Date().toISOString().split('T')[0],\n                                        endDate: '',\n                                        notes: '',\n                                        episodeNumber: '',\n                                        seasonNumber: '',\n                                        partNumber: '',\n                                        hardDiskNumber: '',\n                                        showInTX: false\n                                    });\n                                    setSegmentCount(1);\n                                    setSegments([\n                                        {\n                                            id: 1,\n                                            segmentCode: '',\n                                            timeIn: '00:00:00',\n                                            timeOut: '',\n                                            duration: ''\n                                        }\n                                    ]);\n                                    setShowEpisodeNumber(false);\n                                    setShowSeasonNumber(false);\n                                    setShowPartNumber(false);\n                                    showSuccessToast('changesSaved');\n                                },\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#f59e0b',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDDD1️ \",\n                                    t('addMedia.clearFields')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 959,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 921,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 1015,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 372,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"e+B/6U+sBCuqa5PR2qwxBJwteH8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast,\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});