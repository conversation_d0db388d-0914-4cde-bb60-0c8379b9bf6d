/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tar-stream";
exports.ids = ["vendor-chunks/tar-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/tar-stream/extract.js":
/*!********************************************!*\
  !*** ./node_modules/tar-stream/extract.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var util = __webpack_require__(/*! util */ \"util\")\nvar bl = __webpack_require__(/*! bl */ \"(ssr)/./node_modules/bl/bl.js\")\nvar headers = __webpack_require__(/*! ./headers */ \"(ssr)/./node_modules/tar-stream/headers.js\")\n\nvar Writable = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Writable)\nvar PassThrough = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").PassThrough)\n\nvar noop = function () {}\n\nvar overflow = function (size) {\n  size &= 511\n  return size && 512 - size\n}\n\nvar emptyStream = function (self, offset) {\n  var s = new Source(self, offset)\n  s.end()\n  return s\n}\n\nvar mixinPax = function (header, pax) {\n  if (pax.path) header.name = pax.path\n  if (pax.linkpath) header.linkname = pax.linkpath\n  if (pax.size) header.size = parseInt(pax.size, 10)\n  header.pax = pax\n  return header\n}\n\nvar Source = function (self, offset) {\n  this._parent = self\n  this.offset = offset\n  PassThrough.call(this, { autoDestroy: false })\n}\n\nutil.inherits(Source, PassThrough)\n\nSource.prototype.destroy = function (err) {\n  this._parent.destroy(err)\n}\n\nvar Extract = function (opts) {\n  if (!(this instanceof Extract)) return new Extract(opts)\n  Writable.call(this, opts)\n\n  opts = opts || {}\n\n  this._offset = 0\n  this._buffer = bl()\n  this._missing = 0\n  this._partial = false\n  this._onparse = noop\n  this._header = null\n  this._stream = null\n  this._overflow = null\n  this._cb = null\n  this._locked = false\n  this._destroyed = false\n  this._pax = null\n  this._paxGlobal = null\n  this._gnuLongPath = null\n  this._gnuLongLinkPath = null\n\n  var self = this\n  var b = self._buffer\n\n  var oncontinue = function () {\n    self._continue()\n  }\n\n  var onunlock = function (err) {\n    self._locked = false\n    if (err) return self.destroy(err)\n    if (!self._stream) oncontinue()\n  }\n\n  var onstreamend = function () {\n    self._stream = null\n    var drain = overflow(self._header.size)\n    if (drain) self._parse(drain, ondrain)\n    else self._parse(512, onheader)\n    if (!self._locked) oncontinue()\n  }\n\n  var ondrain = function () {\n    self._buffer.consume(overflow(self._header.size))\n    self._parse(512, onheader)\n    oncontinue()\n  }\n\n  var onpaxglobalheader = function () {\n    var size = self._header.size\n    self._paxGlobal = headers.decodePax(b.slice(0, size))\n    b.consume(size)\n    onstreamend()\n  }\n\n  var onpaxheader = function () {\n    var size = self._header.size\n    self._pax = headers.decodePax(b.slice(0, size))\n    if (self._paxGlobal) self._pax = Object.assign({}, self._paxGlobal, self._pax)\n    b.consume(size)\n    onstreamend()\n  }\n\n  var ongnulongpath = function () {\n    var size = self._header.size\n    this._gnuLongPath = headers.decodeLongPath(b.slice(0, size), opts.filenameEncoding)\n    b.consume(size)\n    onstreamend()\n  }\n\n  var ongnulonglinkpath = function () {\n    var size = self._header.size\n    this._gnuLongLinkPath = headers.decodeLongPath(b.slice(0, size), opts.filenameEncoding)\n    b.consume(size)\n    onstreamend()\n  }\n\n  var onheader = function () {\n    var offset = self._offset\n    var header\n    try {\n      header = self._header = headers.decode(b.slice(0, 512), opts.filenameEncoding, opts.allowUnknownFormat)\n    } catch (err) {\n      self.emit('error', err)\n    }\n    b.consume(512)\n\n    if (!header) {\n      self._parse(512, onheader)\n      oncontinue()\n      return\n    }\n    if (header.type === 'gnu-long-path') {\n      self._parse(header.size, ongnulongpath)\n      oncontinue()\n      return\n    }\n    if (header.type === 'gnu-long-link-path') {\n      self._parse(header.size, ongnulonglinkpath)\n      oncontinue()\n      return\n    }\n    if (header.type === 'pax-global-header') {\n      self._parse(header.size, onpaxglobalheader)\n      oncontinue()\n      return\n    }\n    if (header.type === 'pax-header') {\n      self._parse(header.size, onpaxheader)\n      oncontinue()\n      return\n    }\n\n    if (self._gnuLongPath) {\n      header.name = self._gnuLongPath\n      self._gnuLongPath = null\n    }\n\n    if (self._gnuLongLinkPath) {\n      header.linkname = self._gnuLongLinkPath\n      self._gnuLongLinkPath = null\n    }\n\n    if (self._pax) {\n      self._header = header = mixinPax(header, self._pax)\n      self._pax = null\n    }\n\n    self._locked = true\n\n    if (!header.size || header.type === 'directory') {\n      self._parse(512, onheader)\n      self.emit('entry', header, emptyStream(self, offset), onunlock)\n      return\n    }\n\n    self._stream = new Source(self, offset)\n\n    self.emit('entry', header, self._stream, onunlock)\n    self._parse(header.size, onstreamend)\n    oncontinue()\n  }\n\n  this._onheader = onheader\n  this._parse(512, onheader)\n}\n\nutil.inherits(Extract, Writable)\n\nExtract.prototype.destroy = function (err) {\n  if (this._destroyed) return\n  this._destroyed = true\n\n  if (err) this.emit('error', err)\n  this.emit('close')\n  if (this._stream) this._stream.emit('close')\n}\n\nExtract.prototype._parse = function (size, onparse) {\n  if (this._destroyed) return\n  this._offset += size\n  this._missing = size\n  if (onparse === this._onheader) this._partial = false\n  this._onparse = onparse\n}\n\nExtract.prototype._continue = function () {\n  if (this._destroyed) return\n  var cb = this._cb\n  this._cb = noop\n  if (this._overflow) this._write(this._overflow, undefined, cb)\n  else cb()\n}\n\nExtract.prototype._write = function (data, enc, cb) {\n  if (this._destroyed) return\n\n  var s = this._stream\n  var b = this._buffer\n  var missing = this._missing\n  if (data.length) this._partial = true\n\n  // we do not reach end-of-chunk now. just forward it\n\n  if (data.length < missing) {\n    this._missing -= data.length\n    this._overflow = null\n    if (s) return s.write(data, cb)\n    b.append(data)\n    return cb()\n  }\n\n  // end-of-chunk. the parser should call cb.\n\n  this._cb = cb\n  this._missing = 0\n\n  var overflow = null\n  if (data.length > missing) {\n    overflow = data.slice(missing)\n    data = data.slice(0, missing)\n  }\n\n  if (s) s.end(data)\n  else b.append(data)\n\n  this._overflow = overflow\n  this._onparse()\n}\n\nExtract.prototype._final = function (cb) {\n  if (this._partial) return this.destroy(new Error('Unexpected end of data'))\n  cb()\n}\n\nmodule.exports = Extract\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tar-stream/extract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tar-stream/headers.js":
/*!********************************************!*\
  !*** ./node_modules/tar-stream/headers.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("var alloc = Buffer.alloc\n\nvar ZEROS = '0000000000000000000'\nvar SEVENS = '7777777777777777777'\nvar ZERO_OFFSET = '0'.charCodeAt(0)\nvar USTAR_MAGIC = Buffer.from('ustar\\x00', 'binary')\nvar USTAR_VER = Buffer.from('00', 'binary')\nvar GNU_MAGIC = Buffer.from('ustar\\x20', 'binary')\nvar GNU_VER = Buffer.from('\\x20\\x00', 'binary')\nvar MASK = parseInt('7777', 8)\nvar MAGIC_OFFSET = 257\nvar VERSION_OFFSET = 263\n\nvar clamp = function (index, len, defaultValue) {\n  if (typeof index !== 'number') return defaultValue\n  index = ~~index // Coerce to integer.\n  if (index >= len) return len\n  if (index >= 0) return index\n  index += len\n  if (index >= 0) return index\n  return 0\n}\n\nvar toType = function (flag) {\n  switch (flag) {\n    case 0:\n      return 'file'\n    case 1:\n      return 'link'\n    case 2:\n      return 'symlink'\n    case 3:\n      return 'character-device'\n    case 4:\n      return 'block-device'\n    case 5:\n      return 'directory'\n    case 6:\n      return 'fifo'\n    case 7:\n      return 'contiguous-file'\n    case 72:\n      return 'pax-header'\n    case 55:\n      return 'pax-global-header'\n    case 27:\n      return 'gnu-long-link-path'\n    case 28:\n    case 30:\n      return 'gnu-long-path'\n  }\n\n  return null\n}\n\nvar toTypeflag = function (flag) {\n  switch (flag) {\n    case 'file':\n      return 0\n    case 'link':\n      return 1\n    case 'symlink':\n      return 2\n    case 'character-device':\n      return 3\n    case 'block-device':\n      return 4\n    case 'directory':\n      return 5\n    case 'fifo':\n      return 6\n    case 'contiguous-file':\n      return 7\n    case 'pax-header':\n      return 72\n  }\n\n  return 0\n}\n\nvar indexOf = function (block, num, offset, end) {\n  for (; offset < end; offset++) {\n    if (block[offset] === num) return offset\n  }\n  return end\n}\n\nvar cksum = function (block) {\n  var sum = 8 * 32\n  for (var i = 0; i < 148; i++) sum += block[i]\n  for (var j = 156; j < 512; j++) sum += block[j]\n  return sum\n}\n\nvar encodeOct = function (val, n) {\n  val = val.toString(8)\n  if (val.length > n) return SEVENS.slice(0, n) + ' '\n  else return ZEROS.slice(0, n - val.length) + val + ' '\n}\n\n/* Copied from the node-tar repo and modified to meet\n * tar-stream coding standard.\n *\n * Source: https://github.com/npm/node-tar/blob/51b6627a1f357d2eb433e7378e5f05e83b7aa6cd/lib/header.js#L349\n */\nfunction parse256 (buf) {\n  // first byte MUST be either 80 or FF\n  // 80 for positive, FF for 2's comp\n  var positive\n  if (buf[0] === 0x80) positive = true\n  else if (buf[0] === 0xFF) positive = false\n  else return null\n\n  // build up a base-256 tuple from the least sig to the highest\n  var tuple = []\n  for (var i = buf.length - 1; i > 0; i--) {\n    var byte = buf[i]\n    if (positive) tuple.push(byte)\n    else tuple.push(0xFF - byte)\n  }\n\n  var sum = 0\n  var l = tuple.length\n  for (i = 0; i < l; i++) {\n    sum += tuple[i] * Math.pow(256, i)\n  }\n\n  return positive ? sum : -1 * sum\n}\n\nvar decodeOct = function (val, offset, length) {\n  val = val.slice(offset, offset + length)\n  offset = 0\n\n  // If prefixed with 0x80 then parse as a base-256 integer\n  if (val[offset] & 0x80) {\n    return parse256(val)\n  } else {\n    // Older versions of tar can prefix with spaces\n    while (offset < val.length && val[offset] === 32) offset++\n    var end = clamp(indexOf(val, 32, offset, val.length), val.length, val.length)\n    while (offset < end && val[offset] === 0) offset++\n    if (end === offset) return 0\n    return parseInt(val.slice(offset, end).toString(), 8)\n  }\n}\n\nvar decodeStr = function (val, offset, length, encoding) {\n  return val.slice(offset, indexOf(val, 0, offset, offset + length)).toString(encoding)\n}\n\nvar addLength = function (str) {\n  var len = Buffer.byteLength(str)\n  var digits = Math.floor(Math.log(len) / Math.log(10)) + 1\n  if (len + digits >= Math.pow(10, digits)) digits++\n\n  return (len + digits) + str\n}\n\nexports.decodeLongPath = function (buf, encoding) {\n  return decodeStr(buf, 0, buf.length, encoding)\n}\n\nexports.encodePax = function (opts) { // TODO: encode more stuff in pax\n  var result = ''\n  if (opts.name) result += addLength(' path=' + opts.name + '\\n')\n  if (opts.linkname) result += addLength(' linkpath=' + opts.linkname + '\\n')\n  var pax = opts.pax\n  if (pax) {\n    for (var key in pax) {\n      result += addLength(' ' + key + '=' + pax[key] + '\\n')\n    }\n  }\n  return Buffer.from(result)\n}\n\nexports.decodePax = function (buf) {\n  var result = {}\n\n  while (buf.length) {\n    var i = 0\n    while (i < buf.length && buf[i] !== 32) i++\n    var len = parseInt(buf.slice(0, i).toString(), 10)\n    if (!len) return result\n\n    var b = buf.slice(i + 1, len - 1).toString()\n    var keyIndex = b.indexOf('=')\n    if (keyIndex === -1) return result\n    result[b.slice(0, keyIndex)] = b.slice(keyIndex + 1)\n\n    buf = buf.slice(len)\n  }\n\n  return result\n}\n\nexports.encode = function (opts) {\n  var buf = alloc(512)\n  var name = opts.name\n  var prefix = ''\n\n  if (opts.typeflag === 5 && name[name.length - 1] !== '/') name += '/'\n  if (Buffer.byteLength(name) !== name.length) return null // utf-8\n\n  while (Buffer.byteLength(name) > 100) {\n    var i = name.indexOf('/')\n    if (i === -1) return null\n    prefix += prefix ? '/' + name.slice(0, i) : name.slice(0, i)\n    name = name.slice(i + 1)\n  }\n\n  if (Buffer.byteLength(name) > 100 || Buffer.byteLength(prefix) > 155) return null\n  if (opts.linkname && Buffer.byteLength(opts.linkname) > 100) return null\n\n  buf.write(name)\n  buf.write(encodeOct(opts.mode & MASK, 6), 100)\n  buf.write(encodeOct(opts.uid, 6), 108)\n  buf.write(encodeOct(opts.gid, 6), 116)\n  buf.write(encodeOct(opts.size, 11), 124)\n  buf.write(encodeOct((opts.mtime.getTime() / 1000) | 0, 11), 136)\n\n  buf[156] = ZERO_OFFSET + toTypeflag(opts.type)\n\n  if (opts.linkname) buf.write(opts.linkname, 157)\n\n  USTAR_MAGIC.copy(buf, MAGIC_OFFSET)\n  USTAR_VER.copy(buf, VERSION_OFFSET)\n  if (opts.uname) buf.write(opts.uname, 265)\n  if (opts.gname) buf.write(opts.gname, 297)\n  buf.write(encodeOct(opts.devmajor || 0, 6), 329)\n  buf.write(encodeOct(opts.devminor || 0, 6), 337)\n\n  if (prefix) buf.write(prefix, 345)\n\n  buf.write(encodeOct(cksum(buf), 6), 148)\n\n  return buf\n}\n\nexports.decode = function (buf, filenameEncoding, allowUnknownFormat) {\n  var typeflag = buf[156] === 0 ? 0 : buf[156] - ZERO_OFFSET\n\n  var name = decodeStr(buf, 0, 100, filenameEncoding)\n  var mode = decodeOct(buf, 100, 8)\n  var uid = decodeOct(buf, 108, 8)\n  var gid = decodeOct(buf, 116, 8)\n  var size = decodeOct(buf, 124, 12)\n  var mtime = decodeOct(buf, 136, 12)\n  var type = toType(typeflag)\n  var linkname = buf[157] === 0 ? null : decodeStr(buf, 157, 100, filenameEncoding)\n  var uname = decodeStr(buf, 265, 32)\n  var gname = decodeStr(buf, 297, 32)\n  var devmajor = decodeOct(buf, 329, 8)\n  var devminor = decodeOct(buf, 337, 8)\n\n  var c = cksum(buf)\n\n  // checksum is still initial value if header was null.\n  if (c === 8 * 32) return null\n\n  // valid checksum\n  if (c !== decodeOct(buf, 148, 8)) throw new Error('Invalid tar header. Maybe the tar is corrupted or it needs to be gunzipped?')\n\n  if (USTAR_MAGIC.compare(buf, MAGIC_OFFSET, MAGIC_OFFSET + 6) === 0) {\n    // ustar (posix) format.\n    // prepend prefix, if present.\n    if (buf[345]) name = decodeStr(buf, 345, 155, filenameEncoding) + '/' + name\n  } else if (GNU_MAGIC.compare(buf, MAGIC_OFFSET, MAGIC_OFFSET + 6) === 0 &&\n             GNU_VER.compare(buf, VERSION_OFFSET, VERSION_OFFSET + 2) === 0) {\n    // 'gnu'/'oldgnu' format. Similar to ustar, but has support for incremental and\n    // multi-volume tarballs.\n  } else {\n    if (!allowUnknownFormat) {\n      throw new Error('Invalid tar header: unknown format.')\n    }\n  }\n\n  // to support old tar versions that use trailing / to indicate dirs\n  if (typeflag === 0 && name && name[name.length - 1] === '/') typeflag = 5\n\n  return {\n    name,\n    mode,\n    uid,\n    gid,\n    size,\n    mtime: new Date(1000 * mtime),\n    type,\n    linkname,\n    uname,\n    gname,\n    devmajor,\n    devminor\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tar-stream/headers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tar-stream/index.js":
/*!******************************************!*\
  !*** ./node_modules/tar-stream/index.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.extract = __webpack_require__(/*! ./extract */ \"(ssr)/./node_modules/tar-stream/extract.js\")\nexports.pack = __webpack_require__(/*! ./pack */ \"(ssr)/./node_modules/tar-stream/pack.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdGFyLXN0cmVhbS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQSxvR0FBc0M7QUFDdEMsMkZBQWdDIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcdGFyLXN0cmVhbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0cy5leHRyYWN0ID0gcmVxdWlyZSgnLi9leHRyYWN0JylcbmV4cG9ydHMucGFjayA9IHJlcXVpcmUoJy4vcGFjaycpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tar-stream/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/tar-stream/pack.js":
/*!*****************************************!*\
  !*** ./node_modules/tar-stream/pack.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var constants = __webpack_require__(/*! fs-constants */ \"(ssr)/./node_modules/fs-constants/index.js\")\nvar eos = __webpack_require__(/*! end-of-stream */ \"(ssr)/./node_modules/end-of-stream/index.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar alloc = Buffer.alloc\n\nvar Readable = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Readable)\nvar Writable = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Writable)\nvar StringDecoder = (__webpack_require__(/*! string_decoder */ \"string_decoder\").StringDecoder)\n\nvar headers = __webpack_require__(/*! ./headers */ \"(ssr)/./node_modules/tar-stream/headers.js\")\n\nvar DMODE = parseInt('755', 8)\nvar FMODE = parseInt('644', 8)\n\nvar END_OF_TAR = alloc(1024)\n\nvar noop = function () {}\n\nvar overflow = function (self, size) {\n  size &= 511\n  if (size) self.push(END_OF_TAR.slice(0, 512 - size))\n}\n\nfunction modeToType (mode) {\n  switch (mode & constants.S_IFMT) {\n    case constants.S_IFBLK: return 'block-device'\n    case constants.S_IFCHR: return 'character-device'\n    case constants.S_IFDIR: return 'directory'\n    case constants.S_IFIFO: return 'fifo'\n    case constants.S_IFLNK: return 'symlink'\n  }\n\n  return 'file'\n}\n\nvar Sink = function (to) {\n  Writable.call(this)\n  this.written = 0\n  this._to = to\n  this._destroyed = false\n}\n\ninherits(Sink, Writable)\n\nSink.prototype._write = function (data, enc, cb) {\n  this.written += data.length\n  if (this._to.push(data)) return cb()\n  this._to._drain = cb\n}\n\nSink.prototype.destroy = function () {\n  if (this._destroyed) return\n  this._destroyed = true\n  this.emit('close')\n}\n\nvar LinkSink = function () {\n  Writable.call(this)\n  this.linkname = ''\n  this._decoder = new StringDecoder('utf-8')\n  this._destroyed = false\n}\n\ninherits(LinkSink, Writable)\n\nLinkSink.prototype._write = function (data, enc, cb) {\n  this.linkname += this._decoder.write(data)\n  cb()\n}\n\nLinkSink.prototype.destroy = function () {\n  if (this._destroyed) return\n  this._destroyed = true\n  this.emit('close')\n}\n\nvar Void = function () {\n  Writable.call(this)\n  this._destroyed = false\n}\n\ninherits(Void, Writable)\n\nVoid.prototype._write = function (data, enc, cb) {\n  cb(new Error('No body allowed for this entry'))\n}\n\nVoid.prototype.destroy = function () {\n  if (this._destroyed) return\n  this._destroyed = true\n  this.emit('close')\n}\n\nvar Pack = function (opts) {\n  if (!(this instanceof Pack)) return new Pack(opts)\n  Readable.call(this, opts)\n\n  this._drain = noop\n  this._finalized = false\n  this._finalizing = false\n  this._destroyed = false\n  this._stream = null\n}\n\ninherits(Pack, Readable)\n\nPack.prototype.entry = function (header, buffer, callback) {\n  if (this._stream) throw new Error('already piping an entry')\n  if (this._finalized || this._destroyed) return\n\n  if (typeof buffer === 'function') {\n    callback = buffer\n    buffer = null\n  }\n\n  if (!callback) callback = noop\n\n  var self = this\n\n  if (!header.size || header.type === 'symlink') header.size = 0\n  if (!header.type) header.type = modeToType(header.mode)\n  if (!header.mode) header.mode = header.type === 'directory' ? DMODE : FMODE\n  if (!header.uid) header.uid = 0\n  if (!header.gid) header.gid = 0\n  if (!header.mtime) header.mtime = new Date()\n\n  if (typeof buffer === 'string') buffer = Buffer.from(buffer)\n  if (Buffer.isBuffer(buffer)) {\n    header.size = buffer.length\n    this._encode(header)\n    var ok = this.push(buffer)\n    overflow(self, header.size)\n    if (ok) process.nextTick(callback)\n    else this._drain = callback\n    return new Void()\n  }\n\n  if (header.type === 'symlink' && !header.linkname) {\n    var linkSink = new LinkSink()\n    eos(linkSink, function (err) {\n      if (err) { // stream was closed\n        self.destroy()\n        return callback(err)\n      }\n\n      header.linkname = linkSink.linkname\n      self._encode(header)\n      callback()\n    })\n\n    return linkSink\n  }\n\n  this._encode(header)\n\n  if (header.type !== 'file' && header.type !== 'contiguous-file') {\n    process.nextTick(callback)\n    return new Void()\n  }\n\n  var sink = new Sink(this)\n\n  this._stream = sink\n\n  eos(sink, function (err) {\n    self._stream = null\n\n    if (err) { // stream was closed\n      self.destroy()\n      return callback(err)\n    }\n\n    if (sink.written !== header.size) { // corrupting tar\n      self.destroy()\n      return callback(new Error('size mismatch'))\n    }\n\n    overflow(self, header.size)\n    if (self._finalizing) self.finalize()\n    callback()\n  })\n\n  return sink\n}\n\nPack.prototype.finalize = function () {\n  if (this._stream) {\n    this._finalizing = true\n    return\n  }\n\n  if (this._finalized) return\n  this._finalized = true\n  this.push(END_OF_TAR)\n  this.push(null)\n}\n\nPack.prototype.destroy = function (err) {\n  if (this._destroyed) return\n  this._destroyed = true\n\n  if (err) this.emit('error', err)\n  this.emit('close')\n  if (this._stream && this._stream.destroy) this._stream.destroy()\n}\n\nPack.prototype._encode = function (header) {\n  if (!header.pax) {\n    var buf = headers.encode(header)\n    if (buf) {\n      this.push(buf)\n      return\n    }\n  }\n  this._encodePax(header)\n}\n\nPack.prototype._encodePax = function (header) {\n  var paxHeader = headers.encodePax({\n    name: header.name,\n    linkname: header.linkname,\n    pax: header.pax\n  })\n\n  var newHeader = {\n    name: 'PaxHeader',\n    mode: header.mode,\n    uid: header.uid,\n    gid: header.gid,\n    size: paxHeader.length,\n    mtime: header.mtime,\n    type: 'pax-header',\n    linkname: header.linkname && 'PaxHeader',\n    uname: header.uname,\n    gname: header.gname,\n    devmajor: header.devmajor,\n    devminor: header.devminor\n  }\n\n  this.push(headers.encode(newHeader))\n  this.push(paxHeader)\n  overflow(this, paxHeader.length)\n\n  newHeader.size = header.size\n  newHeader.type = header.type\n  this.push(headers.encode(newHeader))\n}\n\nPack.prototype._read = function (n) {\n  var drain = this._drain\n  this._drain = noop\n  drain()\n}\n\nmodule.exports = Pack\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tar-stream/pack.js\n");

/***/ })

};
;