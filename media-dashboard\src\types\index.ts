// تعريفات الأنواع الشاملة للتطبيق

export interface MediaItem {
  id: string;
  title: string;
  description?: string;
  duration: string;
  type: MediaType;
  category?: string;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  filePath?: string;
  thumbnailPath?: string;
  metadata?: MediaMetadata;
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
  isActive: boolean;
}

export interface MediaMetadata {
  fileSize?: number;
  resolution?: string;
  bitrate?: string;
  codec?: string;
  aspectRatio?: string;
  frameRate?: number;
}

export type MediaType = 
  | 'video' 
  | 'audio' 
  | 'image' 
  | 'document' 
  | 'live' 
  | 'break' 
  | 'commercial' 
  | 'news' 
  | 'program' 
  | 'movie' 
  | 'series' 
  | 'sport';

export interface ScheduleItem {
  id: string;
  mediaId: string;
  startTime: string;
  endTime: string;
  duration: string;
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  date: string;
  channel?: string;
  priority: number;
  isRerun: boolean;
  originalAirDate?: string;
  metadata?: ScheduleMetadata;
  status: ScheduleStatus;
}

export interface ScheduleMetadata {
  backgroundColor?: string;
  textColor?: string;
  notes?: string;
  tags?: string[];
  isLive?: boolean;
  isBreaking?: boolean;
}

export type ScheduleStatus = 'scheduled' | 'live' | 'completed' | 'cancelled' | 'delayed';

export interface User {
  id: string;
  username: string;
  email: string;
  role: UserRole;
  permissions: Permission[];
  isActive: boolean;
  createdAt: string;
  lastLogin?: string;
  profile?: UserProfile;
}

export interface UserProfile {
  firstName?: string;
  lastName?: string;
  avatar?: string;
  phone?: string;
  department?: string;
}

export type UserRole = 'admin' | 'editor' | 'viewer' | 'operator';

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: PermissionAction;
}

export type PermissionAction = 'create' | 'read' | 'update' | 'delete' | 'execute';

export interface AuthContext {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  checkPermission: (resource: string, action: PermissionAction) => boolean;
}

export interface LanguageContext {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  isRTL: boolean;
}

export type Language = 'ar' | 'en';

export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: number;
}

export interface PaginatedResponse<T = unknown> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface FilterOptions {
  search?: string;
  type?: MediaType;
  category?: string;
  dateFrom?: string;
  dateTo?: string;
  isActive?: boolean;
  tags?: string[];
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf';
  includeMetadata: boolean;
  dateRange?: {
    from: string;
    to: string;
  };
  filters?: FilterOptions;
}

export interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: ImportError[];
  data?: MediaItem[] | ScheduleItem[];
}

export interface ImportError {
  row: number;
  field: string;
  value: string;
  message: string;
}

export interface Statistics {
  totalMedia: number;
  totalScheduled: number;
  totalUsers: number;
  mediaByType: Record<MediaType, number>;
  scheduleByDay: Record<string, number>;
  recentActivity: ActivityLog[];
}

export interface ActivityLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  timestamp: string;
  details?: Record<string, unknown>;
}

export interface NotificationMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

export interface TimeSlot {
  start: string;
  end: string;
  duration: number; // in minutes
}

export interface WeeklySchedule {
  [key: string]: ScheduleItem[]; // key is day of week (0-6)
}

export interface DailySchedule {
  date: string;
  items: ScheduleItem[];
  totalDuration: number;
  gaps: TimeSlot[];
}
