/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffer-indexof-polyfill";
exports.ids = ["vendor-chunks/buffer-indexof-polyfill"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffer-indexof-polyfill/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("\nvar initBuffer = __webpack_require__(/*! ./init-buffer */ \"(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js\");\n\nif (!Buffer.prototype.indexOf) {\n    Buffer.prototype.indexOf = function (value, offset) {\n        offset = offset || 0;\n\n        // Always wrap the input as a Buffer so that this method will support any\n        // data type such as array octet, string or buffer.\n        if (typeof value === \"string\" || value instanceof String) {\n            value = initBuffer(value);\n        } else if (typeof value === \"number\" || value instanceof Number) {\n            value = initBuffer([ value ]);\n        }\n\n        var len = value.length;\n\n        for (var i = offset; i <= this.length - len; i++) {\n            var mismatch = false;\n            for (var j = 0; j < len; j++) {\n                if (this[i + j] != value[j]) {\n                    mismatch = true;\n                    break;\n                }\n            }\n\n            if (!mismatch) {\n                return i;\n            }\n        }\n\n        return -1;\n    };\n}\n\nfunction bufferLastIndexOf (value, offset) {\n\n    // Always wrap the input as a Buffer so that this method will support any\n    // data type such as array octet, string or buffer.\n    if (typeof value === \"string\" || value instanceof String) {\n        value = initBuffer(value);\n    } else if (typeof value === \"number\" || value instanceof Number) {\n        value = initBuffer([ value ]);\n    }\n\n    var len = value.length;\n    offset = offset || this.length - len;\n\n    for (var i = offset; i >= 0; i--) {\n        var mismatch = false;\n        for (var j = 0; j < len; j++) {\n            if (this[i + j] != value[j]) {\n                mismatch = true;\n                break;\n            }\n        }\n\n        if (!mismatch) {\n            return i;\n        }\n    }\n\n    return -1;\n}\n\n\nif (Buffer.prototype.lastIndexOf) {\n    // check Buffer#lastIndexOf is usable: https://github.com/nodejs/node/issues/4604\n    if (initBuffer(\"ABC\").lastIndexOf (\"ABC\") === -1)\n        Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n} else {\n    Buffer.prototype.lastIndexOf = bufferLastIndexOf;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-indexof-polyfill/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js":
/*!*************************************************************!*\
  !*** ./node_modules/buffer-indexof-polyfill/init-buffer.js ***!
  \*************************************************************/
/***/ ((module) => {

eval("module.exports = function initBuffer(val) {\n  // assume old version\n    var nodeVersion = process && process.version ? process.version : \"v5.0.0\";\n    var major = nodeVersion.split(\".\")[0].replace(\"v\", \"\");\n    return major < 6\n      ? new Buffer(val)\n      : Buffer.from(val);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYnVmZmVyLWluZGV4b2YtcG9seWZpbGwvaW5pdC1idWZmZXIuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXGJ1ZmZlci1pbmRleG9mLXBvbHlmaWxsXFxpbml0LWJ1ZmZlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGluaXRCdWZmZXIodmFsKSB7XG4gIC8vIGFzc3VtZSBvbGQgdmVyc2lvblxuICAgIHZhciBub2RlVmVyc2lvbiA9IHByb2Nlc3MgJiYgcHJvY2Vzcy52ZXJzaW9uID8gcHJvY2Vzcy52ZXJzaW9uIDogXCJ2NS4wLjBcIjtcbiAgICB2YXIgbWFqb3IgPSBub2RlVmVyc2lvbi5zcGxpdChcIi5cIilbMF0ucmVwbGFjZShcInZcIiwgXCJcIik7XG4gICAgcmV0dXJuIG1ham9yIDwgNlxuICAgICAgPyBuZXcgQnVmZmVyKHZhbClcbiAgICAgIDogQnVmZmVyLmZyb20odmFsKTtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffer-indexof-polyfill/init-buffer.js\n");

/***/ })

};
;