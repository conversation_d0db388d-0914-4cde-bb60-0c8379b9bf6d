'use client';

import React, { useState, useEffect } from 'react';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useTranslation } from 'react-i18next';
import { useTranslatedToast } from '@/hooks/useTranslatedToast';
import './daily-schedule.css';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  duration: string;
  segments?: Segment[];
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
}

interface Segment {
  id: string;
  name: string;
  timeIn: string;
  timeOut: string;
  duration: string;
  segmentCode: string;
}

interface ScheduleItem {
  id: string;
  mediaItemId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isRerun: boolean;
  mediaItem: MediaItem;
}

interface GridRow {
  id: string;
  type: 'segment' | 'filler' | 'empty';
  time?: string;
  content?: string;
  mediaItemId?: string;
  segmentId?: string;
  segmentCode?: string; // كود السيجمنت للسيرفرات
  duration?: string;
  canDelete?: boolean;
  isRerun?: boolean;
  isTemporary?: boolean;
  originalStartTime?: string;
  targetTime?: string;
  mediaType?: string; // نوع المادة للفواصل (PROMO, STING, FILL_IN, FILLER)
}

export default function DailySchedulePage() {
  const { user, isViewer } = useAuth();
  const { t, i18n } = useTranslation('common');

  // Get current language and direction
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';
  const { showSuccessToast, showErrorToast, ToastContainer } = useTranslatedToast();
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [startTime, setStartTime] = useState('08:00:00');
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([]);
  const [availableMedia, setAvailableMedia] = useState<MediaItem[]>([]);
  const [gridRows, setGridRows] = useState<GridRow[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('ALL');
  const [loading, setLoading] = useState(false);
  const [isImported, setIsImported] = useState(false); // حالة الاستيراد
  const [weeklySchedule, setWeeklySchedule] = useState<any[]>([]);
  const [showWeeklySchedule, setShowWeeklySchedule] = useState(false);
  const [readOnlyMode, setReadOnlyMode] = useState(false);
  
  // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل
  useEffect(() => {
    if (isViewer) {
      setReadOnlyMode(true);
    }
  }, [isViewer]);

  // تهيئة التاريخ الحالي
  useEffect(() => {
    const today = new Date();
    setSelectedDate(today.toISOString().split('T')[0]);
  }, []);

  // لا نجلب البيانات تلقائياً - فقط عند الاستيراد
  // useEffect(() => {
  //   if (selectedDate) {
  //     fetchScheduleData();
  //   }
  // }, [selectedDate]);

  // جلب بيانات الجدول الإذاعي
  const fetchScheduleData = async () => {
    setLoading(true);
    try {
      console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);
      console.log('🌐 URL المطلوب:', `/api/daily-schedule?date=${selectedDate}`);

      const response = await fetch(`/api/daily-schedule?date=${selectedDate}`);
      console.log('📡 حالة الاستجابة:', response.status, response.statusText);

      if (!response.ok) {
        console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('📦 البيانات المستلمة:', data);

      if (data.success) {
        console.log('✅ استجابة ناجحة من API:', {
          scheduleItemsCount: data.data.scheduleItems?.length || 0,
          availableMediaCount: data.data.availableMedia?.length || 0,
          scheduleRowsCount: data.data.scheduleRows?.length || 0,
          date: data.data.date,
          dayOfWeek: data.data.dayOfWeek
        });

        setScheduleItems(data.data.scheduleItems);
        setAvailableMedia(data.data.availableMedia || []);

        console.log(`📚 تم تحديد ${data.data.availableMedia?.length || 0} مادة متاحة في القائمة الجانبية`);
        
        // التحقق من وجود صفوف في الجدول
        if (data.data.scheduleRows && data.data.scheduleRows.length > 0) {
          // تطبيق الحماية على المواد الأساسية المحفوظة
          const protectedRows = data.data.scheduleRows.map((row: any) => {
            if (row.type === 'segment' && !row.isTemporary) {
              return { ...row, canDelete: false }; // حماية المواد الأساسية
            }
            return row;
          });
          setGridRows(protectedRows);
          console.log('📝 تم تحميل', protectedRows.length, 'صف من الجدول المحفوظ مع تطبيق الحماية');
        } else {
          // إذا لم تكن هناك صفوف، نحاول بناء الجدول من الخريطة البرامجية
          console.log('⚠️ لم يتم العثور على جدول محفوظ، سيتم محاولة بناء الجدول من الخريطة البرامجية');
          await buildScheduleFromWeekly();
        }

        if (data.fromSavedFile) {
          console.log('📂 تم تحميل جدول محفوظ مسبقاً');
          console.log('💾 تاريخ الحفظ:', data.savedAt);
          console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);
        } else {
          console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');
          console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');
        }

        console.log('📦 Available media:', data.data.availableMedia?.length || 0);

        // عرض عينة من المواد المتاحة
        if (data.data.availableMedia && data.data.availableMedia.length > 0) {
          console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));
        } else {
          console.log('⚠️ لا توجد مواد متاحة في القائمة الجانبية');
        }
      } else {
        console.log('❌ فشل في جلب البيانات:', data.error || 'خطأ غير محدد');
        console.log('📋 تفاصيل الاستجابة:', data);
      }

      // جلب الجدول الأسبوعي للمراجعة
      await fetchWeeklySchedule();
    } catch (error) {
      console.error('❌ خطأ في جلب البيانات:', error);
      console.error('📋 تفاصيل الخطأ:', error.message);
    } finally {
      setLoading(false);
      console.log('✅ انتهى تحميل البيانات');
    }
  };
  
  // البحث عن الاستنجات المطابقة لمادة معينة
  const findMatchingStings = (mediaName: string, stingType: 'سنعود' | 'عدنا') => {
    return mediaItems.filter(item =>
      item.name === mediaName &&
      item.type === stingType &&
      item.status === 'VALID' &&
      item.tx === true
    );
  };

  // إضافة استنجات تلقائية بين السيجمنت
  const addAutomaticStings = (scheduleRows: any[], mediaName: string) => {
    console.log(`🔍 البحث عن استنجات للمادة: ${mediaName}`);

    // البحث عن استنجات "سنعود" و "عدنا" لهذه المادة
    const snawodStings = findMatchingStings(mediaName, 'سنعود');
    const odnaStings = findMatchingStings(mediaName, 'عدنا');

    console.log(`📦 وجدت ${snawodStings.length} استنج "سنعود" و ${odnaStings.length} استنج "عدنا" للمادة: ${mediaName}`);

    if (snawodStings.length === 0 && odnaStings.length === 0) {
      console.log(`⚠️ لا توجد استنجات للمادة: ${mediaName}`);
      return scheduleRows; // لا توجد استنجات متطابقة
    }

    // البحث عن المادة الأساسية في الجدول
    const mediaRows = scheduleRows
      .map((row, index) => ({ ...row, originalIndex: index }))
      .filter(row =>
        row.type === 'segment' &&
        row.content &&
        row.content.includes(mediaName) &&
        !row.isAutoGenerated // تجنب المواد المولدة تلقائياً
      );

    console.log(`🎬 وجدت ${mediaRows.length} صف للمادة: ${mediaName}`);

    if (mediaRows.length <= 1) {
      console.log(`⚠️ المادة ${mediaName} لها سيجمنت واحد فقط - لا حاجة للاستنجات`);
      return scheduleRows; // لا حاجة للاستنجات إذا كان سيجمنت واحد فقط
    }

    const newRows = [...scheduleRows];
    const insertions = [];

    // إضافة استنجات بين كل سيجمنت والتالي
    for (let i = 0; i < mediaRows.length - 1; i++) {
      const currentRow = mediaRows[i];

      // إضافة "سنعود" بعد السيجمنت الحالي
      if (snawodStings.length > 0) {
        const snawodSting = snawodStings[0];
        insertions.push({
          afterIndex: currentRow.originalIndex,
          sting: {
            id: `auto_snawod_${currentRow.originalIndex}_${Date.now()}_${Math.random()}`,
            type: 'filler',
            content: `${snawodSting.name} (تلقائي)`,
            duration: calculateTotalDuration(snawodSting),
            mediaType: 'سنعود',
            canDelete: true,
            isAutoGenerated: true
          }
        });
      }

      // إضافة "عدنا" قبل السيجمنت التالي (بعد "سنعود")
      if (odnaStings.length > 0) {
        const odnaSting = odnaStings[0];
        insertions.push({
          afterIndex: currentRow.originalIndex + (snawodStings.length > 0 ? 1 : 0), // بعد "سنعود" إذا وجد
          sting: {
            id: `auto_odna_${currentRow.originalIndex}_${Date.now()}_${Math.random()}`,
            type: 'filler',
            content: `${odnaSting.name} (تلقائي)`,
            duration: calculateTotalDuration(odnaSting),
            mediaType: 'عدنا',
            canDelete: true,
            isAutoGenerated: true
          }
        });
      }
    }

    // إدراج الاستنجات من النهاية للبداية لتجنب تغيير الفهارس
    insertions.sort((a, b) => b.afterIndex - a.afterIndex);
    insertions.forEach(insertion => {
      newRows.splice(insertion.afterIndex + 1, 0, insertion.sting);
    });

    console.log(`✨ تم إضافة ${insertions.length} استنج تلقائي للمادة: ${mediaName}`);
    return newRows;
  };

  // بناء الجدول من الخريطة البرامجية
  const buildScheduleFromWeekly = async () => {
    try {
      console.log('🔄 محاولة بناء الجدول من الخريطة البرامجية للتاريخ:', selectedDate);

      const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekStartStr = weekStart.toISOString().split('T')[0];

      console.log('📅 تفاصيل التاريخ:');
      console.log('  - التاريخ الأصلي:', selectedDate);
      console.log('  - التاريخ المحول:', date.toISOString().split('T')[0]);
      console.log('  - يوم الأسبوع:', date.getDay(), '(0=أحد, 1=اثنين, 2=ثلاثاء, 3=أربعاء, 4=خميس, 5=جمعة, 6=سبت)');
      console.log('  - بداية الأسبوع:', weekStartStr);
      console.log('  - نهاية الأسبوع:', new Date(new Date(weekStartStr).getTime() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);

      console.log('🌐 طلب البيانات من:', `/api/weekly-schedule?weekStart=${weekStartStr}`);
      const response = await fetch(`/api/weekly-schedule?weekStart=${weekStartStr}`);
      console.log('📡 حالة الاستجابة:', response.status, response.statusText);

      if (!response.ok) {
        console.error('❌ خطأ في الاستجابة:', response.status);
        return;
      }

      const data = await response.json();
      console.log('📦 البيانات المستلمة من weekly-schedule:', data);
      
      if (data.success && data.data && data.data.scheduleItems) {
        const dayOfWeek = date.getDay();
        console.log('📅 اليوم المطلوب:', dayOfWeek);
        console.log('📦 إجمالي المواد في الخريطة:', data.data.scheduleItems.length);

        // عرض جميع المواد للتشخيص
        console.log('🔍 جميع المواد في الخريطة:');
        data.data.scheduleItems.forEach((item: any, index: number) => {
          console.log(`  ${index + 1}. ${item.mediaItem?.name || 'بدون اسم'} - يوم ${item.dayOfWeek} - ${item.startTime} - إعادة: ${item.isRerun}`);
        });

        // فلترة المواد الخاصة بهذا اليوم
        const dayItems = data.data.scheduleItems.filter((item: any) => {
          const matches = item.dayOfWeek === dayOfWeek && item.mediaItem;
          if (matches) {
            console.log(`✅ مادة متطابقة: ${item.mediaItem?.name} - يوم ${item.dayOfWeek} - ${item.startTime}`);
          } else {
            console.log(`❌ مادة غير متطابقة: ${item.mediaItem?.name || 'بدون اسم'} - يوم ${item.dayOfWeek} (مطلوب: ${dayOfWeek}) - mediaItem: ${!!item.mediaItem}`);
          }
          return matches;
        });

        console.log('📋 عدد المواد المطابقة لهذا اليوم:', dayItems.length);
        
        if (dayItems.length > 0) {
          // ترتيب المواد حسب وقت البداية
          dayItems.sort((a: any, b: any) => a.startTime.localeCompare(b.startTime));
          
          // بناء صفوف الجدول
          const newRows: GridRow[] = [];
          let currentTime = '08:00:00';
          
          // إضافة صف فارغ في البداية
          newRows.push({
            id: `empty_start_${Date.now()}`,
            type: 'empty',
            time: currentTime,
            canDelete: true
          });
          
          // إضافة المواد
          for (const item of dayItems) {
            // إضافة المادة
            const mediaItem = item.mediaItem;
            const itemName = mediaItem.name;
            const itemType = mediaItem.type;
            const itemId = mediaItem.id;
            
            // إضافة تفاصيل المادة
            const details = [];
            if (mediaItem.episodeNumber) details.push(`ح${mediaItem.episodeNumber}`);
            if (mediaItem.seasonNumber && mediaItem.seasonNumber > 0) details.push(`م${mediaItem.seasonNumber}`);
            if (mediaItem.partNumber) details.push(`ج${mediaItem.partNumber}`);
            
            const detailsText = details.length > 0 ? ` (${details.join(' - ')})` : '';
            
            // تحديد نوع المادة
            let rowType: 'segment' | 'filler' = 'segment';
            let itemContent = `${itemName}${detailsText}`;
            
            if (['PROMO', 'STING', 'FILLER', 'FILL_IN'].includes(itemType)) {
              rowType = 'filler';
              itemContent = `${itemName}${detailsText} - ${itemType}`;
            }
            
            // حساب المدة
            let itemDuration = '00:01:00';
            if (mediaItem.duration) {
              itemDuration = mediaItem.duration;
            } else if (mediaItem.segments && mediaItem.segments.length > 0) {
              let totalSeconds = 0;
              mediaItem.segments.forEach((segment: any) => {
                if (segment.duration) {
                  const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
                  totalSeconds += hours * 3600 + minutes * 60 + seconds;
                }
              });
              
              if (totalSeconds > 0) {
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const secs = totalSeconds % 60;
                itemDuration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
              }
            }
            
            // إضافة الصف
            const newRow = {
              id: `${rowType}_${Date.now()}_${itemId}`,
              type: rowType,
              time: item.startTime,
              content: itemContent,
              mediaItemId: itemId,
              duration: itemDuration,
              canDelete: false, // المواد الأساسية من الخريطة محمية من الحذف
              isRerun: item.isRerun,
              isTemporary: false,
              originalStartTime: item.startTime
            };
            console.log('🔒 إضافة مادة أساسية محمية:', newRow.content, 'canDelete:', newRow.canDelete);
            newRows.push(newRow);
            
            // تحديث الوقت الحالي
            currentTime = calculateNextTime(item.startTime, itemDuration);
          }
          
          // إضافة الاستنجات التلقائية لكل مادة
          let finalRows = [...newRows];
          const processedMediaNames = new Set<string>();

          dayItems.forEach((item: any) => {
            const mediaName = item.mediaItem?.name;
            if (mediaName && !processedMediaNames.has(mediaName)) {
              processedMediaNames.add(mediaName);
              finalRows = addAutomaticStings(finalRows, mediaName);
            }
          });

          // إضافة صفوف فارغة في النهاية
          for (let i = 0; i < 8; i++) {
            finalRows.push({
              id: `empty_end_${Date.now()}_${i}`,
              type: 'empty',
              canDelete: true
            });
          }

          // تحديث الجدول
          setGridRows(finalRows);
          console.log('✅ تم بناء الجدول من الخريطة البرامجية:', finalRows.length, 'صف');

          // إعادة حساب الأوقات
          recalculateTimes(finalRows);
        } else {
          console.log('⚠️ لا توجد مواد لهذا اليوم في الخريطة البرامجية');
          console.log('💡 تحقق من:');
          console.log('  - وجود مواد في الخريطة البرامجية لهذا اليوم');
          console.log('  - صحة يوم الأسبوع المحسوب');
          console.log('  - وجود mediaItem في كل مادة');
        }
      } else {
        console.log('❌ فشل في جلب بيانات الخريطة البرامجية');
        console.log('📋 تفاصيل الاستجابة:', data);
      }
    } catch (error) {
      console.error('❌ خطأ في بناء الجدول من الخريطة البرامجية:', error);
      console.error('📋 تفاصيل الخطأ:', error.message);
    }
  };

  // جلب الجدول الأسبوعي للمراجعة
  const fetchWeeklySchedule = async () => {
    try {
      const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekStartStr = weekStart.toISOString().split('T')[0];

      const response = await fetch(`/api/weekly-schedule?weekStart=${weekStartStr}`);
      const data = await response.json();

      console.log('📊 استجابة API للجدول الأسبوعي:', data);

      if (data.success && data.data) {
        // استخراج مواد اليوم المحدد من scheduleItems
        const dayOfWeek = new Date(selectedDate).getDay();
        const daySchedule = [];

        console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);
        console.log('📦 البيانات المتاحة:', data.data);

        // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)
        if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {
          console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);

          const dayItems = data.data.scheduleItems
            .filter((item: any) => {
              console.log('🔍 فحص المادة:', item.mediaItem?.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);
              return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء
            })
            .sort((a: any, b: any) => {
              // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59
              const timeA = a.startTime;
              const timeB = b.startTime;

              const getTimeOrder = (time: string) => {
                const hour = parseInt(time.split(':')[0]);
                if (hour >= 8 && hour < 18) return 1; // صباح ومساء
                if (hour >= 18) return 2; // برايم تايم
                return 3; // منتصف الليل والفجر
              };

              const orderA = getTimeOrder(timeA);
              const orderB = getTimeOrder(timeB);

              if (orderA !== orderB) return orderA - orderB;
              return timeA.localeCompare(timeB);
            });

          console.log('✅ مواد اليوم المفلترة:', dayItems.length);

          dayItems.forEach((item: any) => {
            const scheduleItem = {
              time: item.startTime,
              name: item.mediaItem?.name || 'مادة غير محددة',
              episodeNumber: item.episodeNumber || item.mediaItem?.episodeNumber,
              partNumber: item.partNumber || item.mediaItem?.partNumber,
              seasonNumber: item.seasonNumber || item.mediaItem?.seasonNumber,
              isRerun: item.isRerun || false
            };
            daySchedule.push(scheduleItem);
            console.log('📺 إضافة مادة للخريطة:', scheduleItem);
          });
        } else {
          console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');
        }

        setWeeklySchedule(daySchedule);
        console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');
      } else {
        console.log('❌ فشل في جلب البيانات أو البيانات فارغة');
        setWeeklySchedule([]);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);
      setWeeklySchedule([]);
    }
  };



  // Filter available media
  const filteredMedia = availableMedia.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'ALL' || item.type === filterType;
    return matchesSearch && matchesType;
  });

  // أنواع المواد للفلترة
  const mediaTypes = ['ALL', 'PROGRAM', 'SERIES', 'FILM', 'SONG', 'PROMO', 'STING', 'FILLER', 'NEXT', 'NOW', 'سنعود', 'عدنا', 'MINI', 'CROSS'];

  // دالة ترجمة أنواع المواد
  const getTypeLabel = (type: string) => {
    const typeLabels: { [key: string]: string } = {
      'ALL': t('schedule.allTypes'),
      'PROGRAM': t('schedule.types.program'),
      'SERIES': t('schedule.types.series'),
      'FILM': t('schedule.types.film'),
      'MOVIE': t('schedule.types.film'), // للتوافق مع البيانات القديمة
      'SONG': t('schedule.types.song'),
      'STING': t('schedule.types.sting'),
      'FILL_IN': t('schedule.types.fillIn'),
      'FILLER': t('schedule.types.filler'),
      'PROMO': t('schedule.types.promo'),
      'NEXT': t('schedule.types.next'),
      'NOW': t('schedule.types.now'),
      'سنعود': t('schedule.types.snawod'),
      'عدنا': t('schedule.types.odna'),
      'MINI': t('schedule.types.mini'),
      'CROSS': t('schedule.types.cross')
    };
    return typeLabels[type] || type;
  };

  // إضافة صف فارغ
  const addEmptyRow = (afterIndex: number) => {
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    const newRows = [...gridRows];
    const newRow: GridRow = {
      id: `empty_${Date.now()}`,
      type: 'empty',
      canDelete: true
    };
    newRows.splice(afterIndex + 1, 0, newRow);
    setGridRows(newRows);

    // استعادة موضع التمرير
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 50);
  };

  // إضافة صفوف فارغة متعددة
  const addMultipleEmptyRows = (afterIndex: number, count: number = 8) => {
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    const newRows = [...gridRows];
    for (let i = 0; i < count; i++) {
      const newRow: GridRow = {
        id: `empty_${Date.now()}_${i}`,
        type: 'empty',
        canDelete: true
      };
      newRows.splice(afterIndex + 1 + i, 0, newRow);
    }
    setGridRows(newRows);
    console.log(`✅ تم إضافة ${count} صف فارغ`);

    // استعادة موضع التمرير
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 50);
  };

  // التحقق من الحاجة لإضافة صفوف فارغة
  const checkAndAddEmptyRows = (currentIndex: number) => {
    const currentRows = gridRows;
    const nextEmptyIndex = currentRows.findIndex((row, index) =>
      index > currentIndex && row.type === 'empty'
    );

    // إذا لم توجد صفوف فارغة بعد الفاصل الحالي
    if (nextEmptyIndex === -1) {
      console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 8 صفوف');
      addMultipleEmptyRows(currentIndex, 8);
    } else {
      console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);
    }
  };

  // حذف صف فارغ
  const deleteRow = (rowId: string) => {
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    const newRows = gridRows.filter(row => row.id !== rowId);
    recalculateTimes(newRows);

    // استعادة موضع التمرير بعد الحذف
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 100);
  };

  // حذف سيجمنت مع حماية المواد الأساسية
  const deleteSegment = (rowId: string) => {
    const row = gridRows.find(r => r.id === rowId);

    // حماية مضاعفة للمواد الأساسية
    if (row && (row.type === 'segment' && (!row.isTemporary || !row.canDelete))) {
      showErrorToast('permissionDenied');
      return;
    }

    // التأكد من أن المادة قابلة للحذف
    if (row && !row.canDelete) {
      showErrorToast('permissionDenied');
      return;
    }

    if (confirm(t('schedule.confirmDeleteSegment'))) {
      const newRows = gridRows.filter(row => row.id !== rowId);
      recalculateTimes(newRows);
      console.log('🗑️ تم حذف السيجمنت');
    }
  };

  // حذف فاصل بدون تأكيد
  const deleteFiller = (rowId: string) => {
    const newRows = gridRows.filter(row => row.id !== rowId);
    recalculateTimes(newRows);
    console.log('🗑️ تم حذف الفاصل');
  };

  // تحريك صف لأعلى
  const moveRowUp = (index: number) => {
    if (index <= 0) return;

    const newRows = [...gridRows];
    [newRows[index - 1], newRows[index]] = [newRows[index], newRows[index - 1]];

    recalculateTimes(newRows);
    console.log('⬆️ تم تحريك الصف لأعلى');
  };

  // تحريك صف لأسفل
  const moveRowDown = (index: number) => {
    if (index >= gridRows.length - 1) return;

    const newRows = [...gridRows];
    [newRows[index], newRows[index + 1]] = [newRows[index + 1], newRows[index]];

    recalculateTimes(newRows);
    console.log('⬇️ تم تحريك الصف لأسفل');
  };

  // معالجة سحب الصفوف داخل الجدول
  const handleRowDragStart = (e: React.DragEvent, index: number) => {
    e.dataTransfer.setData('text/plain', index.toString());
    e.dataTransfer.effectAllowed = 'move';
  };

  // معالجة إسقاط الصفوف داخل الجدول
  const handleRowDrop = (e: React.DragEvent, targetIndex: number) => {
    e.preventDefault();
    const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));

    if (sourceIndex === targetIndex) return;

    const newRows = [...gridRows];
    const [movedRow] = newRows.splice(sourceIndex, 1);
    newRows.splice(targetIndex, 0, movedRow);

    recalculateTimes(newRows);
    console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);

    // تثبيت الجدول بعد السحب
    setTimeout(() => {
      const gridElement = document.querySelector('.schedule-grid');
      if (gridElement) {
        gridElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  // معالجة إسقاط المواد
  const handleDrop = (e: React.DragEvent, rowIndex: number) => {
    e.preventDefault();
    
    // حفظ موضع التمرير الحالي قبل أي تغيير
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;
    const rowElement = e.currentTarget.closest('.grid-row');
    const rowOffsetTop = rowElement?.getBoundingClientRect().top || 0;
    
    try {
      // محاولة الحصول على البيانات بطرق مختلفة
      let mediaData;
      const jsonData = e.dataTransfer.getData('application/json');
      const textData = e.dataTransfer.getData('text/plain');

      console.log('📥 البيانات المسحوبة:', { jsonData, textData });

      // التحقق من أن هذا ليس سحب صف داخلي
      if (textData && !jsonData && /^\d+$/.test(textData)) {
        console.log('🔄 هذا سحب صف داخلي، تجاهل');
        return;
      }

      if (jsonData) {
        mediaData = JSON.parse(jsonData);
      } else if (textData) {
        try {
          mediaData = JSON.parse(textData);
        } catch {
          console.error('❌ لا يمكن تحليل البيانات المسحوبة');
          return;
        }
      } else {
        console.error('❌ لا توجد بيانات مسحوبة');
        return;
      }

      // التحقق من أن الصف فارغ
      const targetRow = gridRows[rowIndex];
      if (targetRow.type !== 'empty') {
        showErrorToast('invalidData');
        return;
      }

      console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);

      // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر
      if (!mediaData || typeof mediaData !== 'object' || typeof mediaData === 'string' || Array.isArray(mediaData)) {
        console.error('❌ بيانات المادة غير صحيحة:', mediaData);
        console.error('❌ نوع البيانات:', typeof mediaData);
        console.error('❌ هل هو مصفوفة؟', Array.isArray(mediaData));
        return;
      }

      // التأكد من وجود الاسم
      const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';
      const itemType = mediaData.type || 'UNKNOWN';
      const itemId = mediaData.id || Date.now().toString();

      console.log('📥 معلومات المادة:', {
        name: itemName,
        type: itemType,
        id: itemId,
        segments: mediaData.segments?.length || 0
      });

      // تحديد نوع المادة المسحوبة
      let dragItemType: 'filler' | 'segment' = 'filler';
      let itemContent = itemName;

      // إضافة تفاصيل المادة
      const details = [];
      if (mediaData.episodeNumber) details.push(`ح${mediaData.episodeNumber}`);
      if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(`م${mediaData.seasonNumber}`);
      if (mediaData.partNumber) details.push(`ج${mediaData.partNumber}`);

      const detailsText = details.length > 0 ? ` (${details.join(' - ')})` : '';

      // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات
      if (['PROMO', 'STING', 'FILLER', 'FILL_IN'].includes(itemType)) {
        dragItemType = 'filler';
        itemContent = `${itemName}${detailsText} - ${itemType}`;
        // تخزين نوع المادة لاستخدامه في تمييز الألوان
        mediaData.mediaType = itemType;
      } else {
        dragItemType = 'segment';
        itemContent = `${itemName}${detailsText} (مادة إضافية)`;
      }

      // حساب المدة الحقيقية للمادة
      let itemDuration = '00:01:00'; // مدة افتراضية

      console.log('🔍 تحليل مدة المادة:', {
        name: itemName,
        hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),
        segmentsCount: mediaData.segments?.length || 0,
        hasDuration: !!mediaData.duration,
        directDuration: mediaData.duration
      });

      if (mediaData.segments && mediaData.segments.length > 0) {
        // حساب إجمالي مدة جميع السيجمنتات
        let totalSeconds = 0;

        mediaData.segments.forEach((segment: any, index: number) => {
          if (segment.duration) {
            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
            const segmentSeconds = hours * 3600 + minutes * 60 + seconds;
            totalSeconds += segmentSeconds;

            console.log(`  📺 سيجمنت ${index + 1}: ${segment.duration} (${segmentSeconds} ثانية)`);
          }
        });

        if (totalSeconds > 0) {
          const hours = Math.floor(totalSeconds / 3600);
          const minutes = Math.floor((totalSeconds % 3600) / 60);
          const secs = totalSeconds % 60;
          itemDuration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        console.log('📊 حساب مدة المادة من السيجمنتات:', {
          name: itemName,
          segments: mediaData.segments.length,
          totalSeconds,
          finalDuration: itemDuration
        });
      } else if (mediaData.duration) {
        // استخدام المدة المباشرة إذا كانت موجودة
        itemDuration = mediaData.duration;
        console.log('📊 استخدام مدة مباشرة:', itemDuration);
      } else {
        console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);
      }

      // إنشاء كود للمادة المسحوبة
      let itemCode = '';
      if (mediaData.segmentCode) {
        itemCode = mediaData.segmentCode;
      } else if (mediaData.id) {
        itemCode = `${itemType}_${mediaData.id}`;
      } else {
        itemCode = `${itemType}_${Date.now().toString().slice(-6)}`;
      }

      // إنشاء صف جديد مع المدة الحقيقية
      const newRow: GridRow = {
        id: `dropped_${Date.now()}`,
        type: dragItemType,
        content: itemContent,
        mediaItemId: itemId,
        segmentCode: itemCode, // كود المادة للسيرفرات
        duration: itemDuration,
        canDelete: true
      };
      
      // إضافة نوع المادة للفواصل لتمييزها بألوان مختلفة
      if (dragItemType === 'filler' && ['PROMO', 'STING', 'FILL_IN', 'FILLER'].includes(itemType)) {
        newRow.mediaType = itemType;
        console.log(`🎨 تم تعيين نوع المادة: ${itemType} للصف الجديد`);
      }

      // التأكد من أن الصف المستهدف فارغ
      if (gridRows[rowIndex].type !== 'empty') {
        console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);
        return;
      }

      // استبدال الصف الفارغ مباشرة
      const newRows = [...gridRows];
      newRows[rowIndex] = newRow;

      console.log('✅ تم إضافة المادة:', {
        name: itemName,
        type: dragItemType,
        duration: itemDuration,
        position: rowIndex,
        content: itemContent,
        beforeType: gridRows[rowIndex].type,
        afterType: newRow.type
      });

      // تحديث الصفوف مباشرة
      setGridRows(newRows);

      // إعادة حساب الأوقات بعد تأخير قصير
      setTimeout(() => {
        recalculateTimes(newRows);

        // التحقق من الحاجة لإضافة صفوف فارغة
        checkAndAddEmptyRows(rowIndex);

        // تثبيت الجدول بعد إسقاط المادة
        setTimeout(() => {
          const gridElement = document.querySelector('.schedule-grid');
          if (gridElement) {
            gridElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
            console.log('📍 تم تثبيت الجدول بعد إسقاط المادة');
          }
        }, 100);
      }, 50);

    } catch (error) {
      console.error('❌ خطأ في إسقاط المادة:', error);
      
      // في حالة حدوث خطأ، نستعيد موضع التمرير على أي حال
      setTimeout(() => {
        if (gridBody) {
          gridBody.scrollTop = currentScrollTop;
        }
      }, 100);
    }
  };

  // إعادة حساب الأوقات مثل Excel
  const recalculateTimes = (rows: GridRow[]) => {
    const newRows = [...rows];
    // استخدام وقت البداية المحدد في الاستيراد أو 08:00:00 كافتراضي
    let currentTime = startTime || '08:00:00';
    let hasFillers = false; // هل تم إضافة فواصل؟

    // التحقق من وجود فواصل
    hasFillers = rows.some(row => row.type === 'filler');

    console.log('🔄 بدء إعادة حساب الأوقات من', currentTime, hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');

    for (let i = 0; i < newRows.length; i++) {
      const row = newRows[i];

      if (row.type === 'segment' || row.type === 'filler') {
        // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل
        if (i === 0 || hasFillers) {
          newRows[i] = { ...row, time: currentTime };
        } else {
          newRows[i] = { ...row, time: undefined };
        }

        if (row.duration) {
          // حساب الوقت التالي بناءً على المدة
          const nextTime = calculateNextTime(currentTime, row.duration);

          console.log(`⏰ ${row.type}: "${row.content}" - من ${currentTime} إلى ${nextTime} (مدة: ${row.duration})`);

          currentTime = nextTime;
        }
      } else if (row.type === 'empty') {
        // الصفوف الفارغة لا تؤثر على الوقت
        newRows[i] = { ...row, time: undefined };
      }

      // التحقق من الوصول لوقت مادة أساسية
      if (row.originalStartTime && hasFillers) {
        const targetMinutes = timeToMinutes(row.originalStartTime);
        const currentMinutes = timeToMinutes(currentTime);
        const difference = targetMinutes - currentMinutes;

        if (Math.abs(difference) > 5) { // فرق أكثر من 5 دقائق
          console.log(`⚠️ انحراف زمني: المادة "${row.content}" مجدولة في ${row.originalStartTime} لكن ستدخل في ${currentTime} (فرق: ${difference} دقيقة)`);
        } else {
          console.log(`✅ توقيت صحيح: المادة "${row.content}" ستدخل في ${currentTime} (مجدولة: ${row.originalStartTime})`);
        }
      }
    }

    console.log(`🏁 انتهاء الحساب - الوقت النهائي: ${currentTime}`);

    // حفظ موضع التمرير قبل التحديث
    const gridBody = document.querySelector('.grid-body');
    const currentScrollTop = gridBody?.scrollTop || 0;

    // تحديث الصفوف دائماً لضمان التحديث الصحيح
    setGridRows(newRows);

    // استعادة موضع التمرير بعد التحديث
    setTimeout(() => {
      if (gridBody) {
        gridBody.scrollTop = currentScrollTop;
      }
    }, 50);
  };

  // تحويل الوقت إلى دقائق
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // حساب المدة الإجمالية للمادة بدقة
  const calculateTotalDuration = (item: any): string => {
    if (item.segments && item.segments.length > 0) {
      let totalSeconds = 0;

      item.segments.forEach((segment: any) => {
        if (segment.duration) {
          const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
          totalSeconds += hours * 3600 + minutes * 60 + seconds;
        }
      });

      if (totalSeconds > 0) {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const secs = totalSeconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
    }

    return item.duration || '00:01:00';
  };



  // Export broadcast schedule to Excel
  const exportDailySchedule = async () => {
    try {
      console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');

      if (!selectedDate) {
        showErrorToast('invalidData');
        return;
      }

      // فلترة البيانات بناءً على الوقت المحدد
      let filteredRows = gridRows.filter(row =>
        (row.type === 'segment' || (row.type === 'filler' && row.content)) &&
        !row.isTemporary
      );

      // إذا تم تحديد وقت بداية، فلتر البيانات من ذلك الوقت
      if (startTime && startTime !== '08:00:00') {
        filteredRows = filteredRows.filter(row => {
          if (!row.time) return false;
          return row.time >= startTime;
        });
        console.log(`📊 تصدير البيانات بدءًا من الوقت ${startTime} - عدد المواد: ${filteredRows.length}`);
      }

      // إرسال البيانات المفلترة مع طلب التصدير
      const currentData = {
        date: selectedDate,
        startTime: startTime || '08:00:00',
        scheduleRows: filteredRows
      };

      const response = await fetch(`/api/export-daily-schedule-new?date=${selectedDate}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currentData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Daily_Schedule_${selectedDate}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      console.log('✅ تم تصدير الجدول الإذاعي بنجاح');
      showSuccessToast('exportSuccess');
    } catch (error) {
      console.error('❌ خطأ في تصدير الجدول:', error);
      showErrorToast('exportFailed');
    }
  };

  // حساب الوقت التالي بناءً على المدة (بدقة الثواني)
  const calculateNextTime = (startTime: string, duration: string): string => {
    // تحليل وقت البداية
    const startParts = startTime.split(':');
    const startHours = parseInt(startParts[0]);
    const startMins = parseInt(startParts[1]);
    const startSecs = parseInt(startParts[2] || '0');

    // تحليل المدة
    const [durHours, durMins, durSecs] = duration.split(':').map(Number);

    // حساب إجمالي الثواني
    let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;
    totalSeconds += durHours * 3600 + durMins * 60 + durSecs;

    // تحويل إلى ساعات ودقائق وثواني
    const hours = Math.floor(totalSeconds / 3600) % 24;
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // استيراد الجدول من وقت محدد
  const importFromTime = async () => {
    if (!selectedDate) {
      showErrorToast('invalidData');
      return;
    }

    if (!startTime) {
      showErrorToast('invalidData');
      return;
    }

    // التحقق من صحة تنسيق الوقت
    const timePattern = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])(?::([0-5][0-9]))?$/;
    if (!timePattern.test(startTime)) {
      showErrorToast('timeFormatError');
      return;
    }

    setLoading(true);
    try {
      console.log('🔍 جلب وفلترة المواد بدءًا من الوقت:', startTime);

      // جلب البيانات من الخادم أولاً
      const response = await fetch(`/api/daily-schedule?date=${selectedDate}`);
      const data = await response.json();

      if (data.success) {
        // فلترة الصفوف التي تبدأ من الوقت المحدد أو بعده
        const allRows = data.data.scheduleRows || [];
        const filteredRows = allRows.filter((row: any) => {
          if (!row.time) return false;
          return row.time >= startTime;
        });

        console.log('✅ تم استيراد', filteredRows.length, 'صف من الوقت', startTime);

        // إضافة صفوف فارغة في النهاية للتعديل
        const emptyRows = Array.from({ length: 15 }, (_, i) => ({
          id: `empty_import_${Date.now()}_${i}`,
          type: 'empty' as const,
          canDelete: true
        }));

        const finalRows = [...filteredRows, ...emptyRows];
        setGridRows(finalRows);
        setAvailableMedia(data.data.availableMedia || []);
        setIsImported(true);

        // إعادة حساب الأوقات بناءً على وقت البداية المحدد
        setTimeout(() => {
          recalculateTimes(finalRows);
        }, 100);

        // جلب الخريطة الأسبوعية للمراجعة
        await fetchWeeklySchedule();

        showSuccessToast('importSuccess');
      } else {
        showErrorToast('importFailed');
      }

    } catch (error) {
      console.error('❌ خطأ في استيراد الجدول:', error);
      showErrorToast('importFailed');
    } finally {
      setLoading(false);
    }
  };



  return (
    <AuthGuard requiredPermissions={['SCHEDULE_READ']}>
      <DashboardLayout title={t('schedule.importTitle')} subtitle={t('schedule.importSubtitle')} icon="📊" fullWidth={true}>
        <style jsx>{`
          .grid-row.empty {
            background: #b8dce8 !important;
            background-color: #b8dce8 !important;
            min-height: 50px !important;
            height: 50px !important;
            border-color: #a0c4d4 !important;
          }
          .grid-row.empty .action-btn {
            font-size: 0.75rem !important;
            padding: 5px 8px !important;
            min-width: 30px !important;
            height: 30px !important;
            line-height: 1.2 !important;
            border-radius: 4px !important;
            margin: 2px !important;
            box-sizing: border-box !important;
            overflow: hidden !important;
            white-space: nowrap !important;
          }
          .grid-row.empty .actions-cell {
            padding: 8px !important;
            gap: 5px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            height: 50px !important;
            max-height: 50px !important;
            overflow: hidden !important;
            box-sizing: border-box !important;
          }
        `}</style>
        <div style={{ background: '#b8dce8', minHeight: '100vh', margin: '-2rem', padding: '1rem' }}>

      {/* تعليمات مختصرة */}
      <div style={{
        background: 'linear-gradient(45deg, #e3f2fd, #bbdefb)',
        padding: '8px 15px',
        borderRadius: '6px',
        marginBottom: '10px',
        border: '1px solid #2196f3'
      }}>
        <p style={{ margin: '0', color: '#424242', fontSize: '14px' }}>
          📋 <strong>{t('schedule.importInstructions')}</strong>
        </p>
      </div>

      {/* Controls */}
      <div className="schedule-controls">
        <div className="date-selector">
          <label htmlFor="schedule-date">{t('common.selectDate')}:</label>
          <input
            id="schedule-date"
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="glass-input"
          />
        </div>

        <div className="time-selector" style={{ marginRight: '20px' }}>
          <label htmlFor="start-time">{t('schedule.startTime')}:</label>
          <input
            id="start-time"
            type="text"
            placeholder="08:00:00"
            value={startTime}
            onChange={(e) => setStartTime(e.target.value)}
            className="glass-input"
            style={{ width: '120px', textAlign: 'center' }}
          />
        </div>

        <div style={{ marginRight: '20px' }}>
          <button
            onClick={importFromTime}
            className="glass-button primary"
            disabled={!selectedDate || !startTime || loading}
            style={{
              background: 'linear-gradient(45deg, #28a745, #20c997)',
              color: 'white',
              fontWeight: 'bold'
            }}
          >
            {loading ? `⏳ ${t('common.loading')}` : `📥 ${t('schedule.importFromTime')}`}
          </button>
        </div>
        
        <div className="header-buttons">
          <button
            onClick={exportDailySchedule}
            className="glass-button export"
            style={{
              background: 'linear-gradient(45deg, #17a2b8, #138496)',
              color: 'white'
            }}
          >
            📊 {t('common.export')} Excel
          </button>

          <button
            onClick={() => setShowWeeklySchedule(!showWeeklySchedule)}
            className="glass-button"
          >
            {showWeeklySchedule ? `📋 ${t('schedule.hideSchedule')}` : `📅 ${t('schedule.showSchedule')}`}
          </button>
        </div>
      </div>

      <div className="schedule-content">
        {/* Weekly Schedule Sidebar */}
        {showWeeklySchedule && (
          <div className="weekly-sidebar">
            <h3 className="sidebar-title">{t('schedule.weeklyScheduleTitle')}</h3>
            <div className="weekly-schedule-list">
              {Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? (
                weeklySchedule.map((item, index) => (
                  <div key={index} className="weekly-item">
                    <div className="weekly-time">{item.time}</div>
                    <div className="weekly-content">
                      <div className="weekly-name">{item.name}</div>
                      {item.episodeNumber && (
                        <div className="weekly-details">ح{item.episodeNumber}</div>
                      )}
                      {item.partNumber && (
                        <div className="weekly-details">ج{item.partNumber}</div>
                      )}
                    </div>
                    <div className="weekly-status">
                      {item.isRerun ? '🔄' : '🎯'}
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-data">{t('schedule.noWeeklyData')}</div>
              )}
            </div>
          </div>
        )}

        {/* Sidebar */}
        <div className="media-sidebar">
          <h3 className="sidebar-title">المواد المتاحة</h3>
          
          {/* Search and Filter */}
          <div className="sidebar-controls">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="filter-select"
            >
              {mediaTypes.map(type => (
                <option key={type} value={type}>
                  {getTypeLabel(type)}
                </option>
              ))}
            </select>
            
            <input
              type="text"
              placeholder="البحث في المواد..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>

          {/* Media List - Table Format */}
          <div className="media-table">
            {/* Table Header */}
            <div className="media-table-header">
              <div className="media-col-name">الاسم</div>
              <div className="media-col-type">النوع</div>
              <div className="media-col-duration">المدة</div>
              <div className="media-col-code">الكود</div>
            </div>

            {/* Table Body */}
            <div className="media-table-body">
              {filteredMedia.map(item => (
                <div
                  key={item.id}
                  className={`media-row ${item.type.toLowerCase()}`}
                  draggable
                  onDragStart={(e) => {
                    e.dataTransfer.setData('application/json', JSON.stringify(item));
                    // إضافة class للتصغير أثناء السحب
                    e.currentTarget.classList.add('dragging');
                  }}
                  onDragEnd={(e) => {
                    // إزالة class بعد انتهاء السحب
                    e.currentTarget.classList.remove('dragging');
                  }}
                  data-type={item.type}
                >
                  <div className="media-col-name" title={`${item.name} - ${getTypeLabel(item.type)}`}>
                    <div className="media-name-text">{item.name}</div>
                    <div className="media-tags">
                      {item.episodeNumber && <span className="episode-tag">ح{item.episodeNumber}</span>}
                      {item.seasonNumber && item.seasonNumber > 0 && <span className="season-tag">م{item.seasonNumber}</span>}
                      {item.partNumber && <span className="part-tag">ج{item.partNumber}</span>}
                    </div>
                  </div>
                  <div className="media-col-type">{getTypeLabel(item.type)}</div>
                  <div className="media-col-duration">{calculateTotalDuration(item)}</div>
                  <div className="media-col-code">{item.id}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Grid */}
        <div className="schedule-grid">
          {!isImported ? (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '70vh',
              background: 'linear-gradient(45deg, #f5f5f5, #e0e0e0)',
              borderRadius: '8px',
              border: '2px dashed #ccc'
            }}>
              <div style={{ textAlign: 'center', color: '#666' }}>
                <h3>📥 {t('schedule.importSchedule')}</h3>
                <p>{t('schedule.importInstructionsLong')}</p>
              </div>
            </div>
          ) : (
            <>
              <div className="grid-header">
                <div className="code-column">الكود</div>
                <div className="time-column">الوقت</div>
                <div className="content-column">المحتوى</div>
                <div className="duration-column">المدة</div>
                <div className="status-column">الحالة</div>
                <div className="actions-column">الإجراءات</div>
              </div>

              <div className="grid-body">
            {loading ? (
              <div className="loading">{t('common.loading')}</div>
            ) : (
              gridRows.map((row, index) => (
                <div
                  key={row.id}
                  className={`grid-row ${row.type} ${
                    row.type === 'segment' ? (
                      row.isRerun ? 'rerun-content' :
                      row.isTemporary ? 'temp-content' :
                      'primary-content'
                    ) :
                    row.type === 'filler' ? (
                      row.mediaType === 'PROMO' ? 'promo-content' :
                      row.mediaType === 'STING' ? 'sting-content' :
                      row.mediaType === 'FILLER' ? 'filler-content' :
                      row.mediaType === 'FILL_IN' ? 'filler-content' :
                      'break-content'
                    ) :
                    'primary-content'
                  }`}
                  draggable={row.type === 'filler' || row.type === 'empty' || (row.type === 'segment' && row.canDelete)}
                  onDragStart={(e) => handleRowDragStart(e, index)}
                  onDrop={(e) => {
                    handleRowDrop(e, index);
                    // إزالة تأثير drag-over
                    e.currentTarget.classList.remove('drag-over');
                  }}
                  onDragOver={(e) => {
                    e.preventDefault();
                    // إضافة تأثير بصري عند السحب فوق الصف الفارغ
                    if (row.type === 'empty') {
                      e.currentTarget.classList.add('drag-over');
                    }
                  }}
                  onDragLeave={(e) => {
                    // إزالة تأثير drag-over عند مغادرة الصف
                    e.currentTarget.classList.remove('drag-over');
                  }}
                  data-type={
                    // استخدام الحقل الإضافي mediaType إن وجد
                    row.mediaType ? row.mediaType :
                    row.content?.includes('PROMO') ? 'PROMO' :
                    row.content?.includes('STING') ? 'STING' :
                    row.content?.includes('FILL_IN') ? 'FILL_IN' :
                    row.content?.includes('FILLER') ? 'FILLER' : ''
                  }
                >
                  <div className="code-cell">
                    {(row.type === 'segment' || row.type === 'filler') ? (
                      <span className="content-code">
                        {row.segmentCode || `${row.type.toUpperCase()}_${row.id.slice(-6)}`}
                      </span>
                    ) : ''}
                  </div>
                  <div className="time-cell">
                    <span className="content-time">
                      {row.time || ''}
                    </span>
                  </div>
                  <div
                    className="content-cell"
                    onDrop={(e) => handleDrop(e, index)}
                    onDragOver={(e) => e.preventDefault()}
                  >
                    <div className="content-text">
                      {row.content || ''}
                    </div>
                  </div>
                  <div className="duration-cell">
                    {row.duration || ''}
                  </div>
                  <div className="status-cell">
                    {row.type === 'segment' && row.isTemporary && '🟣 مؤقت'}
                    {row.type === 'segment' && !row.isRerun && !row.isTemporary && (
                      row.originalStartTime ? (
                        Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ?
                        '⚠️ انحراف' : '✅ دقيق'
                      ) : '🎯 أساسي'
                    )}
                    {row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة'}
                    {row.type === 'filler' && '📺 فاصل'}
                    {row.type === 'empty' && '⚪ فارغ'}
                  </div>
                  <div className="actions-cell">
                    {row.type === 'empty' && !readOnlyMode && (
                      <>
                        <button
                          className="action-btn add-row"
                          title="إضافة صف"
                          onClick={() => addEmptyRow(index)}
                        >
                          ➕
                        </button>
                        <button
                          className="action-btn add-multiple-rows"
                          title="إضافة 8 صفوف"
                          onClick={() => addMultipleEmptyRows(index, 8)}
                        >
                          ➕➕
                        </button>
                        {row.canDelete && (
                          <button
                            className="action-btn delete-row"
                            title="حذف صف"
                            onClick={() => deleteRow(row.id)}
                          >
                            ➖
                          </button>
                        )}
                      </>
                    )}
                    {row.type === 'filler' && !readOnlyMode && (
                      <>
                        <button
                          className="action-btn move-up"
                          title="تحريك لأعلى"
                          onClick={() => moveRowUp(index)}
                          disabled={index === 0}
                        >
                          ⬆️
                        </button>
                        <button
                          className="action-btn move-down"
                          title="تحريك لأسفل"
                          onClick={() => moveRowDown(index)}
                          disabled={index === gridRows.length - 1}
                        >
                          ⬇️
                        </button>
                        <button
                          className="action-btn delete-row"
                          title="حذف فاصل"
                          onClick={() => {
                            // تحويل الفاصل إلى صف فارغ
                            const newRows = [...gridRows];
                            newRows[index] = {
                              id: `empty_${Date.now()}`,
                              type: 'empty',
                              canDelete: true
                            };

                            // إعادة حساب الأوقات
                            recalculateTimes(newRows);
                          }}
                        >
                          🗑️
                        </button>
                      </>
                    )}
                    {row.type === 'segment' && row.isTemporary && !readOnlyMode && (
                      <button
                        className="action-btn replace-temp"
                        title="استبدال بمادة حقيقية"
                        onClick={() => {
                          showErrorToast('invalidData');
                        }}
                        style={{ color: '#9c27b0' }}
                      >
                        🔄
                      </button>
                    )}
                    {row.type === 'segment' && row.canDelete && !row.isTemporary && !readOnlyMode && (
                      <button
                        className="action-btn delete-row"
                        title="حذف سيجمنت"
                        onClick={() => deleteSegment(row.id)}
                      >
                        ❌
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
              </div>
            </>
          )}
        </div>
      </div>
        </div>
        <ToastContainer />
      </DashboardLayout>
    </AuthGuard>
  );
}
