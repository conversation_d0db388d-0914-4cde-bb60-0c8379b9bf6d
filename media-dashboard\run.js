const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Media Dashboard...');
console.log('📁 Current directory:', process.cwd());

// تنظيف ملفات البناء
const fs = require('fs');
const nextDir = path.join(process.cwd(), '.next');
if (fs.existsSync(nextDir)) {
  console.log('🧹 Cleaning .next directory...');
  fs.rmSync(nextDir, { recursive: true, force: true });
}

// تشغيل Next.js
const nextPath = path.join(process.cwd(), 'node_modules', 'next', 'dist', 'bin', 'next');
console.log('▶️ Starting Next.js from:', nextPath);

const child = spawn('node', [nextPath, 'dev'], {
  stdio: 'inherit',
  cwd: process.cwd()
});

child.on('error', (error) => {
  console.error('❌ Error starting application:', error);
});

child.on('exit', (code) => {
  console.log(`🔚 Application exited with code: ${code}`);
});

// معالجة إشارات الإنهاء
process.on('SIGINT', () => {
  console.log('🛑 Stopping application...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('🛑 Terminating application...');
  child.kill('SIGTERM');
});
