import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translation files
import arCommon from '../../public/locales/ar/common.json';
import enCommon from '../../public/locales/en/common.json';

const resources = {
  ar: {
    common: arCommon,
  },
  en: {
    common: enCommon,
  },
};

// Get saved language from localStorage or default to Arabic
const getInitialLanguage = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('language') || 'ar';
  }
  return 'ar';
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: getInitialLanguage(),
    fallbackLng: 'ar',
    debug: false,

    interpolation: {
      escapeValue: false, // React already does escaping
    },

    react: {
      useSuspense: false,
    },

    // Configure namespaces
    defaultNS: 'common',
    ns: ['common'],
  });

// Listen for language changes and update document direction
i18n.on('languageChanged', (lng) => {
  if (typeof window !== 'undefined') {
    document.documentElement.dir = lng === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = lng;
    localStorage.setItem('language', lng);
  }
});

export default i18n;
