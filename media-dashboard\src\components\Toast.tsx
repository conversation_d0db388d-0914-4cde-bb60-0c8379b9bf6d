'use client';

import { useState, useEffect } from 'react';

interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onClose: () => void;
}

export default function Toast({ message, type, duration = 3000, onClose }: ToastProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const getToastStyles = () => {
    const baseStyles = {
      position: 'relative' as const,
      padding: '15px 20px',
      borderRadius: '10px',
      color: 'white',
      fontWeight: 'bold',
      fontSize: '1rem',
      boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
      transform: isVisible ? 'translateX(0)' : 'translateX(100%)',
      transition: 'transform 0.3s ease, opacity 0.3s ease',
      opacity: isVisible ? 1 : 0,
      minWidth: '300px',
      maxWidth: '500px',
      direction: 'rtl' as const,
      fontFamily: 'Cairo, Arial, sans-serif'
    };

    const typeStyles = {
      success: {
        background: 'linear-gradient(45deg, #28a745, #20c997)',
      },
      error: {
        background: 'linear-gradient(45deg, #dc3545, #c82333)',
      },
      warning: {
        background: 'linear-gradient(45deg, #ffc107, #e0a800)',
      },
      info: {
        background: 'linear-gradient(45deg, #007bff, #0056b3)',
      }
    };

    return { ...baseStyles, ...typeStyles[type] };
  };

  const getIcon = () => {
    const icons = {
      success: '✅',
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️'
    };
    return icons[type];
  };

  return (
    <div style={getToastStyles()}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
        <span style={{ fontSize: '1.2rem' }}>{getIcon()}</span>
        <span>{message}</span>
        <button
          onClick={() => {
            setIsVisible(false);
            setTimeout(onClose, 300);
          }}
          style={{
            background: 'rgba(255,255,255,0.2)',
            border: 'none',
            color: 'white',
            borderRadius: '50%',
            width: '25px',
            height: '25px',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginLeft: 'auto'
          }}
        >
          ×
        </button>
      </div>
    </div>
  );
}

// Hook لاستخدام Toast
export function useToast() {
  const [toasts, setToasts] = useState<Array<{
    id: string;
    message: string;
    type: 'success' | 'error' | 'warning' | 'info';
  }>>([]);

  const showToast = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    // التحقق من عدم وجود رسالة مشابهة
    const existingToast = toasts.find(toast => toast.message === message && toast.type === type);
    if (existingToast) {
      return; // لا تضيف رسالة مكررة
    }

    // إنشاء ID فريد باستخدام timestamp + random number
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    setToasts(prev => [...prev, { id, message, type }]);

    // حد أقصى 5 رسائل في نفس الوقت
    setToasts(prev => prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const ToastContainer = () => (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      zIndex: 1000,
      display: 'flex',
      flexDirection: 'column',
      gap: '10px'
    }}>
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          message={toast.message}
          type={toast.type}
          onClose={() => removeToast(toast.id)}
        />
      ))}
    </div>
  );

  return { showToast, ToastContainer };
}
