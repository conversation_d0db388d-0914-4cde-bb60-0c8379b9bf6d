"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/saxes";
exports.ids = ["vendor-chunks/saxes"];
exports.modules = {

/***/ "(ssr)/./node_modules/saxes/saxes.js":
/*!*************************************!*\
  !*** ./node_modules/saxes/saxes.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst ed5 = __webpack_require__(/*! xmlchars/xml/1.0/ed5 */ \"(ssr)/./node_modules/xmlchars/xml/1.0/ed5.js\");\nconst ed2 = __webpack_require__(/*! xmlchars/xml/1.1/ed2 */ \"(ssr)/./node_modules/xmlchars/xml/1.1/ed2.js\");\nconst NSed3 = __webpack_require__(/*! xmlchars/xmlns/1.0/ed3 */ \"(ssr)/./node_modules/xmlchars/xmlns/1.0/ed3.js\");\nvar isS = ed5.isS;\nvar isChar10 = ed5.isChar;\nvar isNameStartChar = ed5.isNameStartChar;\nvar isNameChar = ed5.isNameChar;\nvar S_LIST = ed5.S_LIST;\nvar NAME_RE = ed5.NAME_RE;\nvar isChar11 = ed2.isChar;\nvar isNCNameStartChar = NSed3.isNCNameStartChar;\nvar isNCNameChar = NSed3.isNCNameChar;\nvar NC_NAME_RE = NSed3.NC_NAME_RE;\nconst XML_NAMESPACE = \"http://www.w3.org/XML/1998/namespace\";\nconst XMLNS_NAMESPACE = \"http://www.w3.org/2000/xmlns/\";\nconst rootNS = {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    __proto__: null,\n    xml: XML_NAMESPACE,\n    xmlns: XMLNS_NAMESPACE,\n};\nconst XML_ENTITIES = {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    __proto__: null,\n    amp: \"&\",\n    gt: \">\",\n    lt: \"<\",\n    quot: \"\\\"\",\n    apos: \"'\",\n};\n// EOC: end-of-chunk\nconst EOC = -1;\nconst NL_LIKE = -2;\nconst S_BEGIN = 0; // Initial state.\nconst S_BEGIN_WHITESPACE = 1; // leading whitespace\nconst S_DOCTYPE = 2; // <!DOCTYPE\nconst S_DOCTYPE_QUOTE = 3; // <!DOCTYPE \"//blah\nconst S_DTD = 4; // <!DOCTYPE \"//blah\" [ ...\nconst S_DTD_QUOTED = 5; // <!DOCTYPE \"//blah\" [ \"foo\nconst S_DTD_OPEN_WAKA = 6;\nconst S_DTD_OPEN_WAKA_BANG = 7;\nconst S_DTD_COMMENT = 8; // <!--\nconst S_DTD_COMMENT_ENDING = 9; // <!-- blah -\nconst S_DTD_COMMENT_ENDED = 10; // <!-- blah --\nconst S_DTD_PI = 11; // <?\nconst S_DTD_PI_ENDING = 12; // <?hi \"there\" ?\nconst S_TEXT = 13; // general stuff\nconst S_ENTITY = 14; // &amp and such\nconst S_OPEN_WAKA = 15; // <\nconst S_OPEN_WAKA_BANG = 16; // <!...\nconst S_COMMENT = 17; // <!--\nconst S_COMMENT_ENDING = 18; // <!-- blah -\nconst S_COMMENT_ENDED = 19; // <!-- blah --\nconst S_CDATA = 20; // <![CDATA[ something\nconst S_CDATA_ENDING = 21; // ]\nconst S_CDATA_ENDING_2 = 22; // ]]\nconst S_PI_FIRST_CHAR = 23; // <?hi, first char\nconst S_PI_REST = 24; // <?hi, rest of the name\nconst S_PI_BODY = 25; // <?hi there\nconst S_PI_ENDING = 26; // <?hi \"there\" ?\nconst S_XML_DECL_NAME_START = 27; // <?xml\nconst S_XML_DECL_NAME = 28; // <?xml foo\nconst S_XML_DECL_EQ = 29; // <?xml foo=\nconst S_XML_DECL_VALUE_START = 30; // <?xml foo=\nconst S_XML_DECL_VALUE = 31; // <?xml foo=\"bar\"\nconst S_XML_DECL_SEPARATOR = 32; // <?xml foo=\"bar\"\nconst S_XML_DECL_ENDING = 33; // <?xml ... ?\nconst S_OPEN_TAG = 34; // <strong\nconst S_OPEN_TAG_SLASH = 35; // <strong /\nconst S_ATTRIB = 36; // <a\nconst S_ATTRIB_NAME = 37; // <a foo\nconst S_ATTRIB_NAME_SAW_WHITE = 38; // <a foo _\nconst S_ATTRIB_VALUE = 39; // <a foo=\nconst S_ATTRIB_VALUE_QUOTED = 40; // <a foo=\"bar\nconst S_ATTRIB_VALUE_CLOSED = 41; // <a foo=\"bar\"\nconst S_ATTRIB_VALUE_UNQUOTED = 42; // <a foo=bar\nconst S_CLOSE_TAG = 43; // </a\nconst S_CLOSE_TAG_SAW_WHITE = 44; // </a   >\nconst TAB = 9;\nconst NL = 0xA;\nconst CR = 0xD;\nconst SPACE = 0x20;\nconst BANG = 0x21;\nconst DQUOTE = 0x22;\nconst AMP = 0x26;\nconst SQUOTE = 0x27;\nconst MINUS = 0x2D;\nconst FORWARD_SLASH = 0x2F;\nconst SEMICOLON = 0x3B;\nconst LESS = 0x3C;\nconst EQUAL = 0x3D;\nconst GREATER = 0x3E;\nconst QUESTION = 0x3F;\nconst OPEN_BRACKET = 0x5B;\nconst CLOSE_BRACKET = 0x5D;\nconst NEL = 0x85;\nconst LS = 0x2028; // Line Separator\nconst isQuote = (c) => c === DQUOTE || c === SQUOTE;\nconst QUOTES = [DQUOTE, SQUOTE];\nconst DOCTYPE_TERMINATOR = [...QUOTES, OPEN_BRACKET, GREATER];\nconst DTD_TERMINATOR = [...QUOTES, LESS, CLOSE_BRACKET];\nconst XML_DECL_NAME_TERMINATOR = [EQUAL, QUESTION, ...S_LIST];\nconst ATTRIB_VALUE_UNQUOTED_TERMINATOR = [...S_LIST, GREATER, AMP, LESS];\nfunction nsPairCheck(parser, prefix, uri) {\n    switch (prefix) {\n        case \"xml\":\n            if (uri !== XML_NAMESPACE) {\n                parser.fail(`xml prefix must be bound to ${XML_NAMESPACE}.`);\n            }\n            break;\n        case \"xmlns\":\n            if (uri !== XMLNS_NAMESPACE) {\n                parser.fail(`xmlns prefix must be bound to ${XMLNS_NAMESPACE}.`);\n            }\n            break;\n        default:\n    }\n    switch (uri) {\n        case XMLNS_NAMESPACE:\n            parser.fail(prefix === \"\" ?\n                `the default namespace may not be set to ${uri}.` :\n                `may not assign a prefix (even \"xmlns\") to the URI \\\n${XMLNS_NAMESPACE}.`);\n            break;\n        case XML_NAMESPACE:\n            switch (prefix) {\n                case \"xml\":\n                    // Assinging the XML namespace to \"xml\" is fine.\n                    break;\n                case \"\":\n                    parser.fail(`the default namespace may not be set to ${uri}.`);\n                    break;\n                default:\n                    parser.fail(\"may not assign the xml namespace to another prefix.\");\n            }\n            break;\n        default:\n    }\n}\nfunction nsMappingCheck(parser, mapping) {\n    for (const local of Object.keys(mapping)) {\n        nsPairCheck(parser, local, mapping[local]);\n    }\n}\nconst isNCName = (name) => NC_NAME_RE.test(name);\nconst isName = (name) => NAME_RE.test(name);\nconst FORBIDDEN_START = 0;\nconst FORBIDDEN_BRACKET = 1;\nconst FORBIDDEN_BRACKET_BRACKET = 2;\n/**\n * The list of supported events.\n */\nexports.EVENTS = [\n    \"xmldecl\",\n    \"text\",\n    \"processinginstruction\",\n    \"doctype\",\n    \"comment\",\n    \"opentagstart\",\n    \"attribute\",\n    \"opentag\",\n    \"closetag\",\n    \"cdata\",\n    \"error\",\n    \"end\",\n    \"ready\",\n];\nconst EVENT_NAME_TO_HANDLER_NAME = {\n    xmldecl: \"xmldeclHandler\",\n    text: \"textHandler\",\n    processinginstruction: \"piHandler\",\n    doctype: \"doctypeHandler\",\n    comment: \"commentHandler\",\n    opentagstart: \"openTagStartHandler\",\n    attribute: \"attributeHandler\",\n    opentag: \"openTagHandler\",\n    closetag: \"closeTagHandler\",\n    cdata: \"cdataHandler\",\n    error: \"errorHandler\",\n    end: \"endHandler\",\n    ready: \"readyHandler\",\n};\nclass SaxesParser {\n    /**\n     * @param opt The parser options.\n     */\n    constructor(opt) {\n        this.opt = opt !== null && opt !== void 0 ? opt : {};\n        this.fragmentOpt = !!this.opt.fragment;\n        const xmlnsOpt = this.xmlnsOpt = !!this.opt.xmlns;\n        this.trackPosition = this.opt.position !== false;\n        this.fileName = this.opt.fileName;\n        if (xmlnsOpt) {\n            // This is the function we use to perform name checks on PIs and entities.\n            // When namespaces are used, colons are not allowed in PI target names or\n            // entity names. So the check depends on whether namespaces are used. See:\n            //\n            // https://www.w3.org/XML/xml-names-19990114-errata.html\n            // NE08\n            //\n            this.nameStartCheck = isNCNameStartChar;\n            this.nameCheck = isNCNameChar;\n            this.isName = isNCName;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.processAttribs = this.processAttribsNS;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.pushAttrib = this.pushAttribNS;\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            this.ns = Object.assign({ __proto__: null }, rootNS);\n            const additional = this.opt.additionalNamespaces;\n            if (additional != null) {\n                nsMappingCheck(this, additional);\n                Object.assign(this.ns, additional);\n            }\n        }\n        else {\n            this.nameStartCheck = isNameStartChar;\n            this.nameCheck = isNameChar;\n            this.isName = isName;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.processAttribs = this.processAttribsPlain;\n            // eslint-disable-next-line @typescript-eslint/unbound-method\n            this.pushAttrib = this.pushAttribPlain;\n        }\n        //\n        // The order of the members in this table needs to correspond to the state\n        // numbers given to the states that correspond to the methods being recorded\n        // here.\n        //\n        this.stateTable = [\n            /* eslint-disable @typescript-eslint/unbound-method */\n            this.sBegin,\n            this.sBeginWhitespace,\n            this.sDoctype,\n            this.sDoctypeQuote,\n            this.sDTD,\n            this.sDTDQuoted,\n            this.sDTDOpenWaka,\n            this.sDTDOpenWakaBang,\n            this.sDTDComment,\n            this.sDTDCommentEnding,\n            this.sDTDCommentEnded,\n            this.sDTDPI,\n            this.sDTDPIEnding,\n            this.sText,\n            this.sEntity,\n            this.sOpenWaka,\n            this.sOpenWakaBang,\n            this.sComment,\n            this.sCommentEnding,\n            this.sCommentEnded,\n            this.sCData,\n            this.sCDataEnding,\n            this.sCDataEnding2,\n            this.sPIFirstChar,\n            this.sPIRest,\n            this.sPIBody,\n            this.sPIEnding,\n            this.sXMLDeclNameStart,\n            this.sXMLDeclName,\n            this.sXMLDeclEq,\n            this.sXMLDeclValueStart,\n            this.sXMLDeclValue,\n            this.sXMLDeclSeparator,\n            this.sXMLDeclEnding,\n            this.sOpenTag,\n            this.sOpenTagSlash,\n            this.sAttrib,\n            this.sAttribName,\n            this.sAttribNameSawWhite,\n            this.sAttribValue,\n            this.sAttribValueQuoted,\n            this.sAttribValueClosed,\n            this.sAttribValueUnquoted,\n            this.sCloseTag,\n            this.sCloseTagSawWhite,\n        ];\n        this._init();\n    }\n    /**\n     * Indicates whether or not the parser is closed. If ``true``, wait for\n     * the ``ready`` event to write again.\n     */\n    get closed() {\n        return this._closed;\n    }\n    _init() {\n        var _a;\n        this.openWakaBang = \"\";\n        this.text = \"\";\n        this.name = \"\";\n        this.piTarget = \"\";\n        this.entity = \"\";\n        this.q = null;\n        this.tags = [];\n        this.tag = null;\n        this.topNS = null;\n        this.chunk = \"\";\n        this.chunkPosition = 0;\n        this.i = 0;\n        this.prevI = 0;\n        this.carriedFromPrevious = undefined;\n        this.forbiddenState = FORBIDDEN_START;\n        this.attribList = [];\n        // The logic is organized so as to minimize the need to check\n        // this.opt.fragment while parsing.\n        const { fragmentOpt } = this;\n        this.state = fragmentOpt ? S_TEXT : S_BEGIN;\n        // We want these to be all true if we are dealing with a fragment.\n        this.reportedTextBeforeRoot = this.reportedTextAfterRoot = this.closedRoot =\n            this.sawRoot = fragmentOpt;\n        // An XML declaration is intially possible only when parsing whole\n        // documents.\n        this.xmlDeclPossible = !fragmentOpt;\n        this.xmlDeclExpects = [\"version\"];\n        this.entityReturnState = undefined;\n        let { defaultXMLVersion } = this.opt;\n        if (defaultXMLVersion === undefined) {\n            if (this.opt.forceXMLVersion === true) {\n                throw new Error(\"forceXMLVersion set but defaultXMLVersion is not set\");\n            }\n            defaultXMLVersion = \"1.0\";\n        }\n        this.setXMLVersion(defaultXMLVersion);\n        this.positionAtNewLine = 0;\n        this.doctype = false;\n        this._closed = false;\n        this.xmlDecl = {\n            version: undefined,\n            encoding: undefined,\n            standalone: undefined,\n        };\n        this.line = 1;\n        this.column = 0;\n        this.ENTITIES = Object.create(XML_ENTITIES);\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.readyHandler) === null || _a === void 0 ? void 0 : _a.call(this);\n    }\n    /**\n     * The stream position the parser is currently looking at. This field is\n     * zero-based.\n     *\n     * This field is not based on counting Unicode characters but is to be\n     * interpreted as a plain index into a JavaScript string.\n     */\n    get position() {\n        return this.chunkPosition + this.i;\n    }\n    /**\n     * The column number of the next character to be read by the parser.  *\n     * This field is zero-based. (The first column in a line is 0.)\n     *\n     * This field reports the index at which the next character would be in the\n     * line if the line were represented as a JavaScript string.  Note that this\n     * *can* be different to a count based on the number of *Unicode characters*\n     * due to how JavaScript handles astral plane characters.\n     *\n     * See [[column]] for a number that corresponds to a count of Unicode\n     * characters.\n     */\n    get columnIndex() {\n        return this.position - this.positionAtNewLine;\n    }\n    /**\n     * Set an event listener on an event. The parser supports one handler per\n     * event type. If you try to set an event handler over an existing handler,\n     * the old handler is silently overwritten.\n     *\n     * @param name The event to listen to.\n     *\n     * @param handler The handler to set.\n     */\n    on(name, handler) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this[EVENT_NAME_TO_HANDLER_NAME[name]] = handler;\n    }\n    /**\n     * Unset an event handler.\n     *\n     * @parma name The event to stop listening to.\n     */\n    off(name) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        this[EVENT_NAME_TO_HANDLER_NAME[name]] = undefined;\n    }\n    /**\n     * Make an error object. The error object will have a message that contains\n     * the ``fileName`` option passed at the creation of the parser. If position\n     * tracking was turned on, it will also have line and column number\n     * information.\n     *\n     * @param message The message describing the error to report.\n     *\n     * @returns An error object with a properly formatted message.\n     */\n    makeError(message) {\n        var _a;\n        let msg = (_a = this.fileName) !== null && _a !== void 0 ? _a : \"\";\n        if (this.trackPosition) {\n            if (msg.length > 0) {\n                msg += \":\";\n            }\n            msg += `${this.line}:${this.column}`;\n        }\n        if (msg.length > 0) {\n            msg += \": \";\n        }\n        return new Error(msg + message);\n    }\n    /**\n     * Report a parsing error. This method is made public so that client code may\n     * check for issues that are outside the scope of this project and can report\n     * errors.\n     *\n     * @param message The error to report.\n     *\n     * @returns this\n     */\n    fail(message) {\n        const err = this.makeError(message);\n        const handler = this.errorHandler;\n        if (handler === undefined) {\n            throw err;\n        }\n        else {\n            handler(err);\n        }\n        return this;\n    }\n    /**\n     * Write a XML data to the parser.\n     *\n     * @param chunk The XML data to write.\n     *\n     * @returns this\n     */\n    write(chunk) {\n        if (this.closed) {\n            return this.fail(\"cannot write after close; assign an onready handler.\");\n        }\n        let end = false;\n        if (chunk === null) {\n            // We cannot return immediately because carriedFromPrevious may need\n            // processing.\n            end = true;\n            chunk = \"\";\n        }\n        else if (typeof chunk === \"object\") {\n            chunk = chunk.toString();\n        }\n        // We checked if performing a pre-decomposition of the string into an array\n        // of single complete characters (``Array.from(chunk)``) would be faster\n        // than the current repeated calls to ``charCodeAt``. As of August 2018, it\n        // isn't. (There may be Node-specific code that would perform faster than\n        // ``Array.from`` but don't want to be dependent on Node.)\n        if (this.carriedFromPrevious !== undefined) {\n            // The previous chunk had char we must carry over.\n            chunk = `${this.carriedFromPrevious}${chunk}`;\n            this.carriedFromPrevious = undefined;\n        }\n        let limit = chunk.length;\n        const lastCode = chunk.charCodeAt(limit - 1);\n        if (!end &&\n            // A trailing CR or surrogate must be carried over to the next\n            // chunk.\n            (lastCode === CR || (lastCode >= 0xD800 && lastCode <= 0xDBFF))) {\n            // The chunk ends with a character that must be carried over. We cannot\n            // know how to handle it until we get the next chunk or the end of the\n            // stream. So save it for later.\n            this.carriedFromPrevious = chunk[limit - 1];\n            limit--;\n            chunk = chunk.slice(0, limit);\n        }\n        const { stateTable } = this;\n        this.chunk = chunk;\n        this.i = 0;\n        while (this.i < limit) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            stateTable[this.state].call(this);\n        }\n        this.chunkPosition += limit;\n        return end ? this.end() : this;\n    }\n    /**\n     * Close the current stream. Perform final well-formedness checks and reset\n     * the parser tstate.\n     *\n     * @returns this\n     */\n    close() {\n        return this.write(null);\n    }\n    /**\n     * Get a single code point out of the current chunk. This updates the current\n     * position if we do position tracking.\n     *\n     * This is the algorithm to use for XML 1.0.\n     *\n     * @returns The character read.\n     */\n    getCode10() {\n        const { chunk, i } = this;\n        this.prevI = i;\n        // Yes, we do this instead of doing this.i++. Doing it this way, we do not\n        // read this.i again, which is a bit faster.\n        this.i = i + 1;\n        if (i >= chunk.length) {\n            return EOC;\n        }\n        // Using charCodeAt and handling the surrogates ourselves is faster\n        // than using codePointAt.\n        const code = chunk.charCodeAt(i);\n        this.column++;\n        if (code < 0xD800) {\n            if (code >= SPACE || code === TAB) {\n                return code;\n            }\n            switch (code) {\n                case NL:\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL;\n                case CR:\n                    // We may get NaN if we read past the end of the chunk, which is fine.\n                    if (chunk.charCodeAt(i + 1) === NL) {\n                        // A \\r\\n sequence is converted to \\n so we have to skip over the\n                        // next character. We already know it has a size of 1 so ++ is fine\n                        // here.\n                        this.i = i + 2;\n                    }\n                    // Otherwise, a \\r is just converted to \\n, so we don't have to skip\n                    // ahead.\n                    // In either case, \\r becomes \\n.\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL_LIKE;\n                default:\n                    // If we get here, then code < SPACE and it is not NL CR or TAB.\n                    this.fail(\"disallowed character.\");\n                    return code;\n            }\n        }\n        if (code > 0xDBFF) {\n            // This is a specialized version of isChar10 that takes into account\n            // that in this context code > 0xDBFF and code <= 0xFFFF. So it does not\n            // test cases that don't need testing.\n            if (!(code >= 0xE000 && code <= 0xFFFD)) {\n                this.fail(\"disallowed character.\");\n            }\n            return code;\n        }\n        const final = 0x10000 + ((code - 0xD800) * 0x400) +\n            (chunk.charCodeAt(i + 1) - 0xDC00);\n        this.i = i + 2;\n        // This is a specialized version of isChar10 that takes into account that in\n        // this context necessarily final >= 0x10000.\n        if (final > 0x10FFFF) {\n            this.fail(\"disallowed character.\");\n        }\n        return final;\n    }\n    /**\n     * Get a single code point out of the current chunk. This updates the current\n     * position if we do position tracking.\n     *\n     * This is the algorithm to use for XML 1.1.\n     *\n     * @returns {number} The character read.\n     */\n    getCode11() {\n        const { chunk, i } = this;\n        this.prevI = i;\n        // Yes, we do this instead of doing this.i++. Doing it this way, we do not\n        // read this.i again, which is a bit faster.\n        this.i = i + 1;\n        if (i >= chunk.length) {\n            return EOC;\n        }\n        // Using charCodeAt and handling the surrogates ourselves is faster\n        // than using codePointAt.\n        const code = chunk.charCodeAt(i);\n        this.column++;\n        if (code < 0xD800) {\n            if ((code > 0x1F && code < 0x7F) || (code > 0x9F && code !== LS) ||\n                code === TAB) {\n                return code;\n            }\n            switch (code) {\n                case NL: // 0xA\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL;\n                case CR: { // 0xD\n                    // We may get NaN if we read past the end of the chunk, which is\n                    // fine.\n                    const next = chunk.charCodeAt(i + 1);\n                    if (next === NL || next === NEL) {\n                        // A CR NL or CR NEL sequence is converted to NL so we have to skip\n                        // over the next character. We already know it has a size of 1.\n                        this.i = i + 2;\n                    }\n                    // Otherwise, a CR is just converted to NL, no skip.\n                }\n                /* yes, fall through */\n                case NEL: // 0x85\n                case LS: // Ox2028\n                    this.line++;\n                    this.column = 0;\n                    this.positionAtNewLine = this.position;\n                    return NL_LIKE;\n                default:\n                    this.fail(\"disallowed character.\");\n                    return code;\n            }\n        }\n        if (code > 0xDBFF) {\n            // This is a specialized version of isCharAndNotRestricted that takes into\n            // account that in this context code > 0xDBFF and code <= 0xFFFF. So it\n            // does not test cases that don't need testing.\n            if (!(code >= 0xE000 && code <= 0xFFFD)) {\n                this.fail(\"disallowed character.\");\n            }\n            return code;\n        }\n        const final = 0x10000 + ((code - 0xD800) * 0x400) +\n            (chunk.charCodeAt(i + 1) - 0xDC00);\n        this.i = i + 2;\n        // This is a specialized version of isCharAndNotRestricted that takes into\n        // account that in this context necessarily final >= 0x10000.\n        if (final > 0x10FFFF) {\n            this.fail(\"disallowed character.\");\n        }\n        return final;\n    }\n    /**\n     * Like ``getCode`` but with the return value normalized so that ``NL`` is\n     * returned for ``NL_LIKE``.\n     */\n    getCodeNorm() {\n        const c = this.getCode();\n        return c === NL_LIKE ? NL : c;\n    }\n    unget() {\n        this.i = this.prevI;\n        this.column--;\n    }\n    /**\n     * Capture characters into a buffer until encountering one of a set of\n     * characters.\n     *\n     * @param chars An array of codepoints. Encountering a character in the array\n     * ends the capture. (``chars`` may safely contain ``NL``.)\n     *\n     * @return The character code that made the capture end, or ``EOC`` if we hit\n     * the end of the chunk. The return value cannot be NL_LIKE: NL is returned\n     * instead.\n     */\n    captureTo(chars) {\n        let { i: start } = this;\n        const { chunk } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCode();\n            const isNLLike = c === NL_LIKE;\n            const final = isNLLike ? NL : c;\n            if (final === EOC || chars.includes(final)) {\n                this.text += chunk.slice(start, this.prevI);\n                return final;\n            }\n            if (isNLLike) {\n                this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                start = this.i;\n            }\n        }\n    }\n    /**\n     * Capture characters into a buffer until encountering a character.\n     *\n     * @param char The codepoint that ends the capture. **NOTE ``char`` MAY NOT\n     * CONTAIN ``NL``.** Passing ``NL`` will result in buggy behavior.\n     *\n     * @return ``true`` if we ran into the character. Otherwise, we ran into the\n     * end of the current chunk.\n     */\n    captureToChar(char) {\n        let { i: start } = this;\n        const { chunk } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            let c = this.getCode();\n            switch (c) {\n                case NL_LIKE:\n                    this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                    start = this.i;\n                    c = NL;\n                    break;\n                case EOC:\n                    this.text += chunk.slice(start);\n                    return false;\n                default:\n            }\n            if (c === char) {\n                this.text += chunk.slice(start, this.prevI);\n                return true;\n            }\n        }\n    }\n    /**\n     * Capture characters that satisfy ``isNameChar`` into the ``name`` field of\n     * this parser.\n     *\n     * @return The character code that made the test fail, or ``EOC`` if we hit\n     * the end of the chunk. The return value cannot be NL_LIKE: NL is returned\n     * instead.\n     */\n    captureNameChars() {\n        const { chunk, i: start } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCode();\n            if (c === EOC) {\n                this.name += chunk.slice(start);\n                return EOC;\n            }\n            // NL is not a name char so we don't have to test specifically for it.\n            if (!isNameChar(c)) {\n                this.name += chunk.slice(start, this.prevI);\n                return c === NL_LIKE ? NL : c;\n            }\n        }\n    }\n    /**\n     * Skip white spaces.\n     *\n     * @return The character that ended the skip, or ``EOC`` if we hit\n     * the end of the chunk. The return value cannot be NL_LIKE: NL is returned\n     * instead.\n     */\n    skipSpaces() {\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCodeNorm();\n            if (c === EOC || !isS(c)) {\n                return c;\n            }\n        }\n    }\n    setXMLVersion(version) {\n        this.currentXMLVersion = version;\n        /*  eslint-disable @typescript-eslint/unbound-method */\n        if (version === \"1.0\") {\n            this.isChar = isChar10;\n            this.getCode = this.getCode10;\n        }\n        else {\n            this.isChar = isChar11;\n            this.getCode = this.getCode11;\n        }\n        /* eslint-enable @typescript-eslint/unbound-method */\n    }\n    // STATE ENGINE METHODS\n    // This needs to be a state separate from S_BEGIN_WHITESPACE because we want\n    // to be sure never to come back to this state later.\n    sBegin() {\n        // We are essentially peeking at the first character of the chunk. Since\n        // S_BEGIN can be in effect only when we start working on the first chunk,\n        // the index at which we must look is necessarily 0. Note also that the\n        // following test does not depend on decoding surrogates.\n        // If the initial character is 0xFEFF, ignore it.\n        if (this.chunk.charCodeAt(0) === 0xFEFF) {\n            this.i++;\n            this.column++;\n        }\n        this.state = S_BEGIN_WHITESPACE;\n    }\n    sBeginWhitespace() {\n        // We need to know whether we've encountered spaces or not because as soon\n        // as we run into a space, an XML declaration is no longer possible. Rather\n        // than slow down skipSpaces even in places where we don't care whether it\n        // skipped anything or not, we check whether prevI is equal to the value of\n        // i from before we skip spaces.\n        const iBefore = this.i;\n        const c = this.skipSpaces();\n        if (this.prevI !== iBefore) {\n            this.xmlDeclPossible = false;\n        }\n        switch (c) {\n            case LESS:\n                this.state = S_OPEN_WAKA;\n                // We could naively call closeText but in this state, it is not normal\n                // to have text be filled with any data.\n                if (this.text.length !== 0) {\n                    throw new Error(\"no-empty text at start\");\n                }\n                break;\n            case EOC:\n                break;\n            default:\n                this.unget();\n                this.state = S_TEXT;\n                this.xmlDeclPossible = false;\n        }\n    }\n    sDoctype() {\n        var _a;\n        const c = this.captureTo(DOCTYPE_TERMINATOR);\n        switch (c) {\n            case GREATER: {\n                // eslint-disable-next-line no-unused-expressions\n                (_a = this.doctypeHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.text);\n                this.text = \"\";\n                this.state = S_TEXT;\n                this.doctype = true; // just remember that we saw it.\n                break;\n            }\n            case EOC:\n                break;\n            default:\n                this.text += String.fromCodePoint(c);\n                if (c === OPEN_BRACKET) {\n                    this.state = S_DTD;\n                }\n                else if (isQuote(c)) {\n                    this.state = S_DOCTYPE_QUOTE;\n                    this.q = c;\n                }\n        }\n    }\n    sDoctypeQuote() {\n        const q = this.q;\n        if (this.captureToChar(q)) {\n            this.text += String.fromCodePoint(q);\n            this.q = null;\n            this.state = S_DOCTYPE;\n        }\n    }\n    sDTD() {\n        const c = this.captureTo(DTD_TERMINATOR);\n        if (c === EOC) {\n            return;\n        }\n        this.text += String.fromCodePoint(c);\n        if (c === CLOSE_BRACKET) {\n            this.state = S_DOCTYPE;\n        }\n        else if (c === LESS) {\n            this.state = S_DTD_OPEN_WAKA;\n        }\n        else if (isQuote(c)) {\n            this.state = S_DTD_QUOTED;\n            this.q = c;\n        }\n    }\n    sDTDQuoted() {\n        const q = this.q;\n        if (this.captureToChar(q)) {\n            this.text += String.fromCodePoint(q);\n            this.state = S_DTD;\n            this.q = null;\n        }\n    }\n    sDTDOpenWaka() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        switch (c) {\n            case BANG:\n                this.state = S_DTD_OPEN_WAKA_BANG;\n                this.openWakaBang = \"\";\n                break;\n            case QUESTION:\n                this.state = S_DTD_PI;\n                break;\n            default:\n                this.state = S_DTD;\n        }\n    }\n    sDTDOpenWakaBang() {\n        const char = String.fromCodePoint(this.getCodeNorm());\n        const owb = this.openWakaBang += char;\n        this.text += char;\n        if (owb !== \"-\") {\n            this.state = owb === \"--\" ? S_DTD_COMMENT : S_DTD;\n            this.openWakaBang = \"\";\n        }\n    }\n    sDTDComment() {\n        if (this.captureToChar(MINUS)) {\n            this.text += \"-\";\n            this.state = S_DTD_COMMENT_ENDING;\n        }\n    }\n    sDTDCommentEnding() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        this.state = c === MINUS ? S_DTD_COMMENT_ENDED : S_DTD_COMMENT;\n    }\n    sDTDCommentEnded() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        if (c === GREATER) {\n            this.state = S_DTD;\n        }\n        else {\n            this.fail(\"malformed comment.\");\n            // <!-- blah -- bloo --> will be recorded as\n            // a comment of \" blah -- bloo \"\n            this.state = S_DTD_COMMENT;\n        }\n    }\n    sDTDPI() {\n        if (this.captureToChar(QUESTION)) {\n            this.text += \"?\";\n            this.state = S_DTD_PI_ENDING;\n        }\n    }\n    sDTDPIEnding() {\n        const c = this.getCodeNorm();\n        this.text += String.fromCodePoint(c);\n        if (c === GREATER) {\n            this.state = S_DTD;\n        }\n    }\n    sText() {\n        //\n        // We did try a version of saxes where the S_TEXT state was split in two\n        // states: one for text inside the root element, and one for text\n        // outside. This was avoiding having to test this.tags.length to decide\n        // what implementation to actually use.\n        //\n        // Peformance testing on gigabyte-size files did not show any advantage to\n        // using the two states solution instead of the current one. Conversely, it\n        // made the code a bit more complicated elsewhere. For instance, a comment\n        // can appear before the root element so when a comment ended it was\n        // necessary to determine whether to return to the S_TEXT state or to the\n        // new text-outside-root state.\n        //\n        if (this.tags.length !== 0) {\n            this.handleTextInRoot();\n        }\n        else {\n            this.handleTextOutsideRoot();\n        }\n    }\n    sEntity() {\n        // This is essentially a specialized version of captureToChar(SEMICOLON...)\n        let { i: start } = this;\n        const { chunk } = this;\n        // eslint-disable-next-line no-labels, no-restricted-syntax\n        loop: \n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            switch (this.getCode()) {\n                case NL_LIKE:\n                    this.entity += `${chunk.slice(start, this.prevI)}\\n`;\n                    start = this.i;\n                    break;\n                case SEMICOLON: {\n                    const { entityReturnState } = this;\n                    const entity = this.entity + chunk.slice(start, this.prevI);\n                    this.state = entityReturnState;\n                    let parsed;\n                    if (entity === \"\") {\n                        this.fail(\"empty entity name.\");\n                        parsed = \"&;\";\n                    }\n                    else {\n                        parsed = this.parseEntity(entity);\n                        this.entity = \"\";\n                    }\n                    if (entityReturnState !== S_TEXT || this.textHandler !== undefined) {\n                        this.text += parsed;\n                    }\n                    // eslint-disable-next-line no-labels\n                    break loop;\n                }\n                case EOC:\n                    this.entity += chunk.slice(start);\n                    // eslint-disable-next-line no-labels\n                    break loop;\n                default:\n            }\n        }\n    }\n    sOpenWaka() {\n        // Reminder: a state handler is called with at least one character\n        // available in the current chunk. So the first call to get code inside of\n        // a state handler cannot return ``EOC``. That's why we don't test\n        // for it.\n        const c = this.getCode();\n        // either a /, ?, !, or text is coming next.\n        if (isNameStartChar(c)) {\n            this.state = S_OPEN_TAG;\n            this.unget();\n            this.xmlDeclPossible = false;\n        }\n        else {\n            switch (c) {\n                case FORWARD_SLASH:\n                    this.state = S_CLOSE_TAG;\n                    this.xmlDeclPossible = false;\n                    break;\n                case BANG:\n                    this.state = S_OPEN_WAKA_BANG;\n                    this.openWakaBang = \"\";\n                    this.xmlDeclPossible = false;\n                    break;\n                case QUESTION:\n                    this.state = S_PI_FIRST_CHAR;\n                    break;\n                default:\n                    this.fail(\"disallowed character in tag name\");\n                    this.state = S_TEXT;\n                    this.xmlDeclPossible = false;\n            }\n        }\n    }\n    sOpenWakaBang() {\n        this.openWakaBang += String.fromCodePoint(this.getCodeNorm());\n        switch (this.openWakaBang) {\n            case \"[CDATA[\":\n                if (!this.sawRoot && !this.reportedTextBeforeRoot) {\n                    this.fail(\"text data outside of root node.\");\n                    this.reportedTextBeforeRoot = true;\n                }\n                if (this.closedRoot && !this.reportedTextAfterRoot) {\n                    this.fail(\"text data outside of root node.\");\n                    this.reportedTextAfterRoot = true;\n                }\n                this.state = S_CDATA;\n                this.openWakaBang = \"\";\n                break;\n            case \"--\":\n                this.state = S_COMMENT;\n                this.openWakaBang = \"\";\n                break;\n            case \"DOCTYPE\":\n                this.state = S_DOCTYPE;\n                if (this.doctype || this.sawRoot) {\n                    this.fail(\"inappropriately located doctype declaration.\");\n                }\n                this.openWakaBang = \"\";\n                break;\n            default:\n                // 7 happens to be the maximum length of the string that can possibly\n                // match one of the cases above.\n                if (this.openWakaBang.length >= 7) {\n                    this.fail(\"incorrect syntax.\");\n                }\n        }\n    }\n    sComment() {\n        if (this.captureToChar(MINUS)) {\n            this.state = S_COMMENT_ENDING;\n        }\n    }\n    sCommentEnding() {\n        var _a;\n        const c = this.getCodeNorm();\n        if (c === MINUS) {\n            this.state = S_COMMENT_ENDED;\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.commentHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.text);\n            this.text = \"\";\n        }\n        else {\n            this.text += `-${String.fromCodePoint(c)}`;\n            this.state = S_COMMENT;\n        }\n    }\n    sCommentEnded() {\n        const c = this.getCodeNorm();\n        if (c !== GREATER) {\n            this.fail(\"malformed comment.\");\n            // <!-- blah -- bloo --> will be recorded as\n            // a comment of \" blah -- bloo \"\n            this.text += `--${String.fromCodePoint(c)}`;\n            this.state = S_COMMENT;\n        }\n        else {\n            this.state = S_TEXT;\n        }\n    }\n    sCData() {\n        if (this.captureToChar(CLOSE_BRACKET)) {\n            this.state = S_CDATA_ENDING;\n        }\n    }\n    sCDataEnding() {\n        const c = this.getCodeNorm();\n        if (c === CLOSE_BRACKET) {\n            this.state = S_CDATA_ENDING_2;\n        }\n        else {\n            this.text += `]${String.fromCodePoint(c)}`;\n            this.state = S_CDATA;\n        }\n    }\n    sCDataEnding2() {\n        var _a;\n        const c = this.getCodeNorm();\n        switch (c) {\n            case GREATER: {\n                // eslint-disable-next-line no-unused-expressions\n                (_a = this.cdataHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.text);\n                this.text = \"\";\n                this.state = S_TEXT;\n                break;\n            }\n            case CLOSE_BRACKET:\n                this.text += \"]\";\n                break;\n            default:\n                this.text += `]]${String.fromCodePoint(c)}`;\n                this.state = S_CDATA;\n        }\n    }\n    // We need this separate state to check the first character fo the pi target\n    // with this.nameStartCheck which allows less characters than this.nameCheck.\n    sPIFirstChar() {\n        const c = this.getCodeNorm();\n        // This is first because in the case where the file is well-formed this is\n        // the branch taken. We optimize for well-formedness.\n        if (this.nameStartCheck(c)) {\n            this.piTarget += String.fromCodePoint(c);\n            this.state = S_PI_REST;\n        }\n        else if (c === QUESTION || isS(c)) {\n            this.fail(\"processing instruction without a target.\");\n            this.state = c === QUESTION ? S_PI_ENDING : S_PI_BODY;\n        }\n        else {\n            this.fail(\"disallowed character in processing instruction name.\");\n            this.piTarget += String.fromCodePoint(c);\n            this.state = S_PI_REST;\n        }\n    }\n    sPIRest() {\n        // Capture characters into a piTarget while ``this.nameCheck`` run on the\n        // character read returns true.\n        const { chunk, i: start } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const c = this.getCodeNorm();\n            if (c === EOC) {\n                this.piTarget += chunk.slice(start);\n                return;\n            }\n            // NL cannot satisfy this.nameCheck so we don't have to test specifically\n            // for it.\n            if (!this.nameCheck(c)) {\n                this.piTarget += chunk.slice(start, this.prevI);\n                const isQuestion = c === QUESTION;\n                if (isQuestion || isS(c)) {\n                    if (this.piTarget === \"xml\") {\n                        if (!this.xmlDeclPossible) {\n                            this.fail(\"an XML declaration must be at the start of the document.\");\n                        }\n                        this.state = isQuestion ? S_XML_DECL_ENDING : S_XML_DECL_NAME_START;\n                    }\n                    else {\n                        this.state = isQuestion ? S_PI_ENDING : S_PI_BODY;\n                    }\n                }\n                else {\n                    this.fail(\"disallowed character in processing instruction name.\");\n                    this.piTarget += String.fromCodePoint(c);\n                }\n                break;\n            }\n        }\n    }\n    sPIBody() {\n        if (this.text.length === 0) {\n            const c = this.getCodeNorm();\n            if (c === QUESTION) {\n                this.state = S_PI_ENDING;\n            }\n            else if (!isS(c)) {\n                this.text = String.fromCodePoint(c);\n            }\n        }\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        else if (this.captureToChar(QUESTION)) {\n            this.state = S_PI_ENDING;\n        }\n    }\n    sPIEnding() {\n        var _a;\n        const c = this.getCodeNorm();\n        if (c === GREATER) {\n            const { piTarget } = this;\n            if (piTarget.toLowerCase() === \"xml\") {\n                this.fail(\"the XML declaration must appear at the start of the document.\");\n            }\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.piHandler) === null || _a === void 0 ? void 0 : _a.call(this, {\n                target: piTarget,\n                body: this.text,\n            });\n            this.piTarget = this.text = \"\";\n            this.state = S_TEXT;\n        }\n        else if (c === QUESTION) {\n            // We ran into ?? as part of a processing instruction. We initially took\n            // the first ? as a sign that the PI was ending, but it is not. So we have\n            // to add it to the body but we take the new ? as a sign that the PI is\n            // ending.\n            this.text += \"?\";\n        }\n        else {\n            this.text += `?${String.fromCodePoint(c)}`;\n            this.state = S_PI_BODY;\n        }\n        this.xmlDeclPossible = false;\n    }\n    sXMLDeclNameStart() {\n        const c = this.skipSpaces();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            // It is valid to go to S_XML_DECL_ENDING from this state.\n            this.state = S_XML_DECL_ENDING;\n            return;\n        }\n        if (c !== EOC) {\n            this.state = S_XML_DECL_NAME;\n            this.name = String.fromCodePoint(c);\n        }\n    }\n    sXMLDeclName() {\n        const c = this.captureTo(XML_DECL_NAME_TERMINATOR);\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.name += this.text;\n            this.text = \"\";\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (!(isS(c) || c === EQUAL)) {\n            return;\n        }\n        this.name += this.text;\n        this.text = \"\";\n        if (!this.xmlDeclExpects.includes(this.name)) {\n            switch (this.name.length) {\n                case 0:\n                    this.fail(\"did not expect any more name/value pairs.\");\n                    break;\n                case 1:\n                    this.fail(`expected the name ${this.xmlDeclExpects[0]}.`);\n                    break;\n                default:\n                    this.fail(`expected one of ${this.xmlDeclExpects.join(\", \")}`);\n            }\n        }\n        this.state = c === EQUAL ? S_XML_DECL_VALUE_START : S_XML_DECL_EQ;\n    }\n    sXMLDeclEq() {\n        const c = this.getCodeNorm();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (isS(c)) {\n            return;\n        }\n        if (c !== EQUAL) {\n            this.fail(\"value required.\");\n        }\n        this.state = S_XML_DECL_VALUE_START;\n    }\n    sXMLDeclValueStart() {\n        const c = this.getCodeNorm();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (isS(c)) {\n            return;\n        }\n        if (!isQuote(c)) {\n            this.fail(\"value must be quoted.\");\n            this.q = SPACE;\n        }\n        else {\n            this.q = c;\n        }\n        this.state = S_XML_DECL_VALUE;\n    }\n    sXMLDeclValue() {\n        const c = this.captureTo([this.q, QUESTION]);\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            this.state = S_XML_DECL_ENDING;\n            this.text = \"\";\n            this.fail(\"XML declaration is incomplete.\");\n            return;\n        }\n        if (c === EOC) {\n            return;\n        }\n        const value = this.text;\n        this.text = \"\";\n        switch (this.name) {\n            case \"version\": {\n                this.xmlDeclExpects = [\"encoding\", \"standalone\"];\n                const version = value;\n                this.xmlDecl.version = version;\n                // This is the test specified by XML 1.0 but it is fine for XML 1.1.\n                if (!/^1\\.[0-9]+$/.test(version)) {\n                    this.fail(\"version number must match /^1\\\\.[0-9]+$/.\");\n                }\n                // When forceXMLVersion is set, the XML declaration is ignored.\n                else if (!this.opt.forceXMLVersion) {\n                    this.setXMLVersion(version);\n                }\n                break;\n            }\n            case \"encoding\":\n                if (!/^[A-Za-z][A-Za-z0-9._-]*$/.test(value)) {\n                    this.fail(\"encoding value must match \\\n/^[A-Za-z0-9][A-Za-z0-9._-]*$/.\");\n                }\n                this.xmlDeclExpects = [\"standalone\"];\n                this.xmlDecl.encoding = value;\n                break;\n            case \"standalone\":\n                if (value !== \"yes\" && value !== \"no\") {\n                    this.fail(\"standalone value must match \\\"yes\\\" or \\\"no\\\".\");\n                }\n                this.xmlDeclExpects = [];\n                this.xmlDecl.standalone = value;\n                break;\n            default:\n            // We don't need to raise an error here since we've already raised one\n            // when checking what name was expected.\n        }\n        this.name = \"\";\n        this.state = S_XML_DECL_SEPARATOR;\n    }\n    sXMLDeclSeparator() {\n        const c = this.getCodeNorm();\n        // The question mark character is not valid inside any of the XML\n        // declaration name/value pairs.\n        if (c === QUESTION) {\n            // It is valid to go to S_XML_DECL_ENDING from this state.\n            this.state = S_XML_DECL_ENDING;\n            return;\n        }\n        if (!isS(c)) {\n            this.fail(\"whitespace required.\");\n            this.unget();\n        }\n        this.state = S_XML_DECL_NAME_START;\n    }\n    sXMLDeclEnding() {\n        var _a;\n        const c = this.getCodeNorm();\n        if (c === GREATER) {\n            if (this.piTarget !== \"xml\") {\n                this.fail(\"processing instructions are not allowed before root.\");\n            }\n            else if (this.name !== \"version\" &&\n                this.xmlDeclExpects.includes(\"version\")) {\n                this.fail(\"XML declaration must contain a version.\");\n            }\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.xmldeclHandler) === null || _a === void 0 ? void 0 : _a.call(this, this.xmlDecl);\n            this.name = \"\";\n            this.piTarget = this.text = \"\";\n            this.state = S_TEXT;\n        }\n        else {\n            // We got here because the previous character was a ?, but the question\n            // mark character is not valid inside any of the XML declaration\n            // name/value pairs.\n            this.fail(\"The character ? is disallowed anywhere in XML declarations.\");\n        }\n        this.xmlDeclPossible = false;\n    }\n    sOpenTag() {\n        var _a;\n        const c = this.captureNameChars();\n        if (c === EOC) {\n            return;\n        }\n        const tag = this.tag = {\n            name: this.name,\n            attributes: Object.create(null),\n        };\n        this.name = \"\";\n        if (this.xmlnsOpt) {\n            this.topNS = tag.ns = Object.create(null);\n        }\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.openTagStartHandler) === null || _a === void 0 ? void 0 : _a.call(this, tag);\n        this.sawRoot = true;\n        if (!this.fragmentOpt && this.closedRoot) {\n            this.fail(\"documents may contain only one root.\");\n        }\n        switch (c) {\n            case GREATER:\n                this.openTag();\n                break;\n            case FORWARD_SLASH:\n                this.state = S_OPEN_TAG_SLASH;\n                break;\n            default:\n                if (!isS(c)) {\n                    this.fail(\"disallowed character in tag name.\");\n                }\n                this.state = S_ATTRIB;\n        }\n    }\n    sOpenTagSlash() {\n        if (this.getCode() === GREATER) {\n            this.openSelfClosingTag();\n        }\n        else {\n            this.fail(\"forward-slash in opening tag not followed by >.\");\n            this.state = S_ATTRIB;\n        }\n    }\n    sAttrib() {\n        const c = this.skipSpaces();\n        if (c === EOC) {\n            return;\n        }\n        if (isNameStartChar(c)) {\n            this.unget();\n            this.state = S_ATTRIB_NAME;\n        }\n        else if (c === GREATER) {\n            this.openTag();\n        }\n        else if (c === FORWARD_SLASH) {\n            this.state = S_OPEN_TAG_SLASH;\n        }\n        else {\n            this.fail(\"disallowed character in attribute name.\");\n        }\n    }\n    sAttribName() {\n        const c = this.captureNameChars();\n        if (c === EQUAL) {\n            this.state = S_ATTRIB_VALUE;\n        }\n        else if (isS(c)) {\n            this.state = S_ATTRIB_NAME_SAW_WHITE;\n        }\n        else if (c === GREATER) {\n            this.fail(\"attribute without value.\");\n            this.pushAttrib(this.name, this.name);\n            this.name = this.text = \"\";\n            this.openTag();\n        }\n        else if (c !== EOC) {\n            this.fail(\"disallowed character in attribute name.\");\n        }\n    }\n    sAttribNameSawWhite() {\n        const c = this.skipSpaces();\n        switch (c) {\n            case EOC:\n                return;\n            case EQUAL:\n                this.state = S_ATTRIB_VALUE;\n                break;\n            default:\n                this.fail(\"attribute without value.\");\n                // Should we do this???\n                // this.tag.attributes[this.name] = \"\";\n                this.text = \"\";\n                this.name = \"\";\n                if (c === GREATER) {\n                    this.openTag();\n                }\n                else if (isNameStartChar(c)) {\n                    this.unget();\n                    this.state = S_ATTRIB_NAME;\n                }\n                else {\n                    this.fail(\"disallowed character in attribute name.\");\n                    this.state = S_ATTRIB;\n                }\n        }\n    }\n    sAttribValue() {\n        const c = this.getCodeNorm();\n        if (isQuote(c)) {\n            this.q = c;\n            this.state = S_ATTRIB_VALUE_QUOTED;\n        }\n        else if (!isS(c)) {\n            this.fail(\"unquoted attribute value.\");\n            this.state = S_ATTRIB_VALUE_UNQUOTED;\n            this.unget();\n        }\n    }\n    sAttribValueQuoted() {\n        // We deliberately do not use captureTo here. The specialized code we use\n        // here is faster than using captureTo.\n        const { q, chunk } = this;\n        let { i: start } = this;\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            switch (this.getCode()) {\n                case q:\n                    this.pushAttrib(this.name, this.text + chunk.slice(start, this.prevI));\n                    this.name = this.text = \"\";\n                    this.q = null;\n                    this.state = S_ATTRIB_VALUE_CLOSED;\n                    return;\n                case AMP:\n                    this.text += chunk.slice(start, this.prevI);\n                    this.state = S_ENTITY;\n                    this.entityReturnState = S_ATTRIB_VALUE_QUOTED;\n                    return;\n                case NL:\n                case NL_LIKE:\n                case TAB:\n                    this.text += `${chunk.slice(start, this.prevI)} `;\n                    start = this.i;\n                    break;\n                case LESS:\n                    this.text += chunk.slice(start, this.prevI);\n                    this.fail(\"disallowed character.\");\n                    return;\n                case EOC:\n                    this.text += chunk.slice(start);\n                    return;\n                default:\n            }\n        }\n    }\n    sAttribValueClosed() {\n        const c = this.getCodeNorm();\n        if (isS(c)) {\n            this.state = S_ATTRIB;\n        }\n        else if (c === GREATER) {\n            this.openTag();\n        }\n        else if (c === FORWARD_SLASH) {\n            this.state = S_OPEN_TAG_SLASH;\n        }\n        else if (isNameStartChar(c)) {\n            this.fail(\"no whitespace between attributes.\");\n            this.unget();\n            this.state = S_ATTRIB_NAME;\n        }\n        else {\n            this.fail(\"disallowed character in attribute name.\");\n        }\n    }\n    sAttribValueUnquoted() {\n        // We don't do anything regarding EOL or space handling for unquoted\n        // attributes. We already have failed by the time we get here, and the\n        // contract that saxes upholds states that upon failure, it is not safe to\n        // rely on the data passed to event handlers (other than\n        // ``onerror``). Passing \"bad\" data is not a problem.\n        const c = this.captureTo(ATTRIB_VALUE_UNQUOTED_TERMINATOR);\n        switch (c) {\n            case AMP:\n                this.state = S_ENTITY;\n                this.entityReturnState = S_ATTRIB_VALUE_UNQUOTED;\n                break;\n            case LESS:\n                this.fail(\"disallowed character.\");\n                break;\n            case EOC:\n                break;\n            default:\n                if (this.text.includes(\"]]>\")) {\n                    this.fail(\"the string \\\"]]>\\\" is disallowed in char data.\");\n                }\n                this.pushAttrib(this.name, this.text);\n                this.name = this.text = \"\";\n                if (c === GREATER) {\n                    this.openTag();\n                }\n                else {\n                    this.state = S_ATTRIB;\n                }\n        }\n    }\n    sCloseTag() {\n        const c = this.captureNameChars();\n        if (c === GREATER) {\n            this.closeTag();\n        }\n        else if (isS(c)) {\n            this.state = S_CLOSE_TAG_SAW_WHITE;\n        }\n        else if (c !== EOC) {\n            this.fail(\"disallowed character in closing tag.\");\n        }\n    }\n    sCloseTagSawWhite() {\n        switch (this.skipSpaces()) {\n            case GREATER:\n                this.closeTag();\n                break;\n            case EOC:\n                break;\n            default:\n                this.fail(\"disallowed character in closing tag.\");\n        }\n    }\n    // END OF STATE ENGINE METHODS\n    handleTextInRoot() {\n        // This is essentially a specialized version of captureTo which is optimized\n        // for performing the ]]> check. A previous version of this code, checked\n        // ``this.text`` for the presence of ]]>. It simplified the code but was\n        // very costly when character data contained a lot of entities to be parsed.\n        //\n        // Since we are using a specialized loop, we also keep track of the presence\n        // of ]]> in text data. The sequence ]]> is forbidden to appear as-is.\n        //\n        let { i: start, forbiddenState } = this;\n        const { chunk, textHandler: handler } = this;\n        // eslint-disable-next-line no-labels, no-restricted-syntax\n        scanLoop: \n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            switch (this.getCode()) {\n                case LESS: {\n                    this.state = S_OPEN_WAKA;\n                    if (handler !== undefined) {\n                        const { text } = this;\n                        const slice = chunk.slice(start, this.prevI);\n                        if (text.length !== 0) {\n                            handler(text + slice);\n                            this.text = \"\";\n                        }\n                        else if (slice.length !== 0) {\n                            handler(slice);\n                        }\n                    }\n                    forbiddenState = FORBIDDEN_START;\n                    // eslint-disable-next-line no-labels\n                    break scanLoop;\n                }\n                case AMP:\n                    this.state = S_ENTITY;\n                    this.entityReturnState = S_TEXT;\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start, this.prevI);\n                    }\n                    forbiddenState = FORBIDDEN_START;\n                    // eslint-disable-next-line no-labels\n                    break scanLoop;\n                case CLOSE_BRACKET:\n                    switch (forbiddenState) {\n                        case FORBIDDEN_START:\n                            forbiddenState = FORBIDDEN_BRACKET;\n                            break;\n                        case FORBIDDEN_BRACKET:\n                            forbiddenState = FORBIDDEN_BRACKET_BRACKET;\n                            break;\n                        case FORBIDDEN_BRACKET_BRACKET:\n                            break;\n                        default:\n                            throw new Error(\"impossible state\");\n                    }\n                    break;\n                case GREATER:\n                    if (forbiddenState === FORBIDDEN_BRACKET_BRACKET) {\n                        this.fail(\"the string \\\"]]>\\\" is disallowed in char data.\");\n                    }\n                    forbiddenState = FORBIDDEN_START;\n                    break;\n                case NL_LIKE:\n                    if (handler !== undefined) {\n                        this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                    }\n                    start = this.i;\n                    forbiddenState = FORBIDDEN_START;\n                    break;\n                case EOC:\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start);\n                    }\n                    // eslint-disable-next-line no-labels\n                    break scanLoop;\n                default:\n                    forbiddenState = FORBIDDEN_START;\n            }\n        }\n        this.forbiddenState = forbiddenState;\n    }\n    handleTextOutsideRoot() {\n        // This is essentially a specialized version of captureTo which is optimized\n        // for a specialized task. We keep track of the presence of non-space\n        // characters in the text since these are errors when appearing outside the\n        // document root element.\n        let { i: start } = this;\n        const { chunk, textHandler: handler } = this;\n        let nonSpace = false;\n        // eslint-disable-next-line no-labels, no-restricted-syntax\n        outRootLoop: \n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const code = this.getCode();\n            switch (code) {\n                case LESS: {\n                    this.state = S_OPEN_WAKA;\n                    if (handler !== undefined) {\n                        const { text } = this;\n                        const slice = chunk.slice(start, this.prevI);\n                        if (text.length !== 0) {\n                            handler(text + slice);\n                            this.text = \"\";\n                        }\n                        else if (slice.length !== 0) {\n                            handler(slice);\n                        }\n                    }\n                    // eslint-disable-next-line no-labels\n                    break outRootLoop;\n                }\n                case AMP:\n                    this.state = S_ENTITY;\n                    this.entityReturnState = S_TEXT;\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start, this.prevI);\n                    }\n                    nonSpace = true;\n                    // eslint-disable-next-line no-labels\n                    break outRootLoop;\n                case NL_LIKE:\n                    if (handler !== undefined) {\n                        this.text += `${chunk.slice(start, this.prevI)}\\n`;\n                    }\n                    start = this.i;\n                    break;\n                case EOC:\n                    if (handler !== undefined) {\n                        this.text += chunk.slice(start);\n                    }\n                    // eslint-disable-next-line no-labels\n                    break outRootLoop;\n                default:\n                    if (!isS(code)) {\n                        nonSpace = true;\n                    }\n            }\n        }\n        if (!nonSpace) {\n            return;\n        }\n        // We use the reportedTextBeforeRoot and reportedTextAfterRoot flags\n        // to avoid reporting errors for every single character that is out of\n        // place.\n        if (!this.sawRoot && !this.reportedTextBeforeRoot) {\n            this.fail(\"text data outside of root node.\");\n            this.reportedTextBeforeRoot = true;\n        }\n        if (this.closedRoot && !this.reportedTextAfterRoot) {\n            this.fail(\"text data outside of root node.\");\n            this.reportedTextAfterRoot = true;\n        }\n    }\n    pushAttribNS(name, value) {\n        var _a;\n        const { prefix, local } = this.qname(name);\n        const attr = { name, prefix, local, value };\n        this.attribList.push(attr);\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.attributeHandler) === null || _a === void 0 ? void 0 : _a.call(this, attr);\n        if (prefix === \"xmlns\") {\n            const trimmed = value.trim();\n            if (this.currentXMLVersion === \"1.0\" && trimmed === \"\") {\n                this.fail(\"invalid attempt to undefine prefix in XML 1.0\");\n            }\n            this.topNS[local] = trimmed;\n            nsPairCheck(this, local, trimmed);\n        }\n        else if (name === \"xmlns\") {\n            const trimmed = value.trim();\n            this.topNS[\"\"] = trimmed;\n            nsPairCheck(this, \"\", trimmed);\n        }\n    }\n    pushAttribPlain(name, value) {\n        var _a;\n        const attr = { name, value };\n        this.attribList.push(attr);\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.attributeHandler) === null || _a === void 0 ? void 0 : _a.call(this, attr);\n    }\n    /**\n     * End parsing. This performs final well-formedness checks and resets the\n     * parser to a clean state.\n     *\n     * @returns this\n     */\n    end() {\n        var _a, _b;\n        if (!this.sawRoot) {\n            this.fail(\"document must contain a root element.\");\n        }\n        const { tags } = this;\n        while (tags.length > 0) {\n            const tag = tags.pop();\n            this.fail(`unclosed tag: ${tag.name}`);\n        }\n        if ((this.state !== S_BEGIN) && (this.state !== S_TEXT)) {\n            this.fail(\"unexpected end.\");\n        }\n        const { text } = this;\n        if (text.length !== 0) {\n            // eslint-disable-next-line no-unused-expressions\n            (_a = this.textHandler) === null || _a === void 0 ? void 0 : _a.call(this, text);\n            this.text = \"\";\n        }\n        this._closed = true;\n        // eslint-disable-next-line no-unused-expressions\n        (_b = this.endHandler) === null || _b === void 0 ? void 0 : _b.call(this);\n        this._init();\n        return this;\n    }\n    /**\n     * Resolve a namespace prefix.\n     *\n     * @param prefix The prefix to resolve.\n     *\n     * @returns The namespace URI or ``undefined`` if the prefix is not defined.\n     */\n    resolve(prefix) {\n        var _a, _b;\n        let uri = this.topNS[prefix];\n        if (uri !== undefined) {\n            return uri;\n        }\n        const { tags } = this;\n        for (let index = tags.length - 1; index >= 0; index--) {\n            uri = tags[index].ns[prefix];\n            if (uri !== undefined) {\n                return uri;\n            }\n        }\n        uri = this.ns[prefix];\n        if (uri !== undefined) {\n            return uri;\n        }\n        return (_b = (_a = this.opt).resolvePrefix) === null || _b === void 0 ? void 0 : _b.call(_a, prefix);\n    }\n    /**\n     * Parse a qname into its prefix and local name parts.\n     *\n     * @param name The name to parse\n     *\n     * @returns\n     */\n    qname(name) {\n        // This is faster than using name.split(\":\").\n        const colon = name.indexOf(\":\");\n        if (colon === -1) {\n            return { prefix: \"\", local: name };\n        }\n        const local = name.slice(colon + 1);\n        const prefix = name.slice(0, colon);\n        if (prefix === \"\" || local === \"\" || local.includes(\":\")) {\n            this.fail(`malformed name: ${name}.`);\n        }\n        return { prefix, local };\n    }\n    processAttribsNS() {\n        var _a;\n        const { attribList } = this;\n        const tag = this.tag;\n        {\n            // add namespace info to tag\n            const { prefix, local } = this.qname(tag.name);\n            tag.prefix = prefix;\n            tag.local = local;\n            const uri = tag.uri = (_a = this.resolve(prefix)) !== null && _a !== void 0 ? _a : \"\";\n            if (prefix !== \"\") {\n                if (prefix === \"xmlns\") {\n                    this.fail(\"tags may not have \\\"xmlns\\\" as prefix.\");\n                }\n                if (uri === \"\") {\n                    this.fail(`unbound namespace prefix: ${JSON.stringify(prefix)}.`);\n                    tag.uri = prefix;\n                }\n            }\n        }\n        if (attribList.length === 0) {\n            return;\n        }\n        const { attributes } = tag;\n        const seen = new Set();\n        // Note: do not apply default ns to attributes:\n        //   http://www.w3.org/TR/REC-xml-names/#defaulting\n        for (const attr of attribList) {\n            const { name, prefix, local } = attr;\n            let uri;\n            let eqname;\n            if (prefix === \"\") {\n                uri = name === \"xmlns\" ? XMLNS_NAMESPACE : \"\";\n                eqname = name;\n            }\n            else {\n                uri = this.resolve(prefix);\n                // if there's any attributes with an undefined namespace,\n                // then fail on them now.\n                if (uri === undefined) {\n                    this.fail(`unbound namespace prefix: ${JSON.stringify(prefix)}.`);\n                    uri = prefix;\n                }\n                eqname = `{${uri}}${local}`;\n            }\n            if (seen.has(eqname)) {\n                this.fail(`duplicate attribute: ${eqname}.`);\n            }\n            seen.add(eqname);\n            attr.uri = uri;\n            attributes[name] = attr;\n        }\n        this.attribList = [];\n    }\n    processAttribsPlain() {\n        const { attribList } = this;\n        // eslint-disable-next-line prefer-destructuring\n        const attributes = this.tag.attributes;\n        for (const { name, value } of attribList) {\n            if (attributes[name] !== undefined) {\n                this.fail(`duplicate attribute: ${name}.`);\n            }\n            attributes[name] = value;\n        }\n        this.attribList = [];\n    }\n    /**\n     * Handle a complete open tag. This parser code calls this once it has seen\n     * the whole tag. This method checks for well-formeness and then emits\n     * ``onopentag``.\n     */\n    openTag() {\n        var _a;\n        this.processAttribs();\n        const { tags } = this;\n        const tag = this.tag;\n        tag.isSelfClosing = false;\n        // There cannot be any pending text here due to the onopentagstart that was\n        // necessarily emitted before we get here. So we do not check text.\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.openTagHandler) === null || _a === void 0 ? void 0 : _a.call(this, tag);\n        tags.push(tag);\n        this.state = S_TEXT;\n        this.name = \"\";\n    }\n    /**\n     * Handle a complete self-closing tag. This parser code calls this once it has\n     * seen the whole tag. This method checks for well-formeness and then emits\n     * ``onopentag`` and ``onclosetag``.\n     */\n    openSelfClosingTag() {\n        var _a, _b, _c;\n        this.processAttribs();\n        const { tags } = this;\n        const tag = this.tag;\n        tag.isSelfClosing = true;\n        // There cannot be any pending text here due to the onopentagstart that was\n        // necessarily emitted before we get here. So we do not check text.\n        // eslint-disable-next-line no-unused-expressions\n        (_a = this.openTagHandler) === null || _a === void 0 ? void 0 : _a.call(this, tag);\n        // eslint-disable-next-line no-unused-expressions\n        (_b = this.closeTagHandler) === null || _b === void 0 ? void 0 : _b.call(this, tag);\n        const top = this.tag = (_c = tags[tags.length - 1]) !== null && _c !== void 0 ? _c : null;\n        if (top === null) {\n            this.closedRoot = true;\n        }\n        this.state = S_TEXT;\n        this.name = \"\";\n    }\n    /**\n     * Handle a complete close tag. This parser code calls this once it has seen\n     * the whole tag. This method checks for well-formeness and then emits\n     * ``onclosetag``.\n     */\n    closeTag() {\n        const { tags, name } = this;\n        // Our state after this will be S_TEXT, no matter what, and we can clear\n        // tagName now.\n        this.state = S_TEXT;\n        this.name = \"\";\n        if (name === \"\") {\n            this.fail(\"weird empty close tag.\");\n            this.text += \"</>\";\n            return;\n        }\n        const handler = this.closeTagHandler;\n        let l = tags.length;\n        while (l-- > 0) {\n            const tag = this.tag = tags.pop();\n            this.topNS = tag.ns;\n            // eslint-disable-next-line no-unused-expressions\n            handler === null || handler === void 0 ? void 0 : handler(tag);\n            if (tag.name === name) {\n                break;\n            }\n            this.fail(\"unexpected close tag.\");\n        }\n        if (l === 0) {\n            this.closedRoot = true;\n        }\n        else if (l < 0) {\n            this.fail(`unmatched closing tag: ${name}.`);\n            this.text += `</${name}>`;\n        }\n    }\n    /**\n     * Resolves an entity. Makes any necessary well-formedness checks.\n     *\n     * @param entity The entity to resolve.\n     *\n     * @returns The parsed entity.\n     */\n    parseEntity(entity) {\n        // startsWith would be significantly slower for this test.\n        // eslint-disable-next-line @typescript-eslint/prefer-string-starts-ends-with\n        if (entity[0] !== \"#\") {\n            const defined = this.ENTITIES[entity];\n            if (defined !== undefined) {\n                return defined;\n            }\n            this.fail(this.isName(entity) ? \"undefined entity.\" :\n                \"disallowed character in entity name.\");\n            return `&${entity};`;\n        }\n        let num = NaN;\n        if (entity[1] === \"x\" && /^#x[0-9a-f]+$/i.test(entity)) {\n            num = parseInt(entity.slice(2), 16);\n        }\n        else if (/^#[0-9]+$/.test(entity)) {\n            num = parseInt(entity.slice(1), 10);\n        }\n        // The character reference is required to match the CHAR production.\n        if (!this.isChar(num)) {\n            this.fail(\"malformed character entity.\");\n            return `&${entity};`;\n        }\n        return String.fromCodePoint(num);\n    }\n}\nexports.SaxesParser = SaxesParser;\n//# sourceMappingURL=saxes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/saxes/saxes.js\n");

/***/ })

};
;