/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tmp";
exports.ids = ["vendor-chunks/tmp"];
exports.modules = {

/***/ "(ssr)/./node_modules/tmp/lib/tmp.js":
/*!*************************************!*\
  !*** ./node_modules/tmp/lib/tmp.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * Tmp\n *\n * Copyright (c) 2011-2017 KARASZI Istvan <<EMAIL>>\n *\n * MIT Licensed\n */\n\n/*\n * Module dependencies.\n */\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst os = __webpack_require__(/*! os */ \"os\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst crypto = __webpack_require__(/*! crypto */ \"crypto\");\nconst _c = { fs: fs.constants, os: os.constants };\n\n/*\n * The working inner variables.\n */\nconst\n  // the random characters to choose from\n  RANDOM_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz',\n\n  TEMPLATE_PATTERN = /XXXXXX/,\n\n  DEFAULT_TRIES = 3,\n\n  CREATE_FLAGS = (_c.O_CREAT || _c.fs.O_CREAT) | (_c.O_EXCL || _c.fs.O_EXCL) | (_c.O_RDWR || _c.fs.O_RDWR),\n\n  // constants are off on the windows platform and will not match the actual errno codes\n  IS_WIN32 = os.platform() === 'win32',\n  EBADF = _c.EBADF || _c.os.errno.EBADF,\n  ENOENT = _c.ENOENT || _c.os.errno.ENOENT,\n\n  DIR_MODE = 0o700 /* 448 */,\n  FILE_MODE = 0o600 /* 384 */,\n\n  EXIT = 'exit',\n\n  // this will hold the objects need to be removed on exit\n  _removeObjects = [],\n\n  // API change in fs.rmdirSync leads to error when passing in a second parameter, e.g. the callback\n  FN_RMDIR_SYNC = fs.rmdirSync.bind(fs);\n\nlet\n  _gracefulCleanup = false;\n\n/**\n * Recursively remove a directory and its contents.\n *\n * @param {string} dirPath path of directory to remove\n * @param {Function} callback\n * @private\n */\nfunction rimraf(dirPath, callback) {\n  return fs.rm(dirPath, { recursive: true }, callback);\n}\n\n/**\n * Recursively remove a directory and its contents, synchronously.\n *\n * @param {string} dirPath path of directory to remove\n * @private\n */\nfunction FN_RIMRAF_SYNC(dirPath) {\n  return fs.rmSync(dirPath, { recursive: true });\n}\n\n/**\n * Gets a temporary file name.\n *\n * @param {(Options|tmpNameCallback)} options options or callback\n * @param {?tmpNameCallback} callback the callback function\n */\nfunction tmpName(options, callback) {\n  const\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  try {\n    _assertAndSanitizeOptions(opts);\n  } catch (err) {\n    return cb(err);\n  }\n\n  let tries = opts.tries;\n  (function _getUniqueName() {\n    try {\n      const name = _generateTmpName(opts);\n\n      // check whether the path exists then retry if needed\n      fs.stat(name, function (err) {\n        /* istanbul ignore else */\n        if (!err) {\n          /* istanbul ignore else */\n          if (tries-- > 0) return _getUniqueName();\n\n          return cb(new Error('Could not get a unique tmp filename, max tries reached ' + name));\n        }\n\n        cb(null, name);\n      });\n    } catch (err) {\n      cb(err);\n    }\n  }());\n}\n\n/**\n * Synchronous version of tmpName.\n *\n * @param {Object} options\n * @returns {string} the generated random name\n * @throws {Error} if the options are invalid or could not generate a filename\n */\nfunction tmpNameSync(options) {\n  const\n    args = _parseArguments(options),\n    opts = args[0];\n\n  _assertAndSanitizeOptions(opts);\n\n  let tries = opts.tries;\n  do {\n    const name = _generateTmpName(opts);\n    try {\n      fs.statSync(name);\n    } catch (e) {\n      return name;\n    }\n  } while (tries-- > 0);\n\n  throw new Error('Could not get a unique tmp filename, max tries reached');\n}\n\n/**\n * Creates and opens a temporary file.\n *\n * @param {(Options|null|undefined|fileCallback)} options the config options or the callback function or null or undefined\n * @param {?fileCallback} callback\n */\nfunction file(options, callback) {\n  const\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create and open the file\n    fs.open(name, CREATE_FLAGS, opts.mode || FILE_MODE, function _fileCreated(err, fd) {\n      /* istanbu ignore else */\n      if (err) return cb(err);\n\n      if (opts.discardDescriptor) {\n        return fs.close(fd, function _discardCallback(possibleErr) {\n          // the chance of getting an error on close here is rather low and might occur in the most edgiest cases only\n          return cb(possibleErr, name, undefined, _prepareTmpFileRemoveCallback(name, -1, opts, false));\n        });\n      } else {\n        // detachDescriptor passes the descriptor whereas discardDescriptor closes it, either way, we no longer care\n        // about the descriptor\n        const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n        cb(null, name, fd, _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, false));\n      }\n    });\n  });\n}\n\n/**\n * Synchronous version of file.\n *\n * @param {Options} options\n * @returns {FileSyncObject} object consists of name, fd and removeCallback\n * @throws {Error} if cannot create a file\n */\nfunction fileSync(options) {\n  const\n    args = _parseArguments(options),\n    opts = args[0];\n\n  const discardOrDetachDescriptor = opts.discardDescriptor || opts.detachDescriptor;\n  const name = tmpNameSync(opts);\n  var fd = fs.openSync(name, CREATE_FLAGS, opts.mode || FILE_MODE);\n  /* istanbul ignore else */\n  if (opts.discardDescriptor) {\n    fs.closeSync(fd);\n    fd = undefined;\n  }\n\n  return {\n    name: name,\n    fd: fd,\n    removeCallback: _prepareTmpFileRemoveCallback(name, discardOrDetachDescriptor ? -1 : fd, opts, true)\n  };\n}\n\n/**\n * Creates a temporary directory.\n *\n * @param {(Options|dirCallback)} options the options or the callback function\n * @param {?dirCallback} callback\n */\nfunction dir(options, callback) {\n  const\n    args = _parseArguments(options, callback),\n    opts = args[0],\n    cb = args[1];\n\n  // gets a temporary filename\n  tmpName(opts, function _tmpNameCreated(err, name) {\n    /* istanbul ignore else */\n    if (err) return cb(err);\n\n    // create the directory\n    fs.mkdir(name, opts.mode || DIR_MODE, function _dirCreated(err) {\n      /* istanbul ignore else */\n      if (err) return cb(err);\n\n      cb(null, name, _prepareTmpDirRemoveCallback(name, opts, false));\n    });\n  });\n}\n\n/**\n * Synchronous version of dir.\n *\n * @param {Options} options\n * @returns {DirSyncObject} object consists of name and removeCallback\n * @throws {Error} if it cannot create a directory\n */\nfunction dirSync(options) {\n  const\n    args = _parseArguments(options),\n    opts = args[0];\n\n  const name = tmpNameSync(opts);\n  fs.mkdirSync(name, opts.mode || DIR_MODE);\n\n  return {\n    name: name,\n    removeCallback: _prepareTmpDirRemoveCallback(name, opts, true)\n  };\n}\n\n/**\n * Removes files asynchronously.\n *\n * @param {Object} fdPath\n * @param {Function} next\n * @private\n */\nfunction _removeFileAsync(fdPath, next) {\n  const _handler = function (err) {\n    if (err && !_isENOENT(err)) {\n      // reraise any unanticipated error\n      return next(err);\n    }\n    next();\n  };\n\n  if (0 <= fdPath[0])\n    fs.close(fdPath[0], function () {\n      fs.unlink(fdPath[1], _handler);\n    });\n  else fs.unlink(fdPath[1], _handler);\n}\n\n/**\n * Removes files synchronously.\n *\n * @param {Object} fdPath\n * @private\n */\nfunction _removeFileSync(fdPath) {\n  let rethrownException = null;\n  try {\n    if (0 <= fdPath[0]) fs.closeSync(fdPath[0]);\n  } catch (e) {\n    // reraise any unanticipated error\n    if (!_isEBADF(e) && !_isENOENT(e)) throw e;\n  } finally {\n    try {\n      fs.unlinkSync(fdPath[1]);\n    }\n    catch (e) {\n      // reraise any unanticipated error\n      if (!_isENOENT(e)) rethrownException = e;\n    }\n  }\n  if (rethrownException !== null) {\n    throw rethrownException;\n  }\n}\n\n/**\n * Prepares the callback for removal of the temporary file.\n *\n * Returns either a sync callback or a async callback depending on whether\n * fileSync or file was called, which is expressed by the sync parameter.\n *\n * @param {string} name the path of the file\n * @param {number} fd file descriptor\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {fileCallback | fileCallbackSync}\n * @private\n */\nfunction _prepareTmpFileRemoveCallback(name, fd, opts, sync) {\n  const removeCallbackSync = _prepareRemoveCallback(_removeFileSync, [fd, name], sync);\n  const removeCallback = _prepareRemoveCallback(_removeFileAsync, [fd, name], sync, removeCallbackSync);\n\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Prepares the callback for removal of the temporary directory.\n *\n * Returns either a sync callback or a async callback depending on whether\n * tmpFileSync or tmpFile was called, which is expressed by the sync parameter.\n *\n * @param {string} name\n * @param {Object} opts\n * @param {boolean} sync\n * @returns {Function} the callback\n * @private\n */\nfunction _prepareTmpDirRemoveCallback(name, opts, sync) {\n  const removeFunction = opts.unsafeCleanup ? rimraf : fs.rmdir.bind(fs);\n  const removeFunctionSync = opts.unsafeCleanup ? FN_RIMRAF_SYNC : FN_RMDIR_SYNC;\n  const removeCallbackSync = _prepareRemoveCallback(removeFunctionSync, name, sync);\n  const removeCallback = _prepareRemoveCallback(removeFunction, name, sync, removeCallbackSync);\n  if (!opts.keep) _removeObjects.unshift(removeCallbackSync);\n\n  return sync ? removeCallbackSync : removeCallback;\n}\n\n/**\n * Creates a guarded function wrapping the removeFunction call.\n *\n * The cleanup callback is save to be called multiple times.\n * Subsequent invocations will be ignored.\n *\n * @param {Function} removeFunction\n * @param {string} fileOrDirName\n * @param {boolean} sync\n * @param {cleanupCallbackSync?} cleanupCallbackSync\n * @returns {cleanupCallback | cleanupCallbackSync}\n * @private\n */\nfunction _prepareRemoveCallback(removeFunction, fileOrDirName, sync, cleanupCallbackSync) {\n  let called = false;\n\n  // if sync is true, the next parameter will be ignored\n  return function _cleanupCallback(next) {\n\n    /* istanbul ignore else */\n    if (!called) {\n      // remove cleanupCallback from cache\n      const toRemove = cleanupCallbackSync || _cleanupCallback;\n      const index = _removeObjects.indexOf(toRemove);\n      /* istanbul ignore else */\n      if (index >= 0) _removeObjects.splice(index, 1);\n\n      called = true;\n      if (sync || removeFunction === FN_RMDIR_SYNC || removeFunction === FN_RIMRAF_SYNC) {\n        return removeFunction(fileOrDirName);\n      } else {\n        return removeFunction(fileOrDirName, next || function() {});\n      }\n    }\n  };\n}\n\n/**\n * The garbage collector.\n *\n * @private\n */\nfunction _garbageCollector() {\n  /* istanbul ignore else */\n  if (!_gracefulCleanup) return;\n\n  // the function being called removes itself from _removeObjects,\n  // loop until _removeObjects is empty\n  while (_removeObjects.length) {\n    try {\n      _removeObjects[0]();\n    } catch (e) {\n      // already removed?\n    }\n  }\n}\n\n/**\n * Random name generator based on crypto.\n * Adapted from http://blog.tompawlak.org/how-to-generate-random-values-nodejs-javascript\n *\n * @param {number} howMany\n * @returns {string} the generated random name\n * @private\n */\nfunction _randomChars(howMany) {\n  let\n    value = [],\n    rnd = null;\n\n  // make sure that we do not fail because we ran out of entropy\n  try {\n    rnd = crypto.randomBytes(howMany);\n  } catch (e) {\n    rnd = crypto.pseudoRandomBytes(howMany);\n  }\n\n  for (var i = 0; i < howMany; i++) {\n    value.push(RANDOM_CHARS[rnd[i] % RANDOM_CHARS.length]);\n  }\n\n  return value.join('');\n}\n\n/**\n * Helper which determines whether a string s is blank, that is undefined, or empty or null.\n *\n * @private\n * @param {string} s\n * @returns {Boolean} true whether the string s is blank, false otherwise\n */\nfunction _isBlank(s) {\n  return s === null || _isUndefined(s) || !s.trim();\n}\n\n/**\n * Checks whether the `obj` parameter is defined or not.\n *\n * @param {Object} obj\n * @returns {boolean} true if the object is undefined\n * @private\n */\nfunction _isUndefined(obj) {\n  return typeof obj === 'undefined';\n}\n\n/**\n * Parses the function arguments.\n *\n * This function helps to have optional arguments.\n *\n * @param {(Options|null|undefined|Function)} options\n * @param {?Function} callback\n * @returns {Array} parsed arguments\n * @private\n */\nfunction _parseArguments(options, callback) {\n  /* istanbul ignore else */\n  if (typeof options === 'function') {\n    return [{}, options];\n  }\n\n  /* istanbul ignore else */\n  if (_isUndefined(options)) {\n    return [{}, callback];\n  }\n\n  // copy options so we do not leak the changes we make internally\n  const actualOptions = {};\n  for (const key of Object.getOwnPropertyNames(options)) {\n    actualOptions[key] = options[key];\n  }\n\n  return [actualOptions, callback];\n}\n\n/**\n * Generates a new temporary name.\n *\n * @param {Object} opts\n * @returns {string} the new random name according to opts\n * @private\n */\nfunction _generateTmpName(opts) {\n\n  const tmpDir = opts.tmpdir;\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.name))\n    return path.join(tmpDir, opts.dir, opts.name);\n\n  /* istanbul ignore else */\n  if (!_isUndefined(opts.template))\n    return path.join(tmpDir, opts.dir, opts.template).replace(TEMPLATE_PATTERN, _randomChars(6));\n\n  // prefix and postfix\n  const name = [\n    opts.prefix ? opts.prefix : 'tmp',\n    '-',\n    process.pid,\n    '-',\n    _randomChars(12),\n    opts.postfix ? '-' + opts.postfix : ''\n  ].join('');\n\n  return path.join(tmpDir, opts.dir, name);\n}\n\n/**\n * Asserts whether the specified options are valid, also sanitizes options and provides sane defaults for missing\n * options.\n *\n * @param {Options} options\n * @private\n */\nfunction _assertAndSanitizeOptions(options) {\n\n  options.tmpdir = _getTmpDir(options);\n\n  const tmpDir = options.tmpdir;\n\n  /* istanbul ignore else */\n  if (!_isUndefined(options.name))\n    _assertIsRelative(options.name, 'name', tmpDir);\n  /* istanbul ignore else */\n  if (!_isUndefined(options.dir))\n    _assertIsRelative(options.dir, 'dir', tmpDir);\n  /* istanbul ignore else */\n  if (!_isUndefined(options.template)) {\n    _assertIsRelative(options.template, 'template', tmpDir);\n    if (!options.template.match(TEMPLATE_PATTERN))\n      throw new Error(`Invalid template, found \"${options.template}\".`);\n  }\n  /* istanbul ignore else */\n  if (!_isUndefined(options.tries) && isNaN(options.tries) || options.tries < 0)\n    throw new Error(`Invalid tries, found \"${options.tries}\".`);\n\n  // if a name was specified we will try once\n  options.tries = _isUndefined(options.name) ? options.tries || DEFAULT_TRIES : 1;\n  options.keep = !!options.keep;\n  options.detachDescriptor = !!options.detachDescriptor;\n  options.discardDescriptor = !!options.discardDescriptor;\n  options.unsafeCleanup = !!options.unsafeCleanup;\n\n  // sanitize dir, also keep (multiple) blanks if the user, purportedly sane, requests us to\n  options.dir = _isUndefined(options.dir) ? '' : path.relative(tmpDir, _resolvePath(options.dir, tmpDir));\n  options.template = _isUndefined(options.template) ? undefined : path.relative(tmpDir, _resolvePath(options.template, tmpDir));\n  // sanitize further if template is relative to options.dir\n  options.template = _isBlank(options.template) ? undefined : path.relative(options.dir, options.template);\n\n  // for completeness' sake only, also keep (multiple) blanks if the user, purportedly sane, requests us to\n  options.name = _isUndefined(options.name) ? undefined : options.name;\n  options.prefix = _isUndefined(options.prefix) ? '' : options.prefix;\n  options.postfix = _isUndefined(options.postfix) ? '' : options.postfix;\n}\n\n/**\n * Resolve the specified path name in respect to tmpDir.\n *\n * The specified name might include relative path components, e.g. ../\n * so we need to resolve in order to be sure that is is located inside tmpDir\n *\n * @param name\n * @param tmpDir\n * @returns {string}\n * @private\n */\nfunction _resolvePath(name, tmpDir) {\n  if (name.startsWith(tmpDir)) {\n    return path.resolve(name);\n  } else {\n    return path.resolve(path.join(tmpDir, name));\n  }\n}\n\n/**\n * Asserts whether specified name is relative to the specified tmpDir.\n *\n * @param {string} name\n * @param {string} option\n * @param {string} tmpDir\n * @throws {Error}\n * @private\n */\nfunction _assertIsRelative(name, option, tmpDir) {\n  if (option === 'name') {\n    // assert that name is not absolute and does not contain a path\n    if (path.isAbsolute(name))\n      throw new Error(`${option} option must not contain an absolute path, found \"${name}\".`);\n    // must not fail on valid .<name> or ..<name> or similar such constructs\n    let basename = path.basename(name);\n    if (basename === '..' || basename === '.' || basename !== name)\n      throw new Error(`${option} option must not contain a path, found \"${name}\".`);\n  }\n  else { // if (option === 'dir' || option === 'template') {\n    // assert that dir or template are relative to tmpDir\n    if (path.isAbsolute(name) && !name.startsWith(tmpDir)) {\n      throw new Error(`${option} option must be relative to \"${tmpDir}\", found \"${name}\".`);\n    }\n    let resolvedPath = _resolvePath(name, tmpDir);\n    if (!resolvedPath.startsWith(tmpDir))\n      throw new Error(`${option} option must be relative to \"${tmpDir}\", found \"${resolvedPath}\".`);\n  }\n}\n\n/**\n * Helper for testing against EBADF to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isEBADF(error) {\n  return _isExpectedError(error, -EBADF, 'EBADF');\n}\n\n/**\n * Helper for testing against ENOENT to compensate changes made to Node 7.x under Windows.\n *\n * @private\n */\nfunction _isENOENT(error) {\n  return _isExpectedError(error, -ENOENT, 'ENOENT');\n}\n\n/**\n * Helper to determine whether the expected error code matches the actual code and errno,\n * which will differ between the supported node versions.\n *\n * - Node >= 7.0:\n *   error.code {string}\n *   error.errno {number} any numerical value will be negated\n *\n * CAVEAT\n *\n * On windows, the errno for EBADF is -4083 but os.constants.errno.EBADF is different and we must assume that ENOENT\n * is no different here.\n *\n * @param {SystemError} error\n * @param {number} errno\n * @param {string} code\n * @private\n */\nfunction _isExpectedError(error, errno, code) {\n  return IS_WIN32 ? error.code === code : error.code === code && error.errno === errno;\n}\n\n/**\n * Sets the graceful cleanup.\n *\n * If graceful cleanup is set, tmp will remove all controlled temporary objects on process exit, otherwise the\n * temporary objects will remain in place, waiting to be cleaned up on system restart or otherwise scheduled temporary\n * object removals.\n */\nfunction setGracefulCleanup() {\n  _gracefulCleanup = true;\n}\n\n/**\n * Returns the currently configured tmp dir from os.tmpdir().\n *\n * @private\n * @param {?Options} options\n * @returns {string} the currently configured tmp dir\n */\nfunction _getTmpDir(options) {\n  return path.resolve(options && options.tmpdir || os.tmpdir());\n}\n\n// Install process exit listener\nprocess.addListener(EXIT, _garbageCollector);\n\n/**\n * Configuration options.\n *\n * @typedef {Object} Options\n * @property {?boolean} keep the temporary object (file or dir) will not be garbage collected\n * @property {?number} tries the number of tries before give up the name generation\n * @property (?int) mode the access mode, defaults are 0o700 for directories and 0o600 for files\n * @property {?string} template the \"mkstemp\" like filename template\n * @property {?string} name fixed name relative to tmpdir or the specified dir option\n * @property {?string} dir tmp directory relative to the root tmp directory in use\n * @property {?string} prefix prefix for the generated name\n * @property {?string} postfix postfix for the generated name\n * @property {?string} tmpdir the root tmp directory which overrides the os tmpdir\n * @property {?boolean} unsafeCleanup recursively removes the created temporary directory, even when it's not empty\n * @property {?boolean} detachDescriptor detaches the file descriptor, caller is responsible for closing the file, tmp will no longer try closing the file during garbage collection\n * @property {?boolean} discardDescriptor discards the file descriptor (closes file, fd is -1), tmp will no longer try closing the file during garbage collection\n */\n\n/**\n * @typedef {Object} FileSyncObject\n * @property {string} name the name of the file\n * @property {string} fd the file descriptor or -1 if the fd has been discarded\n * @property {fileCallback} removeCallback the callback function to remove the file\n */\n\n/**\n * @typedef {Object} DirSyncObject\n * @property {string} name the name of the directory\n * @property {fileCallback} removeCallback the callback function to remove the directory\n */\n\n/**\n * @callback tmpNameCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n */\n\n/**\n * @callback fileCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback fileCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {number} fd the file descriptor or -1 if the fd had been discarded\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallback\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallback} fn the cleanup callback function\n */\n\n/**\n * @callback dirCallbackSync\n * @param {?Error} err the error object if anything goes wrong\n * @param {string} name the temporary file name\n * @param {cleanupCallbackSync} fn the cleanup callback function\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallback\n * @param {simpleCallback} [next] function to call whenever the tmp object needs to be removed\n */\n\n/**\n * Removes the temporary created file or directory.\n *\n * @callback cleanupCallbackSync\n */\n\n/**\n * Callback function for function composition.\n * @see {@link https://github.com/raszi/node-tmp/issues/57|raszi/node-tmp#57}\n *\n * @callback simpleCallback\n */\n\n// exporting all the needed methods\n\n// evaluate _getTmpDir() lazily, mainly for simplifying testing but it also will\n// allow users to reconfigure the temporary directory\nObject.defineProperty(module.exports, \"tmpdir\", ({\n  enumerable: true,\n  configurable: false,\n  get: function () {\n    return _getTmpDir();\n  }\n}));\n\nmodule.exports.dir = dir;\nmodule.exports.dirSync = dirSync;\n\nmodule.exports.file = file;\nmodule.exports.fileSync = fileSync;\n\nmodule.exports.tmpName = tmpName;\nmodule.exports.tmpNameSync = tmpNameSync;\n\nmodule.exports.setGracefulCleanup = setGracefulCleanup;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tmp/lib/tmp.js\n");

/***/ })

};
;