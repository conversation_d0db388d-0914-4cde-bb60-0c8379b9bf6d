"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlchars";
exports.ids = ["vendor-chunks/xmlchars"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlchars/xml/1.0/ed5.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlchars/xml/1.0/ed5.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\n * Character classes and associated utilities for the 5th edition of XML 1.0.\n *\n * <AUTHOR> Dubeau\n * @license MIT\n * @copyright Louis-Dominique Dubeau\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//\n// Fragments.\n//\nexports.CHAR = \"\\t\\n\\r -\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\";\nexports.S = \" \\t\\r\\n\";\n// tslint:disable-next-line:max-line-length\nexports.NAME_START_CHAR = \":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\uD800\\uDC00-\\uDB7F\\uDFFF\";\nexports.NAME_CHAR = \"-\" + exports.NAME_START_CHAR + \".0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040\";\n//\n// Regular expressions.\n//\nexports.CHAR_RE = new RegExp(\"^[\" + exports.CHAR + \"]$\", \"u\");\nexports.S_RE = new RegExp(\"^[\" + exports.S + \"]+$\", \"u\");\nexports.NAME_START_CHAR_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"]$\", \"u\");\nexports.NAME_CHAR_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]$\", \"u\");\nexports.NAME_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"][\" + exports.NAME_CHAR + \"]*$\", \"u\");\nexports.NMTOKEN_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]+$\", \"u\");\nvar TAB = 9;\nvar NL = 0xA;\nvar CR = 0xD;\nvar SPACE = 0x20;\n//\n// Lists.\n//\n/** All characters in the ``S`` production. */\nexports.S_LIST = [SPACE, NL, CR, TAB];\n/**\n * Determines whether a codepoint matches the ``CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``CHAR``.\n */\nfunction isChar(c) {\n    return (c >= SPACE && c <= 0xD7FF) ||\n        c === NL || c === CR || c === TAB ||\n        (c >= 0xE000 && c <= 0xFFFD) ||\n        (c >= 0x10000 && c <= 0x10FFFF);\n}\nexports.isChar = isChar;\n/**\n * Determines whether a codepoint matches the ``S`` (space) production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``S``.\n */\nfunction isS(c) {\n    return c === SPACE || c === NL || c === CR || c === TAB;\n}\nexports.isS = isS;\n/**\n * Determines whether a codepoint matches the ``NAME_START_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_START_CHAR``.\n */\nfunction isNameStartChar(c) {\n    return ((c >= 0x41 && c <= 0x5A) ||\n        (c >= 0x61 && c <= 0x7A) ||\n        c === 0x3A ||\n        c === 0x5F ||\n        c === 0x200C ||\n        c === 0x200D ||\n        (c >= 0xC0 && c <= 0xD6) ||\n        (c >= 0xD8 && c <= 0xF6) ||\n        (c >= 0x00F8 && c <= 0x02FF) ||\n        (c >= 0x0370 && c <= 0x037D) ||\n        (c >= 0x037F && c <= 0x1FFF) ||\n        (c >= 0x2070 && c <= 0x218F) ||\n        (c >= 0x2C00 && c <= 0x2FEF) ||\n        (c >= 0x3001 && c <= 0xD7FF) ||\n        (c >= 0xF900 && c <= 0xFDCF) ||\n        (c >= 0xFDF0 && c <= 0xFFFD) ||\n        (c >= 0x10000 && c <= 0xEFFFF));\n}\nexports.isNameStartChar = isNameStartChar;\n/**\n * Determines whether a codepoint matches the ``NAME_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_CHAR``.\n */\nfunction isNameChar(c) {\n    return isNameStartChar(c) ||\n        (c >= 0x30 && c <= 0x39) ||\n        c === 0x2D ||\n        c === 0x2E ||\n        c === 0xB7 ||\n        (c >= 0x0300 && c <= 0x036F) ||\n        (c >= 0x203F && c <= 0x2040);\n}\nexports.isNameChar = isNameChar;\n//# sourceMappingURL=ed5.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlchars/xml/1.0/ed5.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlchars/xml/1.1/ed2.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlchars/xml/1.1/ed2.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\n * Character classes and associated utilities for the 2nd edition of XML 1.1.\n *\n * <AUTHOR> Dubeau\n * @license MIT\n * @copyright Louis-Dominique Dubeau\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//\n// Fragments.\n//\nexports.CHAR = \"\\u0001-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\";\nexports.RESTRICTED_CHAR = \"\\u0001-\\u0008\\u000B\\u000C\\u000E-\\u001F\\u007F-\\u0084\\u0086-\\u009F\";\nexports.S = \" \\t\\r\\n\";\n// tslint:disable-next-line:max-line-length\nexports.NAME_START_CHAR = \":A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\uD800\\uDC00-\\uDB7F\\uDFFF\";\nexports.NAME_CHAR = \"-\" + exports.NAME_START_CHAR + \".0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040\";\n//\n// Regular expressions.\n//\nexports.CHAR_RE = new RegExp(\"^[\" + exports.CHAR + \"]$\", \"u\");\nexports.RESTRICTED_CHAR_RE = new RegExp(\"^[\" + exports.RESTRICTED_CHAR + \"]$\", \"u\");\nexports.S_RE = new RegExp(\"^[\" + exports.S + \"]+$\", \"u\");\nexports.NAME_START_CHAR_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"]$\", \"u\");\nexports.NAME_CHAR_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]$\", \"u\");\nexports.NAME_RE = new RegExp(\"^[\" + exports.NAME_START_CHAR + \"][\" + exports.NAME_CHAR + \"]*$\", \"u\");\nexports.NMTOKEN_RE = new RegExp(\"^[\" + exports.NAME_CHAR + \"]+$\", \"u\");\nvar TAB = 9;\nvar NL = 0xA;\nvar CR = 0xD;\nvar SPACE = 0x20;\n//\n// Lists.\n//\n/** All characters in the ``S`` production. */\nexports.S_LIST = [SPACE, NL, CR, TAB];\n/**\n * Determines whether a codepoint matches the ``CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``CHAR``.\n */\nfunction isChar(c) {\n    return (c >= 0x0001 && c <= 0xD7FF) ||\n        (c >= 0xE000 && c <= 0xFFFD) ||\n        (c >= 0x10000 && c <= 0x10FFFF);\n}\nexports.isChar = isChar;\n/**\n * Determines whether a codepoint matches the ``RESTRICTED_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``RESTRICTED_CHAR``.\n */\nfunction isRestrictedChar(c) {\n    return (c >= 0x1 && c <= 0x8) ||\n        c === 0xB ||\n        c === 0xC ||\n        (c >= 0xE && c <= 0x1F) ||\n        (c >= 0x7F && c <= 0x84) ||\n        (c >= 0x86 && c <= 0x9F);\n}\nexports.isRestrictedChar = isRestrictedChar;\n/**\n * Determines whether a codepoint matches the ``CHAR`` production and does not\n * match the ``RESTRICTED_CHAR`` production. ``isCharAndNotRestricted(x)`` is\n * equivalent to ``isChar(x) && !isRestrictedChar(x)``. This function is faster\n * than running the two-call equivalent.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``CHAR`` and does not match\n * ``RESTRICTED_CHAR``.\n */\nfunction isCharAndNotRestricted(c) {\n    return (c === 0x9) ||\n        (c === 0xA) ||\n        (c === 0xD) ||\n        (c > 0x1F && c < 0x7F) ||\n        (c === 0x85) ||\n        (c > 0x9F && c <= 0xD7FF) ||\n        (c >= 0xE000 && c <= 0xFFFD) ||\n        (c >= 0x10000 && c <= 0x10FFFF);\n}\nexports.isCharAndNotRestricted = isCharAndNotRestricted;\n/**\n * Determines whether a codepoint matches the ``S`` (space) production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``S``.\n */\nfunction isS(c) {\n    return c === SPACE || c === NL || c === CR || c === TAB;\n}\nexports.isS = isS;\n/**\n * Determines whether a codepoint matches the ``NAME_START_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_START_CHAR``.\n */\n// tslint:disable-next-line:cyclomatic-complexity\nfunction isNameStartChar(c) {\n    return ((c >= 0x41 && c <= 0x5A) ||\n        (c >= 0x61 && c <= 0x7A) ||\n        c === 0x3A ||\n        c === 0x5F ||\n        c === 0x200C ||\n        c === 0x200D ||\n        (c >= 0xC0 && c <= 0xD6) ||\n        (c >= 0xD8 && c <= 0xF6) ||\n        (c >= 0x00F8 && c <= 0x02FF) ||\n        (c >= 0x0370 && c <= 0x037D) ||\n        (c >= 0x037F && c <= 0x1FFF) ||\n        (c >= 0x2070 && c <= 0x218F) ||\n        (c >= 0x2C00 && c <= 0x2FEF) ||\n        (c >= 0x3001 && c <= 0xD7FF) ||\n        (c >= 0xF900 && c <= 0xFDCF) ||\n        (c >= 0xFDF0 && c <= 0xFFFD) ||\n        (c >= 0x10000 && c <= 0xEFFFF));\n}\nexports.isNameStartChar = isNameStartChar;\n/**\n * Determines whether a codepoint matches the ``NAME_CHAR`` production.\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches ``NAME_CHAR``.\n */\nfunction isNameChar(c) {\n    return isNameStartChar(c) ||\n        (c >= 0x30 && c <= 0x39) ||\n        c === 0x2D ||\n        c === 0x2E ||\n        c === 0xB7 ||\n        (c >= 0x0300 && c <= 0x036F) ||\n        (c >= 0x203F && c <= 0x2040);\n}\nexports.isNameChar = isNameChar;\n//# sourceMappingURL=ed2.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlchars/xml/1.1/ed2.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/xmlchars/xmlns/1.0/ed3.js":
/*!************************************************!*\
  !*** ./node_modules/xmlchars/xmlns/1.0/ed3.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n/**\n * Character class utilities for XML NS 1.0 edition 3.\n *\n * <AUTHOR> Dubeau\n * @license MIT\n * @copyright Louis-Dominique Dubeau\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//\n// Fragments.\n//\n// tslint:disable-next-line:max-line-length\nexports.NC_NAME_START_CHAR = \"A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\uD800\\uDC00-\\uDB7F\\uDFFF\";\nexports.NC_NAME_CHAR = \"-\" + exports.NC_NAME_START_CHAR + \".0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040\";\n//\n// Regular expressions.\n//\nexports.NC_NAME_START_CHAR_RE = new RegExp(\"^[\" + exports.NC_NAME_START_CHAR + \"]$\", \"u\");\nexports.NC_NAME_CHAR_RE = new RegExp(\"^[\" + exports.NC_NAME_CHAR + \"]$\", \"u\");\nexports.NC_NAME_RE = new RegExp(\"^[\" + exports.NC_NAME_START_CHAR + \"][\" + exports.NC_NAME_CHAR + \"]*$\", \"u\");\n/**\n * Determines whether a codepoint matches [[NC_NAME_START_CHAR]].\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches.\n */\n// tslint:disable-next-line:cyclomatic-complexity\nfunction isNCNameStartChar(c) {\n    return ((c >= 0x41 && c <= 0x5A) ||\n        c === 0x5F ||\n        (c >= 0x61 && c <= 0x7A) ||\n        (c >= 0xC0 && c <= 0xD6) ||\n        (c >= 0xD8 && c <= 0xF6) ||\n        (c >= 0x00F8 && c <= 0x02FF) ||\n        (c >= 0x0370 && c <= 0x037D) ||\n        (c >= 0x037F && c <= 0x1FFF) ||\n        (c >= 0x200C && c <= 0x200D) ||\n        (c >= 0x2070 && c <= 0x218F) ||\n        (c >= 0x2C00 && c <= 0x2FEF) ||\n        (c >= 0x3001 && c <= 0xD7FF) ||\n        (c >= 0xF900 && c <= 0xFDCF) ||\n        (c >= 0xFDF0 && c <= 0xFFFD) ||\n        (c >= 0x10000 && c <= 0xEFFFF));\n}\nexports.isNCNameStartChar = isNCNameStartChar;\n/**\n * Determines whether a codepoint matches [[NC_NAME_CHAR]].\n *\n * @param c The code point.\n *\n * @returns ``true`` if the codepoint matches.\n */\nfunction isNCNameChar(c) {\n    return isNCNameStartChar(c) ||\n        (c === 0x2D ||\n            c === 0x2E ||\n            (c >= 0x30 && c <= 0x39) ||\n            c === 0x00B7 ||\n            (c >= 0x0300 && c <= 0x036F) ||\n            (c >= 0x203F && c <= 0x2040));\n}\nexports.isNCNameChar = isNCNameChar;\n//# sourceMappingURL=ed3.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlchars/xmlns/1.0/ed3.js\n");

/***/ })

};
;