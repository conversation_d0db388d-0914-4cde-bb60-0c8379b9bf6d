/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/media/route";
exports.ids = ["app/api/media/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedia%2Froute&page=%2Fapi%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedia%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedia%2Froute&page=%2Fapi%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedia%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_media_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/media/route.ts */ \"(rsc)/./src/app/api/media/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/media/route\",\n        pathname: \"/api/media\",\n        filename: \"route\",\n        bundlePath: \"app/api/media/route\"\n    },\n    resolvedPagePath: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\media\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_media_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedia%2Froute&page=%2Fapi%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedia%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/media/route.ts":
/*!************************************!*\
  !*** ./src/app/api/media/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _shared_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../shared-data */ \"(rsc)/./src/app/api/shared-data.ts\");\n\n// استيراد البيانات المشتركة\n\n// دالة للتحقق من الصلاحيات\nfunction checkPermission(token, requiredPermission) {\n    if (!token) {\n        console.log('No token provided');\n        return false;\n    }\n    try {\n        console.log(`Checking permission: ${requiredPermission} for token: ${token}`);\n        // استخراج معرف المستخدم من التوكن (مؤقت)\n        const parts = token.split('_');\n        console.log('Token parts:', parts);\n        if (parts.length < 3) {\n            console.log('Invalid token format');\n            return true; // مؤقتاً: السماح بالوصول إذا كان التوكن غير صالح (للتطوير فقط)\n        }\n        const userId = parts[1];\n        const role = parts[2]; // افتراضي: نضيف الدور في التوكن\n        console.log(`User ID: ${userId}, Role: ${role}`);\n        // للتبسيط، سنفترض أن المستخدمين لديهم الصلاحيات التالية:\n        // ADMIN: جميع الصلاحيات\n        // MEDIA_MANAGER, DATA_ENTRY: MEDIA_CREATE, MEDIA_READ, MEDIA_UPDATE, MEDIA_DELETE\n        // SCHEDULER, MAP_SCHEDULER, FULL_VIEWER, VIEWER: MEDIA_READ فقط\n        // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true;\n    /*\r\n    if (role === 'ADMIN') {\r\n      console.log('User is ADMIN, granting all permissions');\r\n      return true;\r\n    }\r\n    \r\n    if (requiredPermission === 'MEDIA_READ') {\r\n      const hasPermission = ['MEDIA_MANAGER', 'DATA_ENTRY', 'SCHEDULER', 'MAP_SCHEDULER', 'FULL_VIEWER', 'VIEWER'].includes(role);\r\n      console.log(`MEDIA_READ permission check: ${hasPermission}`);\r\n      return hasPermission;\r\n    }\r\n    \r\n    if (['MEDIA_CREATE', 'MEDIA_UPDATE', 'MEDIA_DELETE'].includes(requiredPermission)) {\r\n      const hasPermission = ['MEDIA_MANAGER', 'DATA_ENTRY'].includes(role);\r\n      console.log(`${requiredPermission} permission check: ${hasPermission}`);\r\n      return hasPermission;\r\n    }\r\n    \r\n    console.log(`No matching permission rule for: ${requiredPermission}`);\r\n    return false;\r\n    */ } catch (error) {\n        console.error('Error checking permission:', error);\n        return true; // مؤقتاً: السماح بالوصول في حالة حدوث خطأ (للتطوير فقط)\n    }\n}\n// GET - جلب جميع المواد الإعلامية أو مادة واحدة\nasync function GET(request) {\n    try {\n        // التحقق من الصلاحيات (للقراءة)\n        const token = request.headers.get('Authorization')?.replace('Bearer ', '');\n        // جميع المستخدمين يمكنهم قراءة المواد\n        // لكن يمكنك إضافة تحقق إضافي هنا إذا كنت تريد تقييد القراءة لبعض المستخدمين\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (id) {\n            // جلب مادة واحدة\n            const mediaItem = (0,_shared_data__WEBPACK_IMPORTED_MODULE_1__.getMediaItemById)(id);\n            if (!mediaItem) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: 'المادة غير موجودة'\n                }, {\n                    status: 404\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: mediaItem\n            });\n        } else {\n            // جلب جميع المواد\n            const mediaItems = (0,_shared_data__WEBPACK_IMPORTED_MODULE_1__.getAllMediaItems)();\n            // تشخيص أول مادة لها سيجمانتات\n            const itemWithSegments = mediaItems.find((item)=>item.segments && item.segments.length > 0);\n            if (itemWithSegments) {\n                console.log(`🔥 تشخيص مادة: ${itemWithSegments.name}`);\n                console.log(`🔥 السيجمانتات:`, itemWithSegments.segments.map((s)=>({\n                        segmentNumber: s.segmentNumber,\n                        code: s.code,\n                        timeIn: s.timeIn\n                    })));\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: mediaItems\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching media items:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب المواد الإعلامية'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مادة إعلامية جديدة\nasync function POST(request) {\n    try {\n        // التحقق من الصلاحيات (للإضافة)\n        const token = request.headers.get('Authorization')?.replace('Bearer ', '');\n        // التحقق من صلاحية إضافة المواد\n        console.log('Checking MEDIA_CREATE permission for token:', token);\n        if (!checkPermission(token, 'MEDIA_CREATE')) {\n            console.log('Permission check failed for MEDIA_CREATE');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'ليس لديك صلاحية إضافة المواد الإعلامية'\n            }, {\n                status: 403\n            });\n        }\n        console.log('Permission check passed for MEDIA_CREATE');\n        const body = await request.json();\n        const { formData, segments } = body;\n        console.log('🔥 POST - البيانات المستلمة:');\n        console.log('🔥 formData:', formData);\n        console.log('🔥 segments:', segments);\n        console.log('🔥 أول سيجمانت:', segments[0]);\n        // التحقق من البيانات المطلوبة\n        if (!formData.name || !formData.type) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المادة ونوعها مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء المادة الإعلامية مع السيجمانت (مؤقت: في الذاكرة)\n        const mediaItem = {\n            id: `media_${Date.now()}`,\n            name: formData.name,\n            type: formData.type,\n            description: formData.description || null,\n            channel: formData.channel,\n            source: formData.source || null,\n            status: formData.status,\n            startDate: formData.startDate || new Date().toISOString(),\n            endDate: formData.endDate || null,\n            notes: formData.notes || null,\n            episodeNumber: formData.episodeNumber ? parseInt(formData.episodeNumber) : null,\n            seasonNumber: formData.seasonNumber ? parseInt(formData.seasonNumber) : null,\n            partNumber: formData.partNumber ? parseInt(formData.partNumber) : null,\n            hardDiskNumber: formData.hardDiskNumber || '',\n            showInTX: formData.showInTX === true,\n            segments: segments.map((segment, index)=>{\n                console.log(`🔥 معالجة السيجمانت ${index + 1}:`, segment);\n                const segmentCode = segment.segmentCode || segment.code || '';\n                console.log(`🔥 كود السيجمانت النهائي: \"${segmentCode}\"`);\n                return {\n                    id: `seg_${Date.now()}_${index}_${segment.id}`,\n                    segmentNumber: segment.id,\n                    timeIn: segment.timeIn,\n                    timeOut: segment.timeOut,\n                    duration: segment.duration,\n                    code: segmentCode\n                };\n            }),\n            createdAt: new Date().toISOString()\n        };\n        // حفظ في الذاكرة مؤقت\n        (0,_shared_data__WEBPACK_IMPORTED_MODULE_1__.addMediaItem)(mediaItem);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: mediaItem,\n            message: 'تم حفظ المادة الإعلامية بنجاح'\n        });\n    } catch (error) {\n        console.error('Error creating media item:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حفظ المادة الإعلامية'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث مادة إعلامية\nasync function PUT(request) {\n    try {\n        // التحقق من الصلاحيات (للتعديل)\n        const token = request.headers.get('Authorization')?.replace('Bearer ', '');\n        console.log('Received token:', token);\n        // التحقق من صلاحية تعديل المواد\n        if (!checkPermission(token, 'MEDIA_UPDATE')) {\n            console.log('Permission check failed for MEDIA_UPDATE');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'ليس لديك صلاحية تعديل المواد الإعلامية'\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المادة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const body = await request.json();\n        console.log('PUT request body:', body);\n        const { formData, segments } = body;\n        console.log('Extracted formData:', formData);\n        console.log('Extracted segments:', segments);\n        // التحقق من البيانات المطلوبة\n        if (!formData || !formData.name || !formData.type) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المادة ونوعها مطلوبان'\n            }, {\n                status: 400\n            });\n        }\n        // تحديث المادة الإعلامية\n        const updatedMediaItem = {\n            id,\n            name: formData.name,\n            type: formData.type,\n            description: formData.description || null,\n            channel: formData.channel,\n            source: formData.source || null,\n            status: formData.status,\n            startDate: formData.startDate || new Date().toISOString(),\n            endDate: formData.endDate || null,\n            notes: formData.notes || null,\n            episodeNumber: formData.episodeNumber ? parseInt(formData.episodeNumber) : null,\n            seasonNumber: formData.seasonNumber ? parseInt(formData.seasonNumber) : null,\n            partNumber: formData.partNumber ? parseInt(formData.partNumber) : null,\n            hardDiskNumber: formData.hardDiskNumber || '',\n            showInTX: formData.showInTX === true,\n            segments: segments.map((segment, index)=>({\n                    id: `seg_${Date.now()}_${index}_${segment.id}`,\n                    segmentNumber: segment.id,\n                    timeIn: segment.timeIn,\n                    timeOut: segment.timeOut,\n                    duration: segment.duration,\n                    code: segment.segmentCode || segment.code || null\n                })),\n            updatedAt: new Date().toISOString()\n        };\n        // تحديث في الذاكرة\n        const success = (0,_shared_data__WEBPACK_IMPORTED_MODULE_1__.updateMediaItem)(id, updatedMediaItem);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المادة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: updatedMediaItem,\n            message: 'تم تحديث المادة الإعلامية بنجاح'\n        });\n    } catch (error) {\n        console.error('Error updating media item:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في تحديث المادة الإعلامية'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مادة إعلامية\nasync function DELETE(request) {\n    try {\n        // التحقق من الصلاحيات (للحذف)\n        const token = request.headers.get('Authorization')?.replace('Bearer ', '');\n        // التحقق من صلاحية حذف المواد\n        console.log('Checking MEDIA_DELETE permission for token:', token);\n        if (!checkPermission(token, 'MEDIA_DELETE')) {\n            console.log('Permission check failed for MEDIA_DELETE');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'ليس لديك صلاحية حذف المواد الإعلامية'\n            }, {\n                status: 403\n            });\n        }\n        console.log('Permission check passed for MEDIA_DELETE');\n        const { searchParams } = new URL(request.url);\n        const id = searchParams.get('id');\n        if (!id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المادة مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // حذف من الذاكرة مؤقت\n        const success = (0,_shared_data__WEBPACK_IMPORTED_MODULE_1__.removeMediaItem)(id);\n        if (!success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المادة غير موجودة'\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف المادة الإعلامية بنجاح'\n        });\n    } catch (error) {\n        console.error('Error deleting media item:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حذف المادة الإعلامية'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/media/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/shared-data.ts":
/*!************************************!*\
  !*** ./src/app/api/shared-data.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMediaItem: () => (/* binding */ addMediaItem),\n/* harmony export */   clearAllMediaItems: () => (/* binding */ clearAllMediaItems),\n/* harmony export */   getAllMediaItems: () => (/* binding */ getAllMediaItems),\n/* harmony export */   getMediaItemById: () => (/* binding */ getMediaItemById),\n/* harmony export */   removeMediaItem: () => (/* binding */ removeMediaItem),\n/* harmony export */   saveImportedData: () => (/* binding */ saveImportedData),\n/* harmony export */   updateMediaItem: () => (/* binding */ updateMediaItem)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// ملف مشترك للبيانات - يضمن التزامن بين جميع APIs\n\n\n// مسار ملف البيانات المؤقت\nconst DATA_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'temp-data.json');\n// قاعدة بيانات مشتركة للمواد الإعلامية\nlet mediaItems = [];\n// تحميل البيانات من الملف عند بدء التشغيل\nfunction loadData() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(DATA_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(DATA_FILE, 'utf8');\n            mediaItems = JSON.parse(data);\n            console.log(`📂 تم تحميل ${mediaItems.length} مادة من الملف المؤقت`);\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل البيانات:', error);\n        mediaItems = [];\n    }\n}\n// حفظ البيانات في الملف\nfunction saveData() {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(DATA_FILE, JSON.stringify(mediaItems, null, 2));\n        console.log(`💾 تم حفظ ${mediaItems.length} مادة في الملف المؤقت`);\n    } catch (error) {\n        console.error('❌ خطأ في حفظ البيانات:', error);\n    }\n}\n// تحميل البيانات عند استيراد الملف\nloadData();\n// دالة لإضافة مادة جديدة\nfunction addMediaItem(item) {\n    mediaItems.push(item);\n    saveData(); // حفظ فوري\n    console.log(`✅ تم إضافة مادة جديدة: ${item.name} (المجموع: ${mediaItems.length})`);\n}\n// دالة لحذف مادة\nfunction removeMediaItem(id) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        const removed = mediaItems.splice(index, 1)[0];\n        saveData(); // حفظ فوري\n        console.log(`🗑️ تم حذف المادة: ${removed.name} (المجموع: ${mediaItems.length})`);\n        return true;\n    }\n    return false;\n}\n// دالة للحصول على جميع المواد\nfunction getAllMediaItems() {\n    return mediaItems;\n}\n// دالة للحصول على مادة بالمعرف\nfunction getMediaItemById(id) {\n    return mediaItems.find((item)=>item.id === id);\n}\n// دالة لتحديث مادة\nfunction updateMediaItem(id, updatedItem) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        mediaItems[index] = {\n            ...mediaItems[index],\n            ...updatedItem\n        };\n        saveData(); // حفظ فوري\n        console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);\n        return true;\n    }\n    return false;\n}\n// دالة لحذف جميع المواد\nfunction clearAllMediaItems() {\n    const count = mediaItems.length;\n    mediaItems = [];\n    saveData(); // حفظ فوري\n    console.log(`🗑️ تم حذف جميع المواد (${count} مادة)`);\n    return count;\n}\n// دالة لحفظ البيانات المستوردة\nfunction saveImportedData(items) {\n    mediaItems = items;\n    saveData(); // حفظ فوري\n    console.log(`📥 تم حفظ ${items.length} مادة مستوردة`);\n    return items.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shared-data.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedia%2Froute&page=%2Fapi%2Fmedia%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedia%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();