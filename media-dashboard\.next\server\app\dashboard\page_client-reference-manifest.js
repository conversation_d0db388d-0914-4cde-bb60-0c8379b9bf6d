globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(ssr)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin-dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/admin-dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/daily-schedule/page.tsx":{"*":{"id":"(ssr)/./src/app/daily-schedule/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/daily-schedule/import/page.tsx":{"*":{"id":"(ssr)/./src/app/daily-schedule/import/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/reports/page.tsx":{"*":{"id":"(ssr)/./src/app/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/unified-system/page.tsx":{"*":{"id":"(ssr)/./src/app/unified-system/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/statistics/page.tsx":{"*":{"id":"(ssr)/./src/app/statistics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/weekly-schedule/page.tsx":{"*":{"id":"(ssr)/./src/app/weekly-schedule/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/add-media/page.tsx":{"*":{"id":"(ssr)/./src/app/add-media/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/media-list/page.tsx":{"*":{"id":"(ssr)/./src/app/media-list/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\layout.tsx":{"id":"(app-pages-browser)/./src/app/layout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\admin-dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin-dashboard/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\daily-schedule\\page.tsx":{"id":"(app-pages-browser)/./src/app/daily-schedule/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\daily-schedule\\import\\page.tsx":{"id":"(app-pages-browser)/./src/app/daily-schedule/import/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\reports\\page.tsx":{"id":"(app-pages-browser)/./src/app/reports/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\unified-system\\page.tsx":{"id":"(app-pages-browser)/./src/app/unified-system/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\statistics\\page.tsx":{"id":"(app-pages-browser)/./src/app/statistics/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\weekly-schedule\\page.tsx":{"id":"(app-pages-browser)/./src/app/weekly-schedule/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\add-media\\page.tsx":{"id":"(app-pages-browser)/./src/app/add-media/page.tsx","name":"*","chunks":[],"async":false},"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\media-list\\page.tsx":{"id":"(app-pages-browser)/./src/app/media-list/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\":[],"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\page":[],"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\dashboard\\page":[{"inlined":false,"path":"static/css/app/dashboard/page.css"}]},"rscModuleMapping":{"(app-pages-browser)/./src/app/layout.tsx":{"*":{"id":"(rsc)/./src/app/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin-dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/admin-dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/daily-schedule/page.tsx":{"*":{"id":"(rsc)/./src/app/daily-schedule/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/daily-schedule/import/page.tsx":{"*":{"id":"(rsc)/./src/app/daily-schedule/import/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/reports/page.tsx":{"*":{"id":"(rsc)/./src/app/reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/unified-system/page.tsx":{"*":{"id":"(rsc)/./src/app/unified-system/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/statistics/page.tsx":{"*":{"id":"(rsc)/./src/app/statistics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/weekly-schedule/page.tsx":{"*":{"id":"(rsc)/./src/app/weekly-schedule/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/add-media/page.tsx":{"*":{"id":"(rsc)/./src/app/add-media/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/media-list/page.tsx":{"*":{"id":"(rsc)/./src/app/media-list/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}