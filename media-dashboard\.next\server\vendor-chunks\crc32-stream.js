"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/crc32-stream";
exports.ids = ["vendor-chunks/crc32-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/crc32-stream/lib/crc32-stream.js":
/*!*******************************************************!*\
  !*** ./node_modules/crc32-stream/lib/crc32-stream.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n \n\nconst {Transform} = __webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\");\n\nconst crc32 = __webpack_require__(/*! crc-32 */ \"(ssr)/./node_modules/crc-32/crc32.js\");\n\nclass CRC32Stream extends Transform {\n  constructor(options) {\n    super(options);\n    this.checksum = Buffer.allocUnsafe(4);\n    this.checksum.writeInt32BE(0, 0);\n\n    this.rawSize = 0;\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk) {\n      this.checksum = crc32.buf(chunk, this.checksum) >>> 0;\n      this.rawSize += chunk.length;\n    }\n\n    callback(null, chunk);\n  }\n\n  digest(encoding) {\n    const checksum = Buffer.allocUnsafe(4);\n    checksum.writeUInt32BE(this.checksum >>> 0, 0);\n    return encoding ? checksum.toString(encoding) : checksum;\n  }\n\n  hex() {\n    return this.digest('hex').toUpperCase();\n  }\n\n  size() {\n    return this.rawSize;\n  }\n}\n\nmodule.exports = CRC32Stream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/crc32-stream/lib/crc32-stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js":
/*!***************************************************************!*\
  !*** ./node_modules/crc32-stream/lib/deflate-crc32-stream.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n\n\nconst {DeflateRaw} = __webpack_require__(/*! zlib */ \"zlib\");\n\nconst crc32 = __webpack_require__(/*! crc-32 */ \"(ssr)/./node_modules/crc-32/crc32.js\");\n\nclass DeflateCRC32Stream extends DeflateRaw {\n  constructor(options) {\n    super(options);\n\n    this.checksum = Buffer.allocUnsafe(4);\n    this.checksum.writeInt32BE(0, 0);\n\n    this.rawSize = 0;\n    this.compressedSize = 0;\n  }\n\n  push(chunk, encoding) {\n    if (chunk) {\n      this.compressedSize += chunk.length;\n    }\n\n    return super.push(chunk, encoding);\n  }\n\n  _transform(chunk, encoding, callback) {\n    if (chunk) {\n      this.checksum = crc32.buf(chunk, this.checksum) >>> 0;\n      this.rawSize += chunk.length;\n    }\n\n    super._transform(chunk, encoding, callback)\n  }\n\n  digest(encoding) {\n    const checksum = Buffer.allocUnsafe(4);\n    checksum.writeUInt32BE(this.checksum >>> 0, 0);\n    return encoding ? checksum.toString(encoding) : checksum;\n  }\n\n  hex() {\n    return this.digest('hex').toUpperCase();\n  }\n\n  size(compressed = false) {\n    if (compressed) {\n      return this.compressedSize;\n    } else {\n      return this.rawSize;\n    }\n  }\n}\n\nmodule.exports = DeflateCRC32Stream;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/crc32-stream/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/crc32-stream/lib/index.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * node-crc32-stream\n *\n * Copyright (c) 2014 Chris Talkington, contributors.\n * Licensed under the MIT license.\n * https://github.com/archiverjs/node-crc32-stream/blob/master/LICENSE-MIT\n */\n\n\n\nmodule.exports = {\n  CRC32Stream: __webpack_require__(/*! ./crc32-stream */ \"(ssr)/./node_modules/crc32-stream/lib/crc32-stream.js\"),\n  DeflateCRC32Stream: __webpack_require__(/*! ./deflate-crc32-stream */ \"(ssr)/./node_modules/crc32-stream/lib/deflate-crc32-stream.js\")\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3JjMzItc3RyZWFtL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFYTs7QUFFYjtBQUNBLGVBQWUsbUJBQU8sQ0FBQyw2RUFBZ0I7QUFDdkMsc0JBQXNCLG1CQUFPLENBQUMsNkZBQXdCO0FBQ3REIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcY3JjMzItc3RyZWFtXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbm9kZS1jcmMzMi1zdHJlYW1cbiAqXG4gKiBDb3B5cmlnaHQgKGMpIDIwMTQgQ2hyaXMgVGFsa2luZ3RvbiwgY29udHJpYnV0b3JzLlxuICogTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlLlxuICogaHR0cHM6Ly9naXRodWIuY29tL2FyY2hpdmVyanMvbm9kZS1jcmMzMi1zdHJlYW0vYmxvYi9tYXN0ZXIvTElDRU5TRS1NSVRcbiAqL1xuXG4ndXNlIHN0cmljdCc7XG5cbm1vZHVsZS5leHBvcnRzID0ge1xuICBDUkMzMlN0cmVhbTogcmVxdWlyZSgnLi9jcmMzMi1zdHJlYW0nKSxcbiAgRGVmbGF0ZUNSQzMyU3RyZWFtOiByZXF1aXJlKCcuL2RlZmxhdGUtY3JjMzItc3RyZWFtJylcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/crc32-stream/lib/index.js\n");

/***/ })

};
;