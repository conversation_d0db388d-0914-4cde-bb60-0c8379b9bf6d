/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/route";
exports.ids = ["app/api/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/users/route.ts */ \"(rsc)/./src/app/api/users/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/route\",\n        pathname: \"/api/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/route\"\n    },\n    resolvedPagePath: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/users/route.ts":
/*!************************************!*\
  !*** ./src/app/api/users/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT),\n/* harmony export */   ROLES: () => (/* binding */ ROLES)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// مسار ملف بيانات المستخدمين\nconst USERS_FILE = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'users-data.json');\n// تعريف الأدوار والصلاحيات\nconst ROLES = {\n    ADMIN: {\n        name: 'ADMIN',\n        permissions: [\n            'ALL',\n            'USER_CREATE',\n            'USER_READ',\n            'USER_UPDATE',\n            'USER_DELETE',\n            'MEDIA_CREATE',\n            'MEDIA_READ',\n            'MEDIA_UPDATE',\n            'MEDIA_DELETE',\n            'SCHEDULE_CREATE',\n            'SCHEDULE_READ',\n            'SCHEDULE_UPDATE',\n            'SCHEDULE_DELETE',\n            'MAP_CREATE',\n            'MAP_READ',\n            'MAP_UPDATE',\n            'MAP_DELETE',\n            'BROADCAST_CREATE',\n            'BROADCAST_READ',\n            'BROADCAST_UPDATE',\n            'BROADCAST_DELETE' // إدارة البث\n        ],\n        description: 'ADMIN_DESC'\n    },\n    CONTENT_MANAGER: {\n        name: 'CONTENT_MANAGER',\n        permissions: [\n            'MEDIA_CREATE',\n            'MEDIA_READ',\n            'MEDIA_UPDATE',\n            'MEDIA_DELETE',\n            'SCHEDULE_CREATE',\n            'SCHEDULE_READ',\n            'SCHEDULE_UPDATE',\n            'SCHEDULE_DELETE' // إدارة الجداول\n        ],\n        description: 'CONTENT_MANAGER_DESC'\n    },\n    MEDIA_MANAGER: {\n        name: 'MEDIA_MANAGER',\n        permissions: [\n            'MEDIA_CREATE',\n            'MEDIA_READ',\n            'MEDIA_UPDATE',\n            'MEDIA_DELETE',\n            'SCHEDULE_READ' // عرض الجداول فقط\n        ],\n        description: 'MEDIA_MANAGER_DESC'\n    },\n    SCHEDULER: {\n        name: 'SCHEDULER',\n        permissions: [\n            'SCHEDULE_CREATE',\n            'SCHEDULE_READ',\n            'SCHEDULE_UPDATE',\n            'SCHEDULE_DELETE',\n            'MEDIA_READ' // عرض المواد فقط\n        ],\n        description: 'SCHEDULER_DESC'\n    },\n    FULL_VIEWER: {\n        name: 'FULL_VIEWER',\n        permissions: [\n            'MEDIA_READ',\n            'SCHEDULE_READ',\n            'MAP_READ',\n            'BROADCAST_READ',\n            'REPORT_READ',\n            'DASHBOARD_READ' // عرض لوحة التحكم فقط\n        ],\n        description: 'FULL_VIEWER_DESC'\n    },\n    DATA_ENTRY: {\n        name: 'DATA_ENTRY',\n        permissions: [\n            'MEDIA_CREATE',\n            'MEDIA_READ',\n            'MEDIA_UPDATE',\n            'MEDIA_DELETE' // إدارة المواد فقط\n        ],\n        description: 'DATA_ENTRY_DESC'\n    },\n    MAP_SCHEDULER: {\n        name: 'MAP_SCHEDULER',\n        permissions: [\n            'MAP_CREATE',\n            'MAP_READ',\n            'MAP_UPDATE',\n            'MAP_DELETE',\n            'SCHEDULE_CREATE',\n            'SCHEDULE_READ',\n            'SCHEDULE_UPDATE',\n            'SCHEDULE_DELETE',\n            'MEDIA_READ' // عرض المواد فقط (بدون تعديل)\n        ],\n        description: 'MAP_SCHEDULER_DESC'\n    },\n    VIEWER: {\n        name: 'VIEWER',\n        permissions: [\n            'MEDIA_READ',\n            'SCHEDULE_READ' // عرض الجداول فقط\n        ],\n        description: 'VIEWER_DESC'\n    }\n};\n// دالة لتحميل المستخدمين من الملف\nfunction loadUsers() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(USERS_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(USERS_FILE, 'utf8');\n            const users = JSON.parse(data);\n            return users;\n        } else {\n            return [];\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل المستخدمين:', error);\n        return [];\n    }\n}\n// دالة لحفظ المستخدمين في الملف\nfunction saveUsers(users) {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));\n        console.log(`💾 تم حفظ ${users.length} مستخدم في الملف`);\n    } catch (error) {\n        console.error('❌ خطأ في حفظ المستخدمين:', error);\n    }\n}\n// GET - الحصول على قائمة المستخدمين\nasync function GET(request) {\n    try {\n        const url = new URL(request.url);\n        const role = url.searchParams.get('role');\n        let filteredUsers = loadUsers();\n        if (role) {\n            filteredUsers = filteredUsers.filter((user)=>user.role === role);\n        }\n        // إزالة كلمات المرور من النتائج\n        const usersWithoutPasswords = filteredUsers.map((user)=>{\n            const { password, ...userWithoutPassword } = user;\n            return {\n                ...userWithoutPassword,\n                roleInfo: ROLES[user.role]\n            };\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            users: usersWithoutPasswords,\n            roles: ROLES,\n            totalUsers: filteredUsers.length\n        });\n    } catch (error) {\n        console.error('Get users error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في جلب بيانات المستخدمين'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - إضافة مستخدم جديد\nasync function POST(request) {\n    try {\n        const userData = await request.json();\n        const { username, password, name, email, role, phone } = userData;\n        // التحقق من البيانات المطلوبة\n        if (!username || !password || !name || !role) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'جميع الحقول مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل المستخدمين الحاليين\n        const users = loadUsers();\n        // التحقق من عدم وجود اسم المستخدم مسبقاً\n        if (users.find((user)=>user.username === username)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'اسم المستخدم موجود مسبقاً'\n            }, {\n                status: 400\n            });\n        }\n        // التحقق من صحة الدور\n        if (!ROLES[role]) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'دور المستخدم غير صحيح'\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء المستخدم الجديد\n        const newUser = {\n            id: Date.now().toString(),\n            username,\n            password,\n            name,\n            email: email || '',\n            phone: phone || '',\n            role,\n            isActive: true,\n            createdAt: new Date().toISOString(),\n            lastLogin: null\n        };\n        // إضافة المستخدم للقائمة وحفظها\n        users.push(newUser);\n        saveUsers(users);\n        // إرجاع المستخدم بدون كلمة المرور\n        const { password: _, ...userWithoutPassword } = newUser;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                roleInfo: ROLES[role]\n            },\n            message: 'تم إنشاء المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Create user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في إنشاء المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n// PUT - تحديث مستخدم\nasync function PUT(request) {\n    try {\n        const url = new URL(request.url);\n        const userId = url.searchParams.get('id');\n        const userData = await request.json();\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المستخدم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل المستخدمين\n        const users = loadUsers();\n        const userIndex = users.findIndex((user)=>user.id === userId);\n        if (userIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // تحديث بيانات المستخدم\n        users[userIndex] = {\n            ...users[userIndex],\n            ...userData,\n            id: userId // التأكد من عدم تغيير المعرف\n        };\n        // حفظ التحديثات\n        saveUsers(users);\n        const { password: _, ...userWithoutPassword } = users[userIndex];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            user: {\n                ...userWithoutPassword,\n                roleInfo: ROLES[users[userIndex].role]\n            },\n            message: 'تم تحديث المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Update user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في تحديث المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - حذف مستخدم\nasync function DELETE(request) {\n    try {\n        const url = new URL(request.url);\n        const userId = url.searchParams.get('id');\n        if (!userId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'معرف المستخدم مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        // تحميل المستخدمين\n        const users = loadUsers();\n        const userIndex = users.findIndex((user)=>user.id === userId);\n        if (userIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'المستخدم غير موجود'\n            }, {\n                status: 404\n            });\n        }\n        // منع حذف المدير الرئيسي\n        if (users[userIndex].role === 'ADMIN' && users[userIndex].id === '1') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'لا يمكن حذف المدير الرئيسي'\n            }, {\n                status: 403\n            });\n        }\n        // حذف المستخدم\n        users.splice(userIndex, 1);\n        saveUsers(users);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حذف المستخدم بنجاح'\n        });\n    } catch (error) {\n        console.error('Delete user error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'خطأ في حذف المستخدم'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/users/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();