'use client';

import "./globals.css";
import { useEffect } from 'react';
import '../lib/i18n'; // Initialize i18n

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  useEffect(() => {
    // Set initial language and direction from localStorage or default to Arabic
    const savedLang = localStorage.getItem('language') || 'ar';
    document.documentElement.lang = savedLang;
    document.documentElement.dir = savedLang === 'ar' ? 'rtl' : 'ltr';

    // Add language class to body for CSS targeting
    document.body.className = `lang-${savedLang}`;

    // Set font family based on language
    if (savedLang === 'ar') {
      document.body.style.fontFamily = 'Cairo, Arial, sans-serif';
    } else {
      document.body.style.fontFamily = 'Inter, Arial, sans-serif';
    }
  }, []);

  return (
    <html lang="ar" dir="rtl">
      <head>
        <title>نظام إدارة المحتوى الإعلامي | Media Management System</title>
        <meta name="description" content="نظام متكامل لإدارة المحتوى الإعلامي والخريطة البرامجية | Integrated Media Content Management System" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
      </head>
      <body style={{ margin: 0, padding: 0, fontFamily: 'Cairo, Inter, Arial, sans-serif' }}>
        {children}
      </body>
    </html>
  );
}
