import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './AuthGuard';
import { useTranslation } from 'react-i18next';

interface NavigationCardProps {
  icon: string;
  title: string;
  subtitle: string;
  path: string;
  permission?: string;
  adminOnly?: boolean;
  height?: string;
}

// خلفية مطابقة للشاشة مع حدود ضوئية ملونة
const getIconColors = (icon: string) => {
  switch (icon) {
    case '🎬': // خلفية الشاشة مع حدود بنفسجية
      return {
        background: 'rgba(17, 24, 39, 0.8)', // نفس لون خلفية الشاشة مع شفافية خفيفة
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(139, 92, 246, 0.3)',
        hoverShadow: 'rgba(139, 92, 246, 0.8)',
        hoverBorder: '#8B5CF6',
        glowColor: '#8B5CF6',
        iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',
        iconShadow: 'rgba(139, 92, 246, 0.4)',
        borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'
      };
    case '➕': // خلفية الشاشة مع حدود تركوازية
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(6, 182, 212, 0.3)',
        hoverShadow: 'rgba(6, 182, 212, 0.8)',
        hoverBorder: '#06B6D4',
        glowColor: '#06B6D4',
        iconBackground: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 50%, #0E7490 100%)',
        iconShadow: 'rgba(6, 182, 212, 0.4)',
        borderGlow: '0 0 20px rgba(6, 182, 212, 0.3)'
      };
    case '🔄': // خلفية الشاشة مع حدود برتقالية
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(249, 115, 22, 0.3)',
        hoverShadow: 'rgba(249, 115, 22, 0.8)',
        hoverBorder: '#F97316',
        glowColor: '#F97316',
        iconBackground: 'linear-gradient(135deg, #F97316 0%, #EA580C 50%, #DC2626 100%)',
        iconShadow: 'rgba(249, 115, 22, 0.4)',
        borderGlow: '0 0 20px rgba(249, 115, 22, 0.3)'
      };
    case '📅': // خلفية الشاشة مع حدود زرقاء
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(59, 130, 246, 0.3)',
        hoverShadow: 'rgba(59, 130, 246, 0.8)',
        hoverBorder: '#3B82F6',
        glowColor: '#3B82F6',
        iconBackground: 'linear-gradient(135deg, #3B82F6 0%, #2563EB 50%, #1D4ED8 100%)',
        iconShadow: 'rgba(59, 130, 246, 0.4)',
        borderGlow: '0 0 20px rgba(59, 130, 246, 0.3)'
      };
    case '📊': // خلفية الشاشة مع حدود خضراء
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(16, 185, 129, 0.3)',
        hoverShadow: 'rgba(16, 185, 129, 0.8)',
        hoverBorder: '#10B981',
        glowColor: '#10B981',
        iconBackground: 'linear-gradient(135deg, #10B981 0%, #059669 50%, #047857 100%)',
        iconShadow: 'rgba(16, 185, 129, 0.4)',
        borderGlow: '0 0 20px rgba(16, 185, 129, 0.3)'
      };
    case '📋': // خلفية الشاشة مع حدود حمراء
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(239, 68, 68, 0.3)',
        hoverShadow: 'rgba(239, 68, 68, 0.8)',
        hoverBorder: '#EF4444',
        glowColor: '#EF4444',
        iconBackground: 'linear-gradient(135deg, #EF4444 0%, #DC2626 50%, #B91C1C 100%)',
        iconShadow: 'rgba(239, 68, 68, 0.4)',
        borderGlow: '0 0 20px rgba(239, 68, 68, 0.3)'
      };
    case '👥': // خلفية الشاشة مع حدود ذهبية
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(245, 158, 11, 0.3)',
        hoverShadow: 'rgba(245, 158, 11, 0.8)',
        hoverBorder: '#F59E0B',
        glowColor: '#F59E0B',
        iconBackground: 'linear-gradient(135deg, #F59E0B 0%, #D97706 50%, #B45309 100%)',
        iconShadow: 'rgba(245, 158, 11, 0.4)',
        borderGlow: '0 0 20px rgba(245, 158, 11, 0.3)'
      };
    case '📈': // خلفية الشاشة مع حدود وردية
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(236, 72, 153, 0.3)',
        hoverShadow: 'rgba(236, 72, 153, 0.8)',
        hoverBorder: '#EC4899',
        glowColor: '#EC4899',
        iconBackground: 'linear-gradient(135deg, #EC4899 0%, #DB2777 50%, #BE185D 100%)',
        iconShadow: 'rgba(236, 72, 153, 0.4)',
        borderGlow: '0 0 20px rgba(236, 72, 153, 0.3)'
      };
    default:
      return {
        background: 'rgba(17, 24, 39, 0.8)',
        shadow: 'rgba(0, 0, 0, 0.3)',
        border: 'rgba(139, 92, 246, 0.3)',
        hoverShadow: 'rgba(139, 92, 246, 0.8)',
        hoverBorder: '#8B5CF6',
        glowColor: '#8B5CF6',
        iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',
        iconShadow: 'rgba(139, 92, 246, 0.4)',
        borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'
      };
  }
};

export default function NavigationCard({
  icon,
  title,
  subtitle,
  path,
  permission,
  adminOnly = false,
  height = 'auto'
}: NavigationCardProps) {
  const router = useRouter();
  const { user, hasPermission } = useAuth();
  const { i18n } = useTranslation('common');

  // Get current language and direction
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';

  // التحقق من الصلاحيات
  if (adminOnly && user?.role !== 'ADMIN') {
    return null;
  }
  
  if (permission && !hasPermission(permission)) {
    return null;
  }

  const handleClick = () => {
    router.push(path);
  };

  const iconColors = getIconColors(icon);

  return (
    <div
      onClick={handleClick}
      style={{
        background: iconColors.background,
        borderRadius: '20px',
        padding: '30px',
        border: `3px solid ${iconColors.border}`,
        position: 'relative',
        overflow: 'hidden',
        cursor: 'pointer',
        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        transform: 'translateZ(0)',
        height: height,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        textAlign: 'center',
        boxShadow: `0 10px 30px ${iconColors.shadow}, ${iconColors.borderGlow}, inset 0 1px 0 rgba(255,255,255,0.1)`,
        direction: isRTL ? 'rtl' : 'ltr',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-12px) scale(1.03)';
        e.currentTarget.style.boxShadow = `0 25px 50px ${iconColors.hoverShadow}, 0 0 40px ${iconColors.hoverShadow}, 0 0 80px ${iconColors.hoverShadow}, inset 0 1px 0 rgba(255,255,255,0.2)`;
        e.currentTarget.style.border = `3px solid ${iconColors.hoverBorder}`;
        e.currentTarget.style.background = `rgba(17, 24, 39, 0.95)`;

        // تأثير الإضاءة على الأيقونة
        const iconElement = e.currentTarget.querySelector('.card-icon') as HTMLElement;
        if (iconElement) {
          iconElement.style.transform = 'scale(1.2) rotateY(5deg)';
          iconElement.style.boxShadow = `0 12px 35px ${iconColors.iconShadow}, 0 0 30px ${iconColors.glowColor}, 0 0 50px ${iconColors.glowColor}, inset 0 2px 8px rgba(255,255,255,0.3)`;
          iconElement.style.filter = `brightness(1.4) contrast(1.3) drop-shadow(0 0 25px ${iconColors.glowColor})`;
        }
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0) scale(1)';
        e.currentTarget.style.boxShadow = `0 10px 30px ${iconColors.shadow}, ${iconColors.borderGlow}, inset 0 1px 0 rgba(255,255,255,0.1)`;
        e.currentTarget.style.border = `3px solid ${iconColors.border}`;
        e.currentTarget.style.background = iconColors.background;

        // إعادة الأيقونة لحالتها الطبيعية
        const iconElement = e.currentTarget.querySelector('.card-icon') as HTMLElement;
        if (iconElement) {
          iconElement.style.transform = 'scale(1) rotateY(0deg)';
          iconElement.style.boxShadow = `0 8px 25px ${iconColors.iconShadow}, 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)`;
          iconElement.style.filter = 'brightness(1.1) contrast(1.1) drop-shadow(0 2px 4px rgba(0,0,0,0.3))';
        }
      }}
    >
      <div
        className="card-icon"
        style={{
          position: 'absolute',
          top: '25px',
          [isRTL ? 'right' : 'left']: '25px',
          width: '75px',
          height: '75px',
          background: iconColors.iconBackground,
          borderRadius: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '2.4rem',
          border: '3px solid rgba(255, 255, 255, 0.4)',
          boxShadow: `0 8px 25px ${iconColors.iconShadow}, 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)`,
          transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          transformStyle: 'preserve-3d'
        }}>
        <span style={{
          filter: 'brightness(1.3) contrast(1.2) drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
          textShadow: '0 2px 8px rgba(0,0,0,0.4)',
          color: 'white',
          transition: 'all 0.3s ease'
        }}>
          {icon}
        </span>
      </div>
      
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        paddingTop: '20px'
      }}>
        <div style={{
          fontSize: '1.7rem',
          fontWeight: '600',
          color: 'rgba(255, 255, 255, 0.95)',
          marginBottom: '12px',
          textShadow: '0 2px 12px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.1)',
          textAlign: 'center',
          letterSpacing: '0.5px',
          transition: 'all 0.3s ease'
        }}>
          {title}
        </div>

        <div style={{
          color: 'rgba(255, 255, 255, 0.75)',
          fontSize: '1.1rem',
          lineHeight: '1.5',
          textShadow: '0 1px 6px rgba(0,0,0,0.6)',
          textAlign: 'center',
          opacity: 0.9,
          transition: 'all 0.3s ease'
        }}>
          {subtitle}
        </div>
      </div>

      {/* تأثير الضوء */}
      <div style={{
        position: 'absolute',
        top: '0',
        left: '0',
        right: '0',
        height: '2px',
        background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',
        transform: 'translateX(-100%)',
        transition: 'transform 0.6s ease'
      }}></div>
    </div>
  );
}
