'use client';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/components/AuthGuard';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Logo from '@/components/Logo';

export default function Home() {
  const router = useRouter();
  const { user, hasPermission } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const { t, i18n } = useTranslation('common');

  // Get current language and direction
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';

  useEffect(() => {
    const checkUserAndRedirect = async () => {
      try {
        // التحقق من وجود المستخدم في localStorage
        const savedUser = localStorage.getItem('user');
        const token = localStorage.getItem('token');

        if (!savedUser || !token) {
          // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول
          router.replace('/login');
        } else {
          // تأخير إعادة التوجيه لإظهار الأزرار
          setTimeout(() => {
            router.replace('/dashboard');
          }, 1500);
        }
      } catch (error) {
        console.error('Error checking user:', error);
        router.replace('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkUserAndRedirect();
  }, [router]);

  // عرض شاشة التحميل أثناء التحقق من المستخدم
  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1a1d29 0%, #2d3748 50%, #1a1d29 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: isRTL ? 'rtl' : 'ltr',
        padding: '20px'
      }}>
        <div style={{
          textAlign: 'center',
          color: 'white'
        }}>
          <div style={{ fontSize: '3rem', marginBottom: '20px' }}>⏳</div>
          <div style={{ fontSize: '1.5rem' }}>{t('home.loading')}</div>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1a1d29 0%, #2d3748 50%, #1a1d29 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: isRTL ? 'rtl' : 'ltr',
      padding: '20px'
    }}>
      <div style={{
        textAlign: 'center',
        color: 'white',
        width: '100%',
        maxWidth: '1400px'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: '30px'
        }}>
          <Logo size="large" style={{ fontSize: '2rem' }} />
        </div>

        <div style={{ fontSize: '3rem', marginBottom: '20px' }}>⏳</div>
        <div style={{ fontSize: '1.5rem', marginBottom: '20px' }}>{t('home.loading')}</div>

        {/* أزرار التنقل السريع المحسنة */}
        {user && (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
            gap: '20px',
            justifyContent: 'center',
            marginBottom: '30px',
            maxWidth: '1200px',
            margin: '0 auto 30px auto',
            padding: '0 20px'
          }}>
            <button
              onClick={() => router.push('/dashboard')}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8b5cf6 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '20px',
                padding: '25px 20px',
                cursor: 'pointer',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                transition: 'all 0.3s ease',
                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '10px',
                minHeight: '120px',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';
                e.currentTarget.style.boxShadow = '0 15px 35px rgba(102, 126, 234, 0.6)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0) scale(1)';
                e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)';
              }}
            >
              <div style={{ fontSize: '3rem', filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))' }}>📊</div>
              <span>{t('home.dashboard')}</span>
            </button>

            {hasPermission('SCHEDULE_READ') && (
              <button
                onClick={() => router.push('/daily-schedule')}
                style={{
                  background: 'linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '25px 20px',
                  cursor: 'pointer',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(16, 185, 129, 0.4)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '10px',
                  minHeight: '120px',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 15px 35px rgba(16, 185, 129, 0.6)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.4)';
                }}
              >
                <div style={{ fontSize: '3rem', filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))' }}>📋</div>
                <span>{t('home.dailySchedule')}</span>
              </button>
            )}

            {hasPermission('SCHEDULE_READ') && (
              <button
                onClick={() => router.push('/weekly-schedule')}
                style={{
                  background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '25px 20px',
                  cursor: 'pointer',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(245, 158, 11, 0.4)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '10px',
                  minHeight: '120px',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 15px 35px rgba(245, 158, 11, 0.6)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(245, 158, 11, 0.4)';
                }}
              >
                <div style={{ fontSize: '3rem', filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))' }}>📅</div>
                <span>{t('home.weeklySchedule')}</span>
              </button>
            )}

            {hasPermission('MEDIA_READ') && (
              <button
                onClick={() => router.push('/media-list')}
                style={{
                  background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '25px 20px',
                  cursor: 'pointer',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(239, 68, 68, 0.4)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '10px',
                  minHeight: '120px',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 15px 35px rgba(239, 68, 68, 0.6)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(239, 68, 68, 0.4)';
                }}
              >
                <div style={{ fontSize: '3rem', filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))' }}>🎬</div>
                <span>{t('home.mediaList')}</span>
              </button>
            )}

            {hasPermission('MEDIA_CREATE') && (
              <button
                onClick={() => router.push('/add-media')}
                style={{
                  background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffc107 100%)',
                  color: '#1a1d29',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '25px 20px',
                  cursor: 'pointer',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '10px',
                  minHeight: '120px',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 15px 35px rgba(255, 215, 0, 0.6)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 215, 0, 0.4)';
                }}
              >
                <div style={{ fontSize: '3rem', filter: 'drop-shadow(0 0 10px rgba(0,0,0,0.3))' }}>➕</div>
                <span>{t('home.addMedia')}</span>
              </button>
            )}

            {user?.role === 'ADMIN' && (
              <button
                onClick={() => router.push('/admin-dashboard')}
                style={{
                  background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '25px 20px',
                  cursor: 'pointer',
                  fontSize: '1.1rem',
                  fontWeight: 'bold',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(139, 92, 246, 0.4)',
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  gap: '10px',
                  minHeight: '120px',
                  position: 'relative',
                  overflow: 'hidden'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';
                  e.currentTarget.style.boxShadow = '0 15px 35px rgba(139, 92, 246, 0.6)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0) scale(1)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(139, 92, 246, 0.4)';
                }}
              >
                <div style={{ fontSize: '3rem', filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))' }}>👥</div>
                <span>{t('home.adminPanel')}</span>
              </button>
            )}
          </div>
        )}

        <div style={{ color: '#a0aec0', fontSize: '0.9rem', marginTop: '10px' }}>{t('home.autoRedirect')}</div>
      </div>
    </div>
  );
}
