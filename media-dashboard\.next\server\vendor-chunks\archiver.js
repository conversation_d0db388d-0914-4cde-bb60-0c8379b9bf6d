/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/archiver";
exports.ids = ["vendor-chunks/archiver"];
exports.modules = {

/***/ "(ssr)/./node_modules/archiver/index.js":
/*!****************************************!*\
  !*** ./node_modules/archiver/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Archiver Vending\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar Archiver = __webpack_require__(/*! ./lib/core */ \"(ssr)/./node_modules/archiver/lib/core.js\");\n\nvar formats = {};\n\n/**\n * Dispenses a new Archiver instance.\n *\n * @constructor\n * @param  {String} format The archive format to use.\n * @param  {Object} options See [Archiver]{@link Archiver}\n * @return {Archiver}\n */\nvar vending = function(format, options) {\n  return vending.create(format, options);\n};\n\n/**\n * Creates a new Archiver instance.\n *\n * @param  {String} format The archive format to use.\n * @param  {Object} options See [Archiver]{@link Archiver}\n * @return {Archiver}\n */\nvending.create = function(format, options) {\n  if (formats[format]) {\n    var instance = new Archiver(format, options);\n    instance.setFormat(format);\n    instance.setModule(new formats[format](options));\n\n    return instance;\n  } else {\n    throw new Error('create(' + format + '): format not registered');\n  }\n};\n\n/**\n * Registers a format for use with archiver.\n *\n * @param  {String} format The name of the format.\n * @param  {Function} module The function for archiver to interact with.\n * @return void\n */\nvending.registerFormat = function(format, module) {\n  if (formats[format]) {\n    throw new Error('register(' + format + '): format already registered');\n  }\n\n  if (typeof module !== 'function') {\n    throw new Error('register(' + format + '): format module invalid');\n  }\n\n  if (typeof module.prototype.append !== 'function' || typeof module.prototype.finalize !== 'function') {\n    throw new Error('register(' + format + '): format module missing methods');\n  }\n\n  formats[format] = module;\n};\n\n/**\n * Check if the format is already registered.\n * \n * @param {String} format the name of the format.\n * @return boolean\n */\nvending.isRegisteredFormat = function (format) {\n  if (formats[format]) {\n    return true;\n  }\n  \n  return false;\n};\n\nvending.registerFormat('zip', __webpack_require__(/*! ./lib/plugins/zip */ \"(ssr)/./node_modules/archiver/lib/plugins/zip.js\"));\nvending.registerFormat('tar', __webpack_require__(/*! ./lib/plugins/tar */ \"(ssr)/./node_modules/archiver/lib/plugins/tar.js\"));\nvending.registerFormat('json', __webpack_require__(/*! ./lib/plugins/json */ \"(ssr)/./node_modules/archiver/lib/plugins/json.js\"));\n\nmodule.exports = vending;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/core.js":
/*!*******************************************!*\
  !*** ./node_modules/archiver/lib/core.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Archiver Core\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar glob = __webpack_require__(/*! readdir-glob */ \"(ssr)/./node_modules/readdir-glob/index.js\");\nvar async = __webpack_require__(/*! async */ \"(ssr)/./node_modules/async/dist/async.mjs\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar ArchiverError = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/archiver/lib/error.js\");\nvar Transform = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Transform);\n\nvar win32 = process.platform === 'win32';\n\n/**\n * @constructor\n * @param {String} format The archive format to use.\n * @param {(CoreOptions|TransformOptions)} options See also {@link ZipOptions} and {@link TarOptions}.\n */\nvar Archiver = function(format, options) {\n  if (!(this instanceof Archiver)) {\n    return new Archiver(format, options);\n  }\n\n  if (typeof format !== 'string') {\n    options = format;\n    format = 'zip';\n  }\n\n  options = this.options = util.defaults(options, {\n    highWaterMark: 1024 * 1024,\n    statConcurrency: 4\n  });\n\n  Transform.call(this, options);\n\n  this._format = false;\n  this._module = false;\n  this._pending = 0;\n  this._pointer = 0;\n\n  this._entriesCount = 0;\n  this._entriesProcessedCount = 0;\n  this._fsEntriesTotalBytes = 0;\n  this._fsEntriesProcessedBytes = 0;\n\n  this._queue = async.queue(this._onQueueTask.bind(this), 1);\n  this._queue.drain(this._onQueueDrain.bind(this));\n\n  this._statQueue = async.queue(this._onStatQueueTask.bind(this), options.statConcurrency);\n  this._statQueue.drain(this._onQueueDrain.bind(this));\n\n  this._state = {\n    aborted: false,\n    finalize: false,\n    finalizing: false,\n    finalized: false,\n    modulePiped: false\n  };\n\n  this._streams = [];\n};\n\ninherits(Archiver, Transform);\n\n/**\n * Internal logic for `abort`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._abort = function() {\n  this._state.aborted = true;\n  this._queue.kill();\n  this._statQueue.kill();\n\n  if (this._queue.idle()) {\n    this._shutdown();\n  }\n};\n\n/**\n * Internal helper for appending files.\n *\n * @private\n * @param  {String} filepath The source filepath.\n * @param  {EntryData} data The entry data.\n * @return void\n */\nArchiver.prototype._append = function(filepath, data) {\n  data = data || {};\n\n  var task = {\n    source: null,\n    filepath: filepath\n  };\n\n  if (!data.name) {\n    data.name = filepath;\n  }\n\n  data.sourcePath = filepath;\n  task.data = data;\n  this._entriesCount++;\n\n  if (data.stats && data.stats instanceof fs.Stats) {\n    task = this._updateQueueTaskWithStats(task, data.stats);\n    if (task) {\n      if (data.stats.size) {\n        this._fsEntriesTotalBytes += data.stats.size;\n      }\n\n      this._queue.push(task);\n    }\n  } else {\n    this._statQueue.push(task);\n  }\n};\n\n/**\n * Internal logic for `finalize`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._finalize = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return;\n  }\n\n  this._state.finalizing = true;\n\n  this._moduleFinalize();\n\n  this._state.finalizing = false;\n  this._state.finalized = true;\n};\n\n/**\n * Checks the various state variables to determine if we can `finalize`.\n *\n * @private\n * @return {Boolean}\n */\nArchiver.prototype._maybeFinalize = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return false;\n  }\n\n  if (this._state.finalize && this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n    return true;\n  }\n\n  return false;\n};\n\n/**\n * Appends an entry to the module.\n *\n * @private\n * @fires  Archiver#entry\n * @param  {(Buffer|Stream)} source\n * @param  {EntryData} data\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._moduleAppend = function(source, data, callback) {\n  if (this._state.aborted) {\n    callback();\n    return;\n  }\n\n  this._module.append(source, data, function(err) {\n    this._task = null;\n\n    if (this._state.aborted) {\n      this._shutdown();\n      return;\n    }\n\n    if (err) {\n      this.emit('error', err);\n      setImmediate(callback);\n      return;\n    }\n\n    /**\n     * Fires when the entry's input has been processed and appended to the archive.\n     *\n     * @event Archiver#entry\n     * @type {EntryData}\n     */\n    this.emit('entry', data);\n    this._entriesProcessedCount++;\n\n    if (data.stats && data.stats.size) {\n      this._fsEntriesProcessedBytes += data.stats.size;\n    }\n\n    /**\n     * @event Archiver#progress\n     * @type {ProgressData}\n     */\n    this.emit('progress', {\n      entries: {\n        total: this._entriesCount,\n        processed: this._entriesProcessedCount\n      },\n      fs: {\n        totalBytes: this._fsEntriesTotalBytes,\n        processedBytes: this._fsEntriesProcessedBytes\n      }\n    });\n\n    setImmediate(callback);\n  }.bind(this));\n};\n\n/**\n * Finalizes the module.\n *\n * @private\n * @return void\n */\nArchiver.prototype._moduleFinalize = function() {\n  if (typeof this._module.finalize === 'function') {\n    this._module.finalize();\n  } else if (typeof this._module.end === 'function') {\n    this._module.end();\n  } else {\n    this.emit('error', new ArchiverError('NOENDMETHOD'));\n  }\n};\n\n/**\n * Pipes the module to our internal stream with error bubbling.\n *\n * @private\n * @return void\n */\nArchiver.prototype._modulePipe = function() {\n  this._module.on('error', this._onModuleError.bind(this));\n  this._module.pipe(this);\n  this._state.modulePiped = true;\n};\n\n/**\n * Determines if the current module supports a defined feature.\n *\n * @private\n * @param  {String} key\n * @return {Boolean}\n */\nArchiver.prototype._moduleSupports = function(key) {\n  if (!this._module.supports || !this._module.supports[key]) {\n    return false;\n  }\n\n  return this._module.supports[key];\n};\n\n/**\n * Unpipes the module from our internal stream.\n *\n * @private\n * @return void\n */\nArchiver.prototype._moduleUnpipe = function() {\n  this._module.unpipe(this);\n  this._state.modulePiped = false;\n};\n\n/**\n * Normalizes entry data with fallbacks for key properties.\n *\n * @private\n * @param  {Object} data\n * @param  {fs.Stats} stats\n * @return {Object}\n */\nArchiver.prototype._normalizeEntryData = function(data, stats) {\n  data = util.defaults(data, {\n    type: 'file',\n    name: null,\n    date: null,\n    mode: null,\n    prefix: null,\n    sourcePath: null,\n    stats: false\n  });\n\n  if (stats && data.stats === false) {\n    data.stats = stats;\n  }\n\n  var isDir = data.type === 'directory';\n\n  if (data.name) {\n    if (typeof data.prefix === 'string' && '' !== data.prefix) {\n      data.name = data.prefix + '/' + data.name;\n      data.prefix = null;\n    }\n\n    data.name = util.sanitizePath(data.name);\n\n    if (data.type !== 'symlink' && data.name.slice(-1) === '/') {\n      isDir = true;\n      data.type = 'directory';\n    } else if (isDir) {\n      data.name += '/';\n    }\n  }\n\n  // 511 === 0777; 493 === 0755; 438 === 0666; 420 === 0644\n  if (typeof data.mode === 'number') {\n    if (win32) {\n      data.mode &= 511;\n    } else {\n      data.mode &= 4095\n    }\n  } else if (data.stats && data.mode === null) {\n    if (win32) {\n      data.mode = data.stats.mode & 511;\n    } else {\n      data.mode = data.stats.mode & 4095;\n    }\n\n    // stat isn't reliable on windows; force 0755 for dir\n    if (win32 && isDir) {\n      data.mode = 493;\n    }\n  } else if (data.mode === null) {\n    data.mode = isDir ? 493 : 420;\n  }\n\n  if (data.stats && data.date === null) {\n    data.date = data.stats.mtime;\n  } else {\n    data.date = util.dateify(data.date);\n  }\n\n  return data;\n};\n\n/**\n * Error listener that re-emits error on to our internal stream.\n *\n * @private\n * @param  {Error} err\n * @return void\n */\nArchiver.prototype._onModuleError = function(err) {\n  /**\n   * @event Archiver#error\n   * @type {ErrorData}\n   */\n  this.emit('error', err);\n};\n\n/**\n * Checks the various state variables after queue has drained to determine if\n * we need to `finalize`.\n *\n * @private\n * @return void\n */\nArchiver.prototype._onQueueDrain = function() {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    return;\n  }\n\n  if (this._state.finalize && this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n  }\n};\n\n/**\n * Appends each queue task to the module.\n *\n * @private\n * @param  {Object} task\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._onQueueTask = function(task, callback) {\n  var fullCallback = () => {\n    if(task.data.callback) {\n      task.data.callback();\n    }\n    callback();\n  }\n\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    fullCallback();\n    return;\n  }\n\n  this._task = task;\n  this._moduleAppend(task.source, task.data, fullCallback);\n};\n\n/**\n * Performs a file stat and reinjects the task back into the queue.\n *\n * @private\n * @param  {Object} task\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._onStatQueueTask = function(task, callback) {\n  if (this._state.finalizing || this._state.finalized || this._state.aborted) {\n    callback();\n    return;\n  }\n\n  fs.lstat(task.filepath, function(err, stats) {\n    if (this._state.aborted) {\n      setImmediate(callback);\n      return;\n    }\n\n    if (err) {\n      this._entriesCount--;\n\n      /**\n       * @event Archiver#warning\n       * @type {ErrorData}\n       */\n      this.emit('warning', err);\n      setImmediate(callback);\n      return;\n    }\n\n    task = this._updateQueueTaskWithStats(task, stats);\n\n    if (task) {\n      if (stats.size) {\n        this._fsEntriesTotalBytes += stats.size;\n      }\n\n      this._queue.push(task);\n    }\n\n    setImmediate(callback);\n  }.bind(this));\n};\n\n/**\n * Unpipes the module and ends our internal stream.\n *\n * @private\n * @return void\n */\nArchiver.prototype._shutdown = function() {\n  this._moduleUnpipe();\n  this.end();\n};\n\n/**\n * Tracks the bytes emitted by our internal stream.\n *\n * @private\n * @param  {Buffer} chunk\n * @param  {String} encoding\n * @param  {Function} callback\n * @return void\n */\nArchiver.prototype._transform = function(chunk, encoding, callback) {\n  if (chunk) {\n    this._pointer += chunk.length;\n  }\n\n  callback(null, chunk);\n};\n\n/**\n * Updates and normalizes a queue task using stats data.\n *\n * @private\n * @param  {Object} task\n * @param  {fs.Stats} stats\n * @return {Object}\n */\nArchiver.prototype._updateQueueTaskWithStats = function(task, stats) {\n  if (stats.isFile()) {\n    task.data.type = 'file';\n    task.data.sourceType = 'stream';\n    task.source = util.lazyReadStream(task.filepath);\n  } else if (stats.isDirectory() && this._moduleSupports('directory')) {\n    task.data.name = util.trailingSlashIt(task.data.name);\n    task.data.type = 'directory';\n    task.data.sourcePath = util.trailingSlashIt(task.filepath);\n    task.data.sourceType = 'buffer';\n    task.source = Buffer.concat([]);\n  } else if (stats.isSymbolicLink() && this._moduleSupports('symlink')) {\n    var linkPath = fs.readlinkSync(task.filepath);\n    var dirName = path.dirname(task.filepath);\n    task.data.type = 'symlink';\n    task.data.linkname = path.relative(dirName, path.resolve(dirName, linkPath));\n    task.data.sourceType = 'buffer';\n    task.source = Buffer.concat([]);\n  } else {\n    if (stats.isDirectory()) {\n      this.emit('warning', new ArchiverError('DIRECTORYNOTSUPPORTED', task.data));\n    } else if (stats.isSymbolicLink()) {\n      this.emit('warning', new ArchiverError('SYMLINKNOTSUPPORTED', task.data));\n    } else {\n      this.emit('warning', new ArchiverError('ENTRYNOTSUPPORTED', task.data));\n    }\n\n    return null;\n  }\n\n  task.data = this._normalizeEntryData(task.data, stats);\n\n  return task;\n};\n\n/**\n * Aborts the archiving process, taking a best-effort approach, by:\n *\n * - removing any pending queue tasks\n * - allowing any active queue workers to finish\n * - detaching internal module pipes\n * - ending both sides of the Transform stream\n *\n * It will NOT drain any remaining sources.\n *\n * @return {this}\n */\nArchiver.prototype.abort = function() {\n  if (this._state.aborted || this._state.finalized) {\n    return this;\n  }\n\n  this._abort();\n\n  return this;\n};\n\n/**\n * Appends an input source (text string, buffer, or stream) to the instance.\n *\n * When the instance has received, processed, and emitted the input, the `entry`\n * event is fired.\n *\n * @fires  Archiver#entry\n * @param  {(Buffer|Stream|String)} source The input source.\n * @param  {EntryData} data See also {@link ZipEntryData} and {@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.append = function(source, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  data = this._normalizeEntryData(data);\n\n  if (typeof data.name !== 'string' || data.name.length === 0) {\n    this.emit('error', new ArchiverError('ENTRYNAMEREQUIRED'));\n    return this;\n  }\n\n  if (data.type === 'directory' && !this._moduleSupports('directory')) {\n    this.emit('error', new ArchiverError('DIRECTORYNOTSUPPORTED', { name: data.name }));\n    return this;\n  }\n\n  source = util.normalizeInputSource(source);\n\n  if (Buffer.isBuffer(source)) {\n    data.sourceType = 'buffer';\n  } else if (util.isStream(source)) {\n    data.sourceType = 'stream';\n  } else {\n    this.emit('error', new ArchiverError('INPUTSTEAMBUFFERREQUIRED', { name: data.name }));\n    return this;\n  }\n\n  this._entriesCount++;\n  this._queue.push({\n    data: data,\n    source: source\n  });\n\n  return this;\n};\n\n/**\n * Appends a directory and its files, recursively, given its dirpath.\n *\n * @param  {String} dirpath The source directory path.\n * @param  {String} destpath The destination path within the archive.\n * @param  {(EntryData|Function)} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.directory = function(dirpath, destpath, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof dirpath !== 'string' || dirpath.length === 0) {\n    this.emit('error', new ArchiverError('DIRECTORYDIRPATHREQUIRED'));\n    return this;\n  }\n\n  this._pending++;\n\n  if (destpath === false) {\n    destpath = '';\n  } else if (typeof destpath !== 'string'){\n    destpath = dirpath;\n  }\n\n  var dataFunction = false;\n  if (typeof data === 'function') {\n    dataFunction = data;\n    data = {};\n  } else if (typeof data !== 'object') {\n    data = {};\n  }\n\n  var globOptions = {\n    stat: true,\n    dot: true\n  };\n\n  function onGlobEnd() {\n    this._pending--;\n    this._maybeFinalize();\n  }\n\n  function onGlobError(err) {\n    this.emit('error', err);\n  }\n\n  function onGlobMatch(match){\n    globber.pause();\n\n    var ignoreMatch = false;\n    var entryData = Object.assign({}, data);\n    entryData.name = match.relative;\n    entryData.prefix = destpath;\n    entryData.stats = match.stat;\n    entryData.callback = globber.resume.bind(globber);\n\n    try {\n      if (dataFunction) {\n        entryData = dataFunction(entryData);\n\n        if (entryData === false) {\n          ignoreMatch = true;\n        } else if (typeof entryData !== 'object') {\n          throw new ArchiverError('DIRECTORYFUNCTIONINVALIDDATA', { dirpath: dirpath });\n        }\n      }\n    } catch(e) {\n      this.emit('error', e);\n      return;\n    }\n\n    if (ignoreMatch) {\n      globber.resume();\n      return;\n    }\n\n    this._append(match.absolute, entryData);\n  }\n\n  var globber = glob(dirpath, globOptions);\n  globber.on('error', onGlobError.bind(this));\n  globber.on('match', onGlobMatch.bind(this));\n  globber.on('end', onGlobEnd.bind(this));\n\n  return this;\n};\n\n/**\n * Appends a file given its filepath using a\n * [lazystream]{@link https://github.com/jpommerening/node-lazystream} wrapper to\n * prevent issues with open file limits.\n *\n * When the instance has received, processed, and emitted the file, the `entry`\n * event is fired.\n *\n * @param  {String} filepath The source filepath.\n * @param  {EntryData} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.file = function(filepath, data) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof filepath !== 'string' || filepath.length === 0) {\n    this.emit('error', new ArchiverError('FILEFILEPATHREQUIRED'));\n    return this;\n  }\n\n  this._append(filepath, data);\n\n  return this;\n};\n\n/**\n * Appends multiple files that match a glob pattern.\n *\n * @param  {String} pattern The [glob pattern]{@link https://github.com/isaacs/minimatch} to match.\n * @param  {Object} options See [node-readdir-glob]{@link https://github.com/yqnn/node-readdir-glob#options}.\n * @param  {EntryData} data See also [ZipEntryData]{@link ZipEntryData} and\n * [TarEntryData]{@link TarEntryData}.\n * @return {this}\n */\nArchiver.prototype.glob = function(pattern, options, data) {\n  this._pending++;\n\n  options = util.defaults(options, {\n    stat: true,\n    pattern: pattern\n  });\n\n  function onGlobEnd() {\n    this._pending--;\n    this._maybeFinalize();\n  }\n\n  function onGlobError(err) {\n    this.emit('error', err);\n  }\n\n  function onGlobMatch(match){\n    globber.pause();\n    var entryData = Object.assign({}, data);\n    entryData.callback = globber.resume.bind(globber);\n    entryData.stats = match.stat;\n    entryData.name = match.relative;\n\n    this._append(match.absolute, entryData);\n  }\n\n  var globber = glob(options.cwd || '.', options);\n  globber.on('error', onGlobError.bind(this));\n  globber.on('match', onGlobMatch.bind(this));\n  globber.on('end', onGlobEnd.bind(this));\n\n  return this;\n};\n\n/**\n * Finalizes the instance and prevents further appending to the archive\n * structure (queue will continue til drained).\n *\n * The `end`, `close` or `finish` events on the destination stream may fire\n * right after calling this method so you should set listeners beforehand to\n * properly detect stream completion.\n *\n * @return {Promise}\n */\nArchiver.prototype.finalize = function() {\n  if (this._state.aborted) {\n    var abortedError = new ArchiverError('ABORTED');\n    this.emit('error', abortedError);\n    return Promise.reject(abortedError);\n  }\n\n  if (this._state.finalize) {\n    var finalizingError = new ArchiverError('FINALIZING');\n    this.emit('error', finalizingError);\n    return Promise.reject(finalizingError);\n  }\n\n  this._state.finalize = true;\n\n  if (this._pending === 0 && this._queue.idle() && this._statQueue.idle()) {\n    this._finalize();\n  }\n\n  var self = this;\n\n  return new Promise(function(resolve, reject) {\n    var errored;\n\n    self._module.on('end', function() {\n      if (!errored) {\n        resolve();\n      }\n    })\n\n    self._module.on('error', function(err) {\n      errored = true;\n      reject(err);\n    })\n  })\n};\n\n/**\n * Sets the module format name used for archiving.\n *\n * @param {String} format The name of the format.\n * @return {this}\n */\nArchiver.prototype.setFormat = function(format) {\n  if (this._format) {\n    this.emit('error', new ArchiverError('FORMATSET'));\n    return this;\n  }\n\n  this._format = format;\n\n  return this;\n};\n\n/**\n * Sets the module used for archiving.\n *\n * @param {Function} module The function for archiver to interact with.\n * @return {this}\n */\nArchiver.prototype.setModule = function(module) {\n  if (this._state.aborted) {\n    this.emit('error', new ArchiverError('ABORTED'));\n    return this;\n  }\n\n  if (this._state.module) {\n    this.emit('error', new ArchiverError('MODULESET'));\n    return this;\n  }\n\n  this._module = module;\n  this._modulePipe();\n\n  return this;\n};\n\n/**\n * Appends a symlink to the instance.\n *\n * This does NOT interact with filesystem and is used for programmatically creating symlinks.\n *\n * @param  {String} filepath The symlink path (within archive).\n * @param  {String} target The target path (within archive).\n * @param  {Number} mode Sets the entry permissions.\n * @return {this}\n */\nArchiver.prototype.symlink = function(filepath, target, mode) {\n  if (this._state.finalize || this._state.aborted) {\n    this.emit('error', new ArchiverError('QUEUECLOSED'));\n    return this;\n  }\n\n  if (typeof filepath !== 'string' || filepath.length === 0) {\n    this.emit('error', new ArchiverError('SYMLINKFILEPATHREQUIRED'));\n    return this;\n  }\n\n  if (typeof target !== 'string' || target.length === 0) {\n    this.emit('error', new ArchiverError('SYMLINKTARGETREQUIRED', { filepath: filepath }));\n    return this;\n  }\n\n  if (!this._moduleSupports('symlink')) {\n    this.emit('error', new ArchiverError('SYMLINKNOTSUPPORTED', { filepath: filepath }));\n    return this;\n  }\n\n  var data = {};\n  data.type = 'symlink';\n  data.name = filepath.replace(/\\\\/g, '/');\n  data.linkname = target.replace(/\\\\/g, '/');\n  data.sourceType = 'buffer';\n\n  if (typeof mode === \"number\") {\n    data.mode = mode;\n  }\n\n  this._entriesCount++;\n  this._queue.push({\n    data: data,\n    source: Buffer.concat([])\n  });\n\n  return this;\n};\n\n/**\n * Returns the current length (in bytes) that has been emitted.\n *\n * @return {Number}\n */\nArchiver.prototype.pointer = function() {\n  return this._pointer;\n};\n\n/**\n * Middleware-like helper that has yet to be fully implemented.\n *\n * @private\n * @param  {Function} plugin\n * @return {this}\n */\nArchiver.prototype.use = function(plugin) {\n  this._streams.push(plugin);\n  return this;\n};\n\nmodule.exports = Archiver;\n\n/**\n * @typedef {Object} CoreOptions\n * @global\n * @property {Number} [statConcurrency=4] Sets the number of workers used to\n * process the internal fs stat queue.\n */\n\n/**\n * @typedef {Object} TransformOptions\n * @property {Boolean} [allowHalfOpen=true] If set to false, then the stream\n * will automatically end the readable side when the writable side ends and vice\n * versa.\n * @property {Boolean} [readableObjectMode=false] Sets objectMode for readable\n * side of the stream. Has no effect if objectMode is true.\n * @property {Boolean} [writableObjectMode=false] Sets objectMode for writable\n * side of the stream. Has no effect if objectMode is true.\n * @property {Boolean} [decodeStrings=true] Whether or not to decode strings\n * into Buffers before passing them to _write(). `Writable`\n * @property {String} [encoding=NULL] If specified, then buffers will be decoded\n * to strings using the specified encoding. `Readable`\n * @property {Number} [highWaterMark=16kb] The maximum number of bytes to store\n * in the internal buffer before ceasing to read from the underlying resource.\n * `Readable` `Writable`\n * @property {Boolean} [objectMode=false] Whether this stream should behave as a\n * stream of objects. Meaning that stream.read(n) returns a single value instead\n * of a Buffer of size n. `Readable` `Writable`\n */\n\n/**\n * @typedef {Object} EntryData\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n */\n\n/**\n * @typedef {Object} ErrorData\n * @property {String} message The message of the error.\n * @property {String} code The error code assigned to this error.\n * @property {String} data Additional data provided for reporting or debugging (where available).\n */\n\n/**\n * @typedef {Object} ProgressData\n * @property {Object} entries\n * @property {Number} entries.total Number of entries that have been appended.\n * @property {Number} entries.processed Number of entries that have been processed.\n * @property {Object} fs\n * @property {Number} fs.totalBytes Number of bytes that have been appended. Calculated asynchronously and might not be accurate: it growth while entries are added. (based on fs.Stats)\n * @property {Number} fs.processedBytes Number of bytes that have been processed. (based on fs.Stats)\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXJjaGl2ZXIvbGliL2NvcmUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBLFNBQVMsbUJBQU8sQ0FBQyxjQUFJO0FBQ3JCLFdBQVcsbUJBQU8sQ0FBQyxnRUFBYztBQUNqQyxZQUFZLG1CQUFPLENBQUMsd0RBQU87QUFDM0IsV0FBVyxtQkFBTyxDQUFDLGtCQUFNO0FBQ3pCLFdBQVcsbUJBQU8sQ0FBQyxvRUFBZ0I7O0FBRW5DLGVBQWUsa0RBQXdCO0FBQ3ZDLG9CQUFvQixtQkFBTyxDQUFDLDJEQUFTO0FBQ3JDLGdCQUFnQiwwR0FBb0M7O0FBRXBEOztBQUVBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxnQ0FBZ0Msa0JBQWtCLGtCQUFrQixLQUFLLGlCQUFpQjtBQUNyRztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFFBQVE7QUFDcEIsWUFBWSxXQUFXO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksaUJBQWlCO0FBQzdCLFlBQVksV0FBVztBQUN2QixZQUFZLFVBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFFBQVE7QUFDcEIsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQixZQUFZLFVBQVU7QUFDdEIsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBLG1CQUFtQixjQUFjLGNBQWM7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBOztBQUVBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksT0FBTztBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxRQUFRO0FBQ3BCLFlBQVksVUFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxRQUFRO0FBQ3BCLFlBQVksVUFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGdCQUFnQjtBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFFBQVE7QUFDcEIsWUFBWSxRQUFRO0FBQ3BCLFlBQVksVUFBVTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQixZQUFZLFVBQVU7QUFDdEIsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksd0JBQXdCO0FBQ3BDLFlBQVksV0FBVyxlQUFlLG9CQUFvQixLQUFLLG1CQUFtQjtBQUNsRixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esb0VBQW9FLGlCQUFpQjtBQUNyRjtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQSxJQUFJO0FBQ0osdUVBQXVFLGlCQUFpQjtBQUN4RjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQixZQUFZLFFBQVE7QUFDcEIsWUFBWSxzQkFBc0IsNkJBQTZCLG9CQUFvQjtBQUNuRixrQkFBa0IsbUJBQW1CO0FBQ3JDLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxvQ0FBb0M7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Ysb0VBQW9FLGtCQUFrQjtBQUN0RjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGdCQUFnQix1REFBdUQ7QUFDdkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksUUFBUTtBQUNwQixZQUFZLFdBQVcsNkJBQTZCLG9CQUFvQjtBQUN4RSxrQkFBa0IsbUJBQW1CO0FBQ3JDLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFFBQVEsMkJBQTJCLDJDQUEyQztBQUMxRixZQUFZLFFBQVEsZ0NBQWdDLHdEQUF3RDtBQUM1RyxZQUFZLFdBQVcsNkJBQTZCLG9CQUFvQjtBQUN4RSxrQkFBa0IsbUJBQW1CO0FBQ3JDLFlBQVk7QUFDWjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7O0FBRUw7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUc7QUFDSDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWSxRQUFRO0FBQ3BCLFlBQVksUUFBUTtBQUNwQixZQUFZLFFBQVE7QUFDcEIsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLG9FQUFvRSxvQkFBb0I7QUFDeEY7QUFDQTs7QUFFQTtBQUNBLGtFQUFrRSxvQkFBb0I7QUFDdEY7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLFVBQVU7QUFDdEIsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQSxjQUFjLFFBQVE7QUFDdEI7QUFDQTs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixjQUFjLFNBQVM7QUFDdkI7QUFDQTtBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBLGNBQWMsU0FBUztBQUN2QjtBQUNBLGNBQWMsUUFBUTtBQUN0QjtBQUNBLGNBQWMsUUFBUTtBQUN0QjtBQUNBO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixjQUFjLFFBQVE7QUFDdEIsY0FBYyxlQUFlO0FBQzdCLGNBQWMsUUFBUTtBQUN0QixjQUFjLFFBQVE7QUFDdEI7QUFDQSxjQUFjLFVBQVU7QUFDeEI7QUFDQTs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixjQUFjLFFBQVE7QUFDdEIsY0FBYyxRQUFRO0FBQ3RCLGNBQWMsUUFBUTtBQUN0Qjs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixjQUFjLFFBQVE7QUFDdEIsY0FBYyxRQUFRO0FBQ3RCLGNBQWMsUUFBUTtBQUN0QixjQUFjLFFBQVE7QUFDdEIsY0FBYyxRQUFRO0FBQ3RCLGNBQWMsUUFBUTtBQUN0QiIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXGFyY2hpdmVyXFxsaWJcXGNvcmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBBcmNoaXZlciBDb3JlXG4gKlxuICogQGlnbm9yZVxuICogQGxpY2Vuc2UgW01JVF17QGxpbmsgaHR0cHM6Ly9naXRodWIuY29tL2FyY2hpdmVyanMvbm9kZS1hcmNoaXZlci9ibG9iL21hc3Rlci9MSUNFTlNFfVxuICogQGNvcHlyaWdodCAoYykgMjAxMi0yMDE0IENocmlzIFRhbGtpbmd0b24sIGNvbnRyaWJ1dG9ycy5cbiAqL1xudmFyIGZzID0gcmVxdWlyZSgnZnMnKTtcbnZhciBnbG9iID0gcmVxdWlyZSgncmVhZGRpci1nbG9iJyk7XG52YXIgYXN5bmMgPSByZXF1aXJlKCdhc3luYycpO1xudmFyIHBhdGggPSByZXF1aXJlKCdwYXRoJyk7XG52YXIgdXRpbCA9IHJlcXVpcmUoJ2FyY2hpdmVyLXV0aWxzJyk7XG5cbnZhciBpbmhlcml0cyA9IHJlcXVpcmUoJ3V0aWwnKS5pbmhlcml0cztcbnZhciBBcmNoaXZlckVycm9yID0gcmVxdWlyZSgnLi9lcnJvcicpO1xudmFyIFRyYW5zZm9ybSA9IHJlcXVpcmUoJ3JlYWRhYmxlLXN0cmVhbScpLlRyYW5zZm9ybTtcblxudmFyIHdpbjMyID0gcHJvY2Vzcy5wbGF0Zm9ybSA9PT0gJ3dpbjMyJztcblxuLyoqXG4gKiBAY29uc3RydWN0b3JcbiAqIEBwYXJhbSB7U3RyaW5nfSBmb3JtYXQgVGhlIGFyY2hpdmUgZm9ybWF0IHRvIHVzZS5cbiAqIEBwYXJhbSB7KENvcmVPcHRpb25zfFRyYW5zZm9ybU9wdGlvbnMpfSBvcHRpb25zIFNlZSBhbHNvIHtAbGluayBaaXBPcHRpb25zfSBhbmQge0BsaW5rIFRhck9wdGlvbnN9LlxuICovXG52YXIgQXJjaGl2ZXIgPSBmdW5jdGlvbihmb3JtYXQsIG9wdGlvbnMpIHtcbiAgaWYgKCEodGhpcyBpbnN0YW5jZW9mIEFyY2hpdmVyKSkge1xuICAgIHJldHVybiBuZXcgQXJjaGl2ZXIoZm9ybWF0LCBvcHRpb25zKTtcbiAgfVxuXG4gIGlmICh0eXBlb2YgZm9ybWF0ICE9PSAnc3RyaW5nJykge1xuICAgIG9wdGlvbnMgPSBmb3JtYXQ7XG4gICAgZm9ybWF0ID0gJ3ppcCc7XG4gIH1cblxuICBvcHRpb25zID0gdGhpcy5vcHRpb25zID0gdXRpbC5kZWZhdWx0cyhvcHRpb25zLCB7XG4gICAgaGlnaFdhdGVyTWFyazogMTAyNCAqIDEwMjQsXG4gICAgc3RhdENvbmN1cnJlbmN5OiA0XG4gIH0pO1xuXG4gIFRyYW5zZm9ybS5jYWxsKHRoaXMsIG9wdGlvbnMpO1xuXG4gIHRoaXMuX2Zvcm1hdCA9IGZhbHNlO1xuICB0aGlzLl9tb2R1bGUgPSBmYWxzZTtcbiAgdGhpcy5fcGVuZGluZyA9IDA7XG4gIHRoaXMuX3BvaW50ZXIgPSAwO1xuXG4gIHRoaXMuX2VudHJpZXNDb3VudCA9IDA7XG4gIHRoaXMuX2VudHJpZXNQcm9jZXNzZWRDb3VudCA9IDA7XG4gIHRoaXMuX2ZzRW50cmllc1RvdGFsQnl0ZXMgPSAwO1xuICB0aGlzLl9mc0VudHJpZXNQcm9jZXNzZWRCeXRlcyA9IDA7XG5cbiAgdGhpcy5fcXVldWUgPSBhc3luYy5xdWV1ZSh0aGlzLl9vblF1ZXVlVGFzay5iaW5kKHRoaXMpLCAxKTtcbiAgdGhpcy5fcXVldWUuZHJhaW4odGhpcy5fb25RdWV1ZURyYWluLmJpbmQodGhpcykpO1xuXG4gIHRoaXMuX3N0YXRRdWV1ZSA9IGFzeW5jLnF1ZXVlKHRoaXMuX29uU3RhdFF1ZXVlVGFzay5iaW5kKHRoaXMpLCBvcHRpb25zLnN0YXRDb25jdXJyZW5jeSk7XG4gIHRoaXMuX3N0YXRRdWV1ZS5kcmFpbih0aGlzLl9vblF1ZXVlRHJhaW4uYmluZCh0aGlzKSk7XG5cbiAgdGhpcy5fc3RhdGUgPSB7XG4gICAgYWJvcnRlZDogZmFsc2UsXG4gICAgZmluYWxpemU6IGZhbHNlLFxuICAgIGZpbmFsaXppbmc6IGZhbHNlLFxuICAgIGZpbmFsaXplZDogZmFsc2UsXG4gICAgbW9kdWxlUGlwZWQ6IGZhbHNlXG4gIH07XG5cbiAgdGhpcy5fc3RyZWFtcyA9IFtdO1xufTtcblxuaW5oZXJpdHMoQXJjaGl2ZXIsIFRyYW5zZm9ybSk7XG5cbi8qKlxuICogSW50ZXJuYWwgbG9naWMgZm9yIGBhYm9ydGAuXG4gKlxuICogQHByaXZhdGVcbiAqIEByZXR1cm4gdm9pZFxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuX2Fib3J0ID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMuX3N0YXRlLmFib3J0ZWQgPSB0cnVlO1xuICB0aGlzLl9xdWV1ZS5raWxsKCk7XG4gIHRoaXMuX3N0YXRRdWV1ZS5raWxsKCk7XG5cbiAgaWYgKHRoaXMuX3F1ZXVlLmlkbGUoKSkge1xuICAgIHRoaXMuX3NodXRkb3duKCk7XG4gIH1cbn07XG5cbi8qKlxuICogSW50ZXJuYWwgaGVscGVyIGZvciBhcHBlbmRpbmcgZmlsZXMuXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSAge1N0cmluZ30gZmlsZXBhdGggVGhlIHNvdXJjZSBmaWxlcGF0aC5cbiAqIEBwYXJhbSAge0VudHJ5RGF0YX0gZGF0YSBUaGUgZW50cnkgZGF0YS5cbiAqIEByZXR1cm4gdm9pZFxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuX2FwcGVuZCA9IGZ1bmN0aW9uKGZpbGVwYXRoLCBkYXRhKSB7XG4gIGRhdGEgPSBkYXRhIHx8IHt9O1xuXG4gIHZhciB0YXNrID0ge1xuICAgIHNvdXJjZTogbnVsbCxcbiAgICBmaWxlcGF0aDogZmlsZXBhdGhcbiAgfTtcblxuICBpZiAoIWRhdGEubmFtZSkge1xuICAgIGRhdGEubmFtZSA9IGZpbGVwYXRoO1xuICB9XG5cbiAgZGF0YS5zb3VyY2VQYXRoID0gZmlsZXBhdGg7XG4gIHRhc2suZGF0YSA9IGRhdGE7XG4gIHRoaXMuX2VudHJpZXNDb3VudCsrO1xuXG4gIGlmIChkYXRhLnN0YXRzICYmIGRhdGEuc3RhdHMgaW5zdGFuY2VvZiBmcy5TdGF0cykge1xuICAgIHRhc2sgPSB0aGlzLl91cGRhdGVRdWV1ZVRhc2tXaXRoU3RhdHModGFzaywgZGF0YS5zdGF0cyk7XG4gICAgaWYgKHRhc2spIHtcbiAgICAgIGlmIChkYXRhLnN0YXRzLnNpemUpIHtcbiAgICAgICAgdGhpcy5fZnNFbnRyaWVzVG90YWxCeXRlcyArPSBkYXRhLnN0YXRzLnNpemU7XG4gICAgICB9XG5cbiAgICAgIHRoaXMuX3F1ZXVlLnB1c2godGFzayk7XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIHRoaXMuX3N0YXRRdWV1ZS5wdXNoKHRhc2spO1xuICB9XG59O1xuXG4vKipcbiAqIEludGVybmFsIGxvZ2ljIGZvciBgZmluYWxpemVgLlxuICpcbiAqIEBwcml2YXRlXG4gKiBAcmV0dXJuIHZvaWRcbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLl9maW5hbGl6ZSA9IGZ1bmN0aW9uKCkge1xuICBpZiAodGhpcy5fc3RhdGUuZmluYWxpemluZyB8fCB0aGlzLl9zdGF0ZS5maW5hbGl6ZWQgfHwgdGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgIHJldHVybjtcbiAgfVxuXG4gIHRoaXMuX3N0YXRlLmZpbmFsaXppbmcgPSB0cnVlO1xuXG4gIHRoaXMuX21vZHVsZUZpbmFsaXplKCk7XG5cbiAgdGhpcy5fc3RhdGUuZmluYWxpemluZyA9IGZhbHNlO1xuICB0aGlzLl9zdGF0ZS5maW5hbGl6ZWQgPSB0cnVlO1xufTtcblxuLyoqXG4gKiBDaGVja3MgdGhlIHZhcmlvdXMgc3RhdGUgdmFyaWFibGVzIHRvIGRldGVybWluZSBpZiB3ZSBjYW4gYGZpbmFsaXplYC5cbiAqXG4gKiBAcHJpdmF0ZVxuICogQHJldHVybiB7Qm9vbGVhbn1cbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLl9tYXliZUZpbmFsaXplID0gZnVuY3Rpb24oKSB7XG4gIGlmICh0aGlzLl9zdGF0ZS5maW5hbGl6aW5nIHx8IHRoaXMuX3N0YXRlLmZpbmFsaXplZCB8fCB0aGlzLl9zdGF0ZS5hYm9ydGVkKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG5cbiAgaWYgKHRoaXMuX3N0YXRlLmZpbmFsaXplICYmIHRoaXMuX3BlbmRpbmcgPT09IDAgJiYgdGhpcy5fcXVldWUuaWRsZSgpICYmIHRoaXMuX3N0YXRRdWV1ZS5pZGxlKCkpIHtcbiAgICB0aGlzLl9maW5hbGl6ZSgpO1xuICAgIHJldHVybiB0cnVlO1xuICB9XG5cbiAgcmV0dXJuIGZhbHNlO1xufTtcblxuLyoqXG4gKiBBcHBlbmRzIGFuIGVudHJ5IHRvIHRoZSBtb2R1bGUuXG4gKlxuICogQHByaXZhdGVcbiAqIEBmaXJlcyAgQXJjaGl2ZXIjZW50cnlcbiAqIEBwYXJhbSAgeyhCdWZmZXJ8U3RyZWFtKX0gc291cmNlXG4gKiBAcGFyYW0gIHtFbnRyeURhdGF9IGRhdGFcbiAqIEBwYXJhbSAge0Z1bmN0aW9ufSBjYWxsYmFja1xuICogQHJldHVybiB2b2lkXG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5fbW9kdWxlQXBwZW5kID0gZnVuY3Rpb24oc291cmNlLCBkYXRhLCBjYWxsYmFjaykge1xuICBpZiAodGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgIGNhbGxiYWNrKCk7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgdGhpcy5fbW9kdWxlLmFwcGVuZChzb3VyY2UsIGRhdGEsIGZ1bmN0aW9uKGVycikge1xuICAgIHRoaXMuX3Rhc2sgPSBudWxsO1xuXG4gICAgaWYgKHRoaXMuX3N0YXRlLmFib3J0ZWQpIHtcbiAgICAgIHRoaXMuX3NodXRkb3duKCk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKGVycikge1xuICAgICAgdGhpcy5lbWl0KCdlcnJvcicsIGVycik7XG4gICAgICBzZXRJbW1lZGlhdGUoY2FsbGJhY2spO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIEZpcmVzIHdoZW4gdGhlIGVudHJ5J3MgaW5wdXQgaGFzIGJlZW4gcHJvY2Vzc2VkIGFuZCBhcHBlbmRlZCB0byB0aGUgYXJjaGl2ZS5cbiAgICAgKlxuICAgICAqIEBldmVudCBBcmNoaXZlciNlbnRyeVxuICAgICAqIEB0eXBlIHtFbnRyeURhdGF9XG4gICAgICovXG4gICAgdGhpcy5lbWl0KCdlbnRyeScsIGRhdGEpO1xuICAgIHRoaXMuX2VudHJpZXNQcm9jZXNzZWRDb3VudCsrO1xuXG4gICAgaWYgKGRhdGEuc3RhdHMgJiYgZGF0YS5zdGF0cy5zaXplKSB7XG4gICAgICB0aGlzLl9mc0VudHJpZXNQcm9jZXNzZWRCeXRlcyArPSBkYXRhLnN0YXRzLnNpemU7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICogQGV2ZW50IEFyY2hpdmVyI3Byb2dyZXNzXG4gICAgICogQHR5cGUge1Byb2dyZXNzRGF0YX1cbiAgICAgKi9cbiAgICB0aGlzLmVtaXQoJ3Byb2dyZXNzJywge1xuICAgICAgZW50cmllczoge1xuICAgICAgICB0b3RhbDogdGhpcy5fZW50cmllc0NvdW50LFxuICAgICAgICBwcm9jZXNzZWQ6IHRoaXMuX2VudHJpZXNQcm9jZXNzZWRDb3VudFxuICAgICAgfSxcbiAgICAgIGZzOiB7XG4gICAgICAgIHRvdGFsQnl0ZXM6IHRoaXMuX2ZzRW50cmllc1RvdGFsQnl0ZXMsXG4gICAgICAgIHByb2Nlc3NlZEJ5dGVzOiB0aGlzLl9mc0VudHJpZXNQcm9jZXNzZWRCeXRlc1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgc2V0SW1tZWRpYXRlKGNhbGxiYWNrKTtcbiAgfS5iaW5kKHRoaXMpKTtcbn07XG5cbi8qKlxuICogRmluYWxpemVzIHRoZSBtb2R1bGUuXG4gKlxuICogQHByaXZhdGVcbiAqIEByZXR1cm4gdm9pZFxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuX21vZHVsZUZpbmFsaXplID0gZnVuY3Rpb24oKSB7XG4gIGlmICh0eXBlb2YgdGhpcy5fbW9kdWxlLmZpbmFsaXplID09PSAnZnVuY3Rpb24nKSB7XG4gICAgdGhpcy5fbW9kdWxlLmZpbmFsaXplKCk7XG4gIH0gZWxzZSBpZiAodHlwZW9mIHRoaXMuX21vZHVsZS5lbmQgPT09ICdmdW5jdGlvbicpIHtcbiAgICB0aGlzLl9tb2R1bGUuZW5kKCk7XG4gIH0gZWxzZSB7XG4gICAgdGhpcy5lbWl0KCdlcnJvcicsIG5ldyBBcmNoaXZlckVycm9yKCdOT0VORE1FVEhPRCcpKTtcbiAgfVxufTtcblxuLyoqXG4gKiBQaXBlcyB0aGUgbW9kdWxlIHRvIG91ciBpbnRlcm5hbCBzdHJlYW0gd2l0aCBlcnJvciBidWJibGluZy5cbiAqXG4gKiBAcHJpdmF0ZVxuICogQHJldHVybiB2b2lkXG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5fbW9kdWxlUGlwZSA9IGZ1bmN0aW9uKCkge1xuICB0aGlzLl9tb2R1bGUub24oJ2Vycm9yJywgdGhpcy5fb25Nb2R1bGVFcnJvci5iaW5kKHRoaXMpKTtcbiAgdGhpcy5fbW9kdWxlLnBpcGUodGhpcyk7XG4gIHRoaXMuX3N0YXRlLm1vZHVsZVBpcGVkID0gdHJ1ZTtcbn07XG5cbi8qKlxuICogRGV0ZXJtaW5lcyBpZiB0aGUgY3VycmVudCBtb2R1bGUgc3VwcG9ydHMgYSBkZWZpbmVkIGZlYXR1cmUuXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSAge1N0cmluZ30ga2V5XG4gKiBAcmV0dXJuIHtCb29sZWFufVxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuX21vZHVsZVN1cHBvcnRzID0gZnVuY3Rpb24oa2V5KSB7XG4gIGlmICghdGhpcy5fbW9kdWxlLnN1cHBvcnRzIHx8ICF0aGlzLl9tb2R1bGUuc3VwcG9ydHNba2V5XSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuXG4gIHJldHVybiB0aGlzLl9tb2R1bGUuc3VwcG9ydHNba2V5XTtcbn07XG5cbi8qKlxuICogVW5waXBlcyB0aGUgbW9kdWxlIGZyb20gb3VyIGludGVybmFsIHN0cmVhbS5cbiAqXG4gKiBAcHJpdmF0ZVxuICogQHJldHVybiB2b2lkXG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5fbW9kdWxlVW5waXBlID0gZnVuY3Rpb24oKSB7XG4gIHRoaXMuX21vZHVsZS51bnBpcGUodGhpcyk7XG4gIHRoaXMuX3N0YXRlLm1vZHVsZVBpcGVkID0gZmFsc2U7XG59O1xuXG4vKipcbiAqIE5vcm1hbGl6ZXMgZW50cnkgZGF0YSB3aXRoIGZhbGxiYWNrcyBmb3Iga2V5IHByb3BlcnRpZXMuXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSAge09iamVjdH0gZGF0YVxuICogQHBhcmFtICB7ZnMuU3RhdHN9IHN0YXRzXG4gKiBAcmV0dXJuIHtPYmplY3R9XG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5fbm9ybWFsaXplRW50cnlEYXRhID0gZnVuY3Rpb24oZGF0YSwgc3RhdHMpIHtcbiAgZGF0YSA9IHV0aWwuZGVmYXVsdHMoZGF0YSwge1xuICAgIHR5cGU6ICdmaWxlJyxcbiAgICBuYW1lOiBudWxsLFxuICAgIGRhdGU6IG51bGwsXG4gICAgbW9kZTogbnVsbCxcbiAgICBwcmVmaXg6IG51bGwsXG4gICAgc291cmNlUGF0aDogbnVsbCxcbiAgICBzdGF0czogZmFsc2VcbiAgfSk7XG5cbiAgaWYgKHN0YXRzICYmIGRhdGEuc3RhdHMgPT09IGZhbHNlKSB7XG4gICAgZGF0YS5zdGF0cyA9IHN0YXRzO1xuICB9XG5cbiAgdmFyIGlzRGlyID0gZGF0YS50eXBlID09PSAnZGlyZWN0b3J5JztcblxuICBpZiAoZGF0YS5uYW1lKSB7XG4gICAgaWYgKHR5cGVvZiBkYXRhLnByZWZpeCA9PT0gJ3N0cmluZycgJiYgJycgIT09IGRhdGEucHJlZml4KSB7XG4gICAgICBkYXRhLm5hbWUgPSBkYXRhLnByZWZpeCArICcvJyArIGRhdGEubmFtZTtcbiAgICAgIGRhdGEucHJlZml4ID0gbnVsbDtcbiAgICB9XG5cbiAgICBkYXRhLm5hbWUgPSB1dGlsLnNhbml0aXplUGF0aChkYXRhLm5hbWUpO1xuXG4gICAgaWYgKGRhdGEudHlwZSAhPT0gJ3N5bWxpbmsnICYmIGRhdGEubmFtZS5zbGljZSgtMSkgPT09ICcvJykge1xuICAgICAgaXNEaXIgPSB0cnVlO1xuICAgICAgZGF0YS50eXBlID0gJ2RpcmVjdG9yeSc7XG4gICAgfSBlbHNlIGlmIChpc0Rpcikge1xuICAgICAgZGF0YS5uYW1lICs9ICcvJztcbiAgICB9XG4gIH1cblxuICAvLyA1MTEgPT09IDA3Nzc7IDQ5MyA9PT0gMDc1NTsgNDM4ID09PSAwNjY2OyA0MjAgPT09IDA2NDRcbiAgaWYgKHR5cGVvZiBkYXRhLm1vZGUgPT09ICdudW1iZXInKSB7XG4gICAgaWYgKHdpbjMyKSB7XG4gICAgICBkYXRhLm1vZGUgJj0gNTExO1xuICAgIH0gZWxzZSB7XG4gICAgICBkYXRhLm1vZGUgJj0gNDA5NVxuICAgIH1cbiAgfSBlbHNlIGlmIChkYXRhLnN0YXRzICYmIGRhdGEubW9kZSA9PT0gbnVsbCkge1xuICAgIGlmICh3aW4zMikge1xuICAgICAgZGF0YS5tb2RlID0gZGF0YS5zdGF0cy5tb2RlICYgNTExO1xuICAgIH0gZWxzZSB7XG4gICAgICBkYXRhLm1vZGUgPSBkYXRhLnN0YXRzLm1vZGUgJiA0MDk1O1xuICAgIH1cblxuICAgIC8vIHN0YXQgaXNuJ3QgcmVsaWFibGUgb24gd2luZG93czsgZm9yY2UgMDc1NSBmb3IgZGlyXG4gICAgaWYgKHdpbjMyICYmIGlzRGlyKSB7XG4gICAgICBkYXRhLm1vZGUgPSA0OTM7XG4gICAgfVxuICB9IGVsc2UgaWYgKGRhdGEubW9kZSA9PT0gbnVsbCkge1xuICAgIGRhdGEubW9kZSA9IGlzRGlyID8gNDkzIDogNDIwO1xuICB9XG5cbiAgaWYgKGRhdGEuc3RhdHMgJiYgZGF0YS5kYXRlID09PSBudWxsKSB7XG4gICAgZGF0YS5kYXRlID0gZGF0YS5zdGF0cy5tdGltZTtcbiAgfSBlbHNlIHtcbiAgICBkYXRhLmRhdGUgPSB1dGlsLmRhdGVpZnkoZGF0YS5kYXRlKTtcbiAgfVxuXG4gIHJldHVybiBkYXRhO1xufTtcblxuLyoqXG4gKiBFcnJvciBsaXN0ZW5lciB0aGF0IHJlLWVtaXRzIGVycm9yIG9uIHRvIG91ciBpbnRlcm5hbCBzdHJlYW0uXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSAge0Vycm9yfSBlcnJcbiAqIEByZXR1cm4gdm9pZFxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuX29uTW9kdWxlRXJyb3IgPSBmdW5jdGlvbihlcnIpIHtcbiAgLyoqXG4gICAqIEBldmVudCBBcmNoaXZlciNlcnJvclxuICAgKiBAdHlwZSB7RXJyb3JEYXRhfVxuICAgKi9cbiAgdGhpcy5lbWl0KCdlcnJvcicsIGVycik7XG59O1xuXG4vKipcbiAqIENoZWNrcyB0aGUgdmFyaW91cyBzdGF0ZSB2YXJpYWJsZXMgYWZ0ZXIgcXVldWUgaGFzIGRyYWluZWQgdG8gZGV0ZXJtaW5lIGlmXG4gKiB3ZSBuZWVkIHRvIGBmaW5hbGl6ZWAuXG4gKlxuICogQHByaXZhdGVcbiAqIEByZXR1cm4gdm9pZFxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuX29uUXVldWVEcmFpbiA9IGZ1bmN0aW9uKCkge1xuICBpZiAodGhpcy5fc3RhdGUuZmluYWxpemluZyB8fCB0aGlzLl9zdGF0ZS5maW5hbGl6ZWQgfHwgdGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgIHJldHVybjtcbiAgfVxuXG4gIGlmICh0aGlzLl9zdGF0ZS5maW5hbGl6ZSAmJiB0aGlzLl9wZW5kaW5nID09PSAwICYmIHRoaXMuX3F1ZXVlLmlkbGUoKSAmJiB0aGlzLl9zdGF0UXVldWUuaWRsZSgpKSB7XG4gICAgdGhpcy5fZmluYWxpemUoKTtcbiAgfVxufTtcblxuLyoqXG4gKiBBcHBlbmRzIGVhY2ggcXVldWUgdGFzayB0byB0aGUgbW9kdWxlLlxuICpcbiAqIEBwcml2YXRlXG4gKiBAcGFyYW0gIHtPYmplY3R9IHRhc2tcbiAqIEBwYXJhbSAge0Z1bmN0aW9ufSBjYWxsYmFja1xuICogQHJldHVybiB2b2lkXG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5fb25RdWV1ZVRhc2sgPSBmdW5jdGlvbih0YXNrLCBjYWxsYmFjaykge1xuICB2YXIgZnVsbENhbGxiYWNrID0gKCkgPT4ge1xuICAgIGlmKHRhc2suZGF0YS5jYWxsYmFjaykge1xuICAgICAgdGFzay5kYXRhLmNhbGxiYWNrKCk7XG4gICAgfVxuICAgIGNhbGxiYWNrKCk7XG4gIH1cblxuICBpZiAodGhpcy5fc3RhdGUuZmluYWxpemluZyB8fCB0aGlzLl9zdGF0ZS5maW5hbGl6ZWQgfHwgdGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgIGZ1bGxDYWxsYmFjaygpO1xuICAgIHJldHVybjtcbiAgfVxuXG4gIHRoaXMuX3Rhc2sgPSB0YXNrO1xuICB0aGlzLl9tb2R1bGVBcHBlbmQodGFzay5zb3VyY2UsIHRhc2suZGF0YSwgZnVsbENhbGxiYWNrKTtcbn07XG5cbi8qKlxuICogUGVyZm9ybXMgYSBmaWxlIHN0YXQgYW5kIHJlaW5qZWN0cyB0aGUgdGFzayBiYWNrIGludG8gdGhlIHF1ZXVlLlxuICpcbiAqIEBwcml2YXRlXG4gKiBAcGFyYW0gIHtPYmplY3R9IHRhc2tcbiAqIEBwYXJhbSAge0Z1bmN0aW9ufSBjYWxsYmFja1xuICogQHJldHVybiB2b2lkXG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5fb25TdGF0UXVldWVUYXNrID0gZnVuY3Rpb24odGFzaywgY2FsbGJhY2spIHtcbiAgaWYgKHRoaXMuX3N0YXRlLmZpbmFsaXppbmcgfHwgdGhpcy5fc3RhdGUuZmluYWxpemVkIHx8IHRoaXMuX3N0YXRlLmFib3J0ZWQpIHtcbiAgICBjYWxsYmFjaygpO1xuICAgIHJldHVybjtcbiAgfVxuXG4gIGZzLmxzdGF0KHRhc2suZmlsZXBhdGgsIGZ1bmN0aW9uKGVyciwgc3RhdHMpIHtcbiAgICBpZiAodGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgICAgc2V0SW1tZWRpYXRlKGNhbGxiYWNrKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBpZiAoZXJyKSB7XG4gICAgICB0aGlzLl9lbnRyaWVzQ291bnQtLTtcblxuICAgICAgLyoqXG4gICAgICAgKiBAZXZlbnQgQXJjaGl2ZXIjd2FybmluZ1xuICAgICAgICogQHR5cGUge0Vycm9yRGF0YX1cbiAgICAgICAqL1xuICAgICAgdGhpcy5lbWl0KCd3YXJuaW5nJywgZXJyKTtcbiAgICAgIHNldEltbWVkaWF0ZShjYWxsYmFjayk7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgdGFzayA9IHRoaXMuX3VwZGF0ZVF1ZXVlVGFza1dpdGhTdGF0cyh0YXNrLCBzdGF0cyk7XG5cbiAgICBpZiAodGFzaykge1xuICAgICAgaWYgKHN0YXRzLnNpemUpIHtcbiAgICAgICAgdGhpcy5fZnNFbnRyaWVzVG90YWxCeXRlcyArPSBzdGF0cy5zaXplO1xuICAgICAgfVxuXG4gICAgICB0aGlzLl9xdWV1ZS5wdXNoKHRhc2spO1xuICAgIH1cblxuICAgIHNldEltbWVkaWF0ZShjYWxsYmFjayk7XG4gIH0uYmluZCh0aGlzKSk7XG59O1xuXG4vKipcbiAqIFVucGlwZXMgdGhlIG1vZHVsZSBhbmQgZW5kcyBvdXIgaW50ZXJuYWwgc3RyZWFtLlxuICpcbiAqIEBwcml2YXRlXG4gKiBAcmV0dXJuIHZvaWRcbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLl9zaHV0ZG93biA9IGZ1bmN0aW9uKCkge1xuICB0aGlzLl9tb2R1bGVVbnBpcGUoKTtcbiAgdGhpcy5lbmQoKTtcbn07XG5cbi8qKlxuICogVHJhY2tzIHRoZSBieXRlcyBlbWl0dGVkIGJ5IG91ciBpbnRlcm5hbCBzdHJlYW0uXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSAge0J1ZmZlcn0gY2h1bmtcbiAqIEBwYXJhbSAge1N0cmluZ30gZW5jb2RpbmdcbiAqIEBwYXJhbSAge0Z1bmN0aW9ufSBjYWxsYmFja1xuICogQHJldHVybiB2b2lkXG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5fdHJhbnNmb3JtID0gZnVuY3Rpb24oY2h1bmssIGVuY29kaW5nLCBjYWxsYmFjaykge1xuICBpZiAoY2h1bmspIHtcbiAgICB0aGlzLl9wb2ludGVyICs9IGNodW5rLmxlbmd0aDtcbiAgfVxuXG4gIGNhbGxiYWNrKG51bGwsIGNodW5rKTtcbn07XG5cbi8qKlxuICogVXBkYXRlcyBhbmQgbm9ybWFsaXplcyBhIHF1ZXVlIHRhc2sgdXNpbmcgc3RhdHMgZGF0YS5cbiAqXG4gKiBAcHJpdmF0ZVxuICogQHBhcmFtICB7T2JqZWN0fSB0YXNrXG4gKiBAcGFyYW0gIHtmcy5TdGF0c30gc3RhdHNcbiAqIEByZXR1cm4ge09iamVjdH1cbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLl91cGRhdGVRdWV1ZVRhc2tXaXRoU3RhdHMgPSBmdW5jdGlvbih0YXNrLCBzdGF0cykge1xuICBpZiAoc3RhdHMuaXNGaWxlKCkpIHtcbiAgICB0YXNrLmRhdGEudHlwZSA9ICdmaWxlJztcbiAgICB0YXNrLmRhdGEuc291cmNlVHlwZSA9ICdzdHJlYW0nO1xuICAgIHRhc2suc291cmNlID0gdXRpbC5sYXp5UmVhZFN0cmVhbSh0YXNrLmZpbGVwYXRoKTtcbiAgfSBlbHNlIGlmIChzdGF0cy5pc0RpcmVjdG9yeSgpICYmIHRoaXMuX21vZHVsZVN1cHBvcnRzKCdkaXJlY3RvcnknKSkge1xuICAgIHRhc2suZGF0YS5uYW1lID0gdXRpbC50cmFpbGluZ1NsYXNoSXQodGFzay5kYXRhLm5hbWUpO1xuICAgIHRhc2suZGF0YS50eXBlID0gJ2RpcmVjdG9yeSc7XG4gICAgdGFzay5kYXRhLnNvdXJjZVBhdGggPSB1dGlsLnRyYWlsaW5nU2xhc2hJdCh0YXNrLmZpbGVwYXRoKTtcbiAgICB0YXNrLmRhdGEuc291cmNlVHlwZSA9ICdidWZmZXInO1xuICAgIHRhc2suc291cmNlID0gQnVmZmVyLmNvbmNhdChbXSk7XG4gIH0gZWxzZSBpZiAoc3RhdHMuaXNTeW1ib2xpY0xpbmsoKSAmJiB0aGlzLl9tb2R1bGVTdXBwb3J0cygnc3ltbGluaycpKSB7XG4gICAgdmFyIGxpbmtQYXRoID0gZnMucmVhZGxpbmtTeW5jKHRhc2suZmlsZXBhdGgpO1xuICAgIHZhciBkaXJOYW1lID0gcGF0aC5kaXJuYW1lKHRhc2suZmlsZXBhdGgpO1xuICAgIHRhc2suZGF0YS50eXBlID0gJ3N5bWxpbmsnO1xuICAgIHRhc2suZGF0YS5saW5rbmFtZSA9IHBhdGgucmVsYXRpdmUoZGlyTmFtZSwgcGF0aC5yZXNvbHZlKGRpck5hbWUsIGxpbmtQYXRoKSk7XG4gICAgdGFzay5kYXRhLnNvdXJjZVR5cGUgPSAnYnVmZmVyJztcbiAgICB0YXNrLnNvdXJjZSA9IEJ1ZmZlci5jb25jYXQoW10pO1xuICB9IGVsc2Uge1xuICAgIGlmIChzdGF0cy5pc0RpcmVjdG9yeSgpKSB7XG4gICAgICB0aGlzLmVtaXQoJ3dhcm5pbmcnLCBuZXcgQXJjaGl2ZXJFcnJvcignRElSRUNUT1JZTk9UU1VQUE9SVEVEJywgdGFzay5kYXRhKSk7XG4gICAgfSBlbHNlIGlmIChzdGF0cy5pc1N5bWJvbGljTGluaygpKSB7XG4gICAgICB0aGlzLmVtaXQoJ3dhcm5pbmcnLCBuZXcgQXJjaGl2ZXJFcnJvcignU1lNTElOS05PVFNVUFBPUlRFRCcsIHRhc2suZGF0YSkpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aGlzLmVtaXQoJ3dhcm5pbmcnLCBuZXcgQXJjaGl2ZXJFcnJvcignRU5UUllOT1RTVVBQT1JURUQnLCB0YXNrLmRhdGEpKTtcbiAgICB9XG5cbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHRhc2suZGF0YSA9IHRoaXMuX25vcm1hbGl6ZUVudHJ5RGF0YSh0YXNrLmRhdGEsIHN0YXRzKTtcblxuICByZXR1cm4gdGFzaztcbn07XG5cbi8qKlxuICogQWJvcnRzIHRoZSBhcmNoaXZpbmcgcHJvY2VzcywgdGFraW5nIGEgYmVzdC1lZmZvcnQgYXBwcm9hY2gsIGJ5OlxuICpcbiAqIC0gcmVtb3ZpbmcgYW55IHBlbmRpbmcgcXVldWUgdGFza3NcbiAqIC0gYWxsb3dpbmcgYW55IGFjdGl2ZSBxdWV1ZSB3b3JrZXJzIHRvIGZpbmlzaFxuICogLSBkZXRhY2hpbmcgaW50ZXJuYWwgbW9kdWxlIHBpcGVzXG4gKiAtIGVuZGluZyBib3RoIHNpZGVzIG9mIHRoZSBUcmFuc2Zvcm0gc3RyZWFtXG4gKlxuICogSXQgd2lsbCBOT1QgZHJhaW4gYW55IHJlbWFpbmluZyBzb3VyY2VzLlxuICpcbiAqIEByZXR1cm4ge3RoaXN9XG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5hYm9ydCA9IGZ1bmN0aW9uKCkge1xuICBpZiAodGhpcy5fc3RhdGUuYWJvcnRlZCB8fCB0aGlzLl9zdGF0ZS5maW5hbGl6ZWQpIHtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHRoaXMuX2Fib3J0KCk7XG5cbiAgcmV0dXJuIHRoaXM7XG59O1xuXG4vKipcbiAqIEFwcGVuZHMgYW4gaW5wdXQgc291cmNlICh0ZXh0IHN0cmluZywgYnVmZmVyLCBvciBzdHJlYW0pIHRvIHRoZSBpbnN0YW5jZS5cbiAqXG4gKiBXaGVuIHRoZSBpbnN0YW5jZSBoYXMgcmVjZWl2ZWQsIHByb2Nlc3NlZCwgYW5kIGVtaXR0ZWQgdGhlIGlucHV0LCB0aGUgYGVudHJ5YFxuICogZXZlbnQgaXMgZmlyZWQuXG4gKlxuICogQGZpcmVzICBBcmNoaXZlciNlbnRyeVxuICogQHBhcmFtICB7KEJ1ZmZlcnxTdHJlYW18U3RyaW5nKX0gc291cmNlIFRoZSBpbnB1dCBzb3VyY2UuXG4gKiBAcGFyYW0gIHtFbnRyeURhdGF9IGRhdGEgU2VlIGFsc28ge0BsaW5rIFppcEVudHJ5RGF0YX0gYW5kIHtAbGluayBUYXJFbnRyeURhdGF9LlxuICogQHJldHVybiB7dGhpc31cbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLmFwcGVuZCA9IGZ1bmN0aW9uKHNvdXJjZSwgZGF0YSkge1xuICBpZiAodGhpcy5fc3RhdGUuZmluYWxpemUgfHwgdGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgQXJjaGl2ZXJFcnJvcignUVVFVUVDTE9TRUQnKSk7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cblxuICBkYXRhID0gdGhpcy5fbm9ybWFsaXplRW50cnlEYXRhKGRhdGEpO1xuXG4gIGlmICh0eXBlb2YgZGF0YS5uYW1lICE9PSAnc3RyaW5nJyB8fCBkYXRhLm5hbWUubGVuZ3RoID09PSAwKSB7XG4gICAgdGhpcy5lbWl0KCdlcnJvcicsIG5ldyBBcmNoaXZlckVycm9yKCdFTlRSWU5BTUVSRVFVSVJFRCcpKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIGlmIChkYXRhLnR5cGUgPT09ICdkaXJlY3RvcnknICYmICF0aGlzLl9tb2R1bGVTdXBwb3J0cygnZGlyZWN0b3J5JykpIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgbmV3IEFyY2hpdmVyRXJyb3IoJ0RJUkVDVE9SWU5PVFNVUFBPUlRFRCcsIHsgbmFtZTogZGF0YS5uYW1lIH0pKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHNvdXJjZSA9IHV0aWwubm9ybWFsaXplSW5wdXRTb3VyY2Uoc291cmNlKTtcblxuICBpZiAoQnVmZmVyLmlzQnVmZmVyKHNvdXJjZSkpIHtcbiAgICBkYXRhLnNvdXJjZVR5cGUgPSAnYnVmZmVyJztcbiAgfSBlbHNlIGlmICh1dGlsLmlzU3RyZWFtKHNvdXJjZSkpIHtcbiAgICBkYXRhLnNvdXJjZVR5cGUgPSAnc3RyZWFtJztcbiAgfSBlbHNlIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgbmV3IEFyY2hpdmVyRXJyb3IoJ0lOUFVUU1RFQU1CVUZGRVJSRVFVSVJFRCcsIHsgbmFtZTogZGF0YS5uYW1lIH0pKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHRoaXMuX2VudHJpZXNDb3VudCsrO1xuICB0aGlzLl9xdWV1ZS5wdXNoKHtcbiAgICBkYXRhOiBkYXRhLFxuICAgIHNvdXJjZTogc291cmNlXG4gIH0pO1xuXG4gIHJldHVybiB0aGlzO1xufTtcblxuLyoqXG4gKiBBcHBlbmRzIGEgZGlyZWN0b3J5IGFuZCBpdHMgZmlsZXMsIHJlY3Vyc2l2ZWx5LCBnaXZlbiBpdHMgZGlycGF0aC5cbiAqXG4gKiBAcGFyYW0gIHtTdHJpbmd9IGRpcnBhdGggVGhlIHNvdXJjZSBkaXJlY3RvcnkgcGF0aC5cbiAqIEBwYXJhbSAge1N0cmluZ30gZGVzdHBhdGggVGhlIGRlc3RpbmF0aW9uIHBhdGggd2l0aGluIHRoZSBhcmNoaXZlLlxuICogQHBhcmFtICB7KEVudHJ5RGF0YXxGdW5jdGlvbil9IGRhdGEgU2VlIGFsc28gW1ppcEVudHJ5RGF0YV17QGxpbmsgWmlwRW50cnlEYXRhfSBhbmRcbiAqIFtUYXJFbnRyeURhdGFde0BsaW5rIFRhckVudHJ5RGF0YX0uXG4gKiBAcmV0dXJuIHt0aGlzfVxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuZGlyZWN0b3J5ID0gZnVuY3Rpb24oZGlycGF0aCwgZGVzdHBhdGgsIGRhdGEpIHtcbiAgaWYgKHRoaXMuX3N0YXRlLmZpbmFsaXplIHx8IHRoaXMuX3N0YXRlLmFib3J0ZWQpIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgbmV3IEFyY2hpdmVyRXJyb3IoJ1FVRVVFQ0xPU0VEJykpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG5cbiAgaWYgKHR5cGVvZiBkaXJwYXRoICE9PSAnc3RyaW5nJyB8fCBkaXJwYXRoLmxlbmd0aCA9PT0gMCkge1xuICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgQXJjaGl2ZXJFcnJvcignRElSRUNUT1JZRElSUEFUSFJFUVVJUkVEJykpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG5cbiAgdGhpcy5fcGVuZGluZysrO1xuXG4gIGlmIChkZXN0cGF0aCA9PT0gZmFsc2UpIHtcbiAgICBkZXN0cGF0aCA9ICcnO1xuICB9IGVsc2UgaWYgKHR5cGVvZiBkZXN0cGF0aCAhPT0gJ3N0cmluZycpe1xuICAgIGRlc3RwYXRoID0gZGlycGF0aDtcbiAgfVxuXG4gIHZhciBkYXRhRnVuY3Rpb24gPSBmYWxzZTtcbiAgaWYgKHR5cGVvZiBkYXRhID09PSAnZnVuY3Rpb24nKSB7XG4gICAgZGF0YUZ1bmN0aW9uID0gZGF0YTtcbiAgICBkYXRhID0ge307XG4gIH0gZWxzZSBpZiAodHlwZW9mIGRhdGEgIT09ICdvYmplY3QnKSB7XG4gICAgZGF0YSA9IHt9O1xuICB9XG5cbiAgdmFyIGdsb2JPcHRpb25zID0ge1xuICAgIHN0YXQ6IHRydWUsXG4gICAgZG90OiB0cnVlXG4gIH07XG5cbiAgZnVuY3Rpb24gb25HbG9iRW5kKCkge1xuICAgIHRoaXMuX3BlbmRpbmctLTtcbiAgICB0aGlzLl9tYXliZUZpbmFsaXplKCk7XG4gIH1cblxuICBmdW5jdGlvbiBvbkdsb2JFcnJvcihlcnIpIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgZXJyKTtcbiAgfVxuXG4gIGZ1bmN0aW9uIG9uR2xvYk1hdGNoKG1hdGNoKXtcbiAgICBnbG9iYmVyLnBhdXNlKCk7XG5cbiAgICB2YXIgaWdub3JlTWF0Y2ggPSBmYWxzZTtcbiAgICB2YXIgZW50cnlEYXRhID0gT2JqZWN0LmFzc2lnbih7fSwgZGF0YSk7XG4gICAgZW50cnlEYXRhLm5hbWUgPSBtYXRjaC5yZWxhdGl2ZTtcbiAgICBlbnRyeURhdGEucHJlZml4ID0gZGVzdHBhdGg7XG4gICAgZW50cnlEYXRhLnN0YXRzID0gbWF0Y2guc3RhdDtcbiAgICBlbnRyeURhdGEuY2FsbGJhY2sgPSBnbG9iYmVyLnJlc3VtZS5iaW5kKGdsb2JiZXIpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGlmIChkYXRhRnVuY3Rpb24pIHtcbiAgICAgICAgZW50cnlEYXRhID0gZGF0YUZ1bmN0aW9uKGVudHJ5RGF0YSk7XG5cbiAgICAgICAgaWYgKGVudHJ5RGF0YSA9PT0gZmFsc2UpIHtcbiAgICAgICAgICBpZ25vcmVNYXRjaCA9IHRydWU7XG4gICAgICAgIH0gZWxzZSBpZiAodHlwZW9mIGVudHJ5RGF0YSAhPT0gJ29iamVjdCcpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgQXJjaGl2ZXJFcnJvcignRElSRUNUT1JZRlVOQ1RJT05JTlZBTElEREFUQScsIHsgZGlycGF0aDogZGlycGF0aCB9KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2goZSkge1xuICAgICAgdGhpcy5lbWl0KCdlcnJvcicsIGUpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmIChpZ25vcmVNYXRjaCkge1xuICAgICAgZ2xvYmJlci5yZXN1bWUoKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0aGlzLl9hcHBlbmQobWF0Y2guYWJzb2x1dGUsIGVudHJ5RGF0YSk7XG4gIH1cblxuICB2YXIgZ2xvYmJlciA9IGdsb2IoZGlycGF0aCwgZ2xvYk9wdGlvbnMpO1xuICBnbG9iYmVyLm9uKCdlcnJvcicsIG9uR2xvYkVycm9yLmJpbmQodGhpcykpO1xuICBnbG9iYmVyLm9uKCdtYXRjaCcsIG9uR2xvYk1hdGNoLmJpbmQodGhpcykpO1xuICBnbG9iYmVyLm9uKCdlbmQnLCBvbkdsb2JFbmQuYmluZCh0aGlzKSk7XG5cbiAgcmV0dXJuIHRoaXM7XG59O1xuXG4vKipcbiAqIEFwcGVuZHMgYSBmaWxlIGdpdmVuIGl0cyBmaWxlcGF0aCB1c2luZyBhXG4gKiBbbGF6eXN0cmVhbV17QGxpbmsgaHR0cHM6Ly9naXRodWIuY29tL2pwb21tZXJlbmluZy9ub2RlLWxhenlzdHJlYW19IHdyYXBwZXIgdG9cbiAqIHByZXZlbnQgaXNzdWVzIHdpdGggb3BlbiBmaWxlIGxpbWl0cy5cbiAqXG4gKiBXaGVuIHRoZSBpbnN0YW5jZSBoYXMgcmVjZWl2ZWQsIHByb2Nlc3NlZCwgYW5kIGVtaXR0ZWQgdGhlIGZpbGUsIHRoZSBgZW50cnlgXG4gKiBldmVudCBpcyBmaXJlZC5cbiAqXG4gKiBAcGFyYW0gIHtTdHJpbmd9IGZpbGVwYXRoIFRoZSBzb3VyY2UgZmlsZXBhdGguXG4gKiBAcGFyYW0gIHtFbnRyeURhdGF9IGRhdGEgU2VlIGFsc28gW1ppcEVudHJ5RGF0YV17QGxpbmsgWmlwRW50cnlEYXRhfSBhbmRcbiAqIFtUYXJFbnRyeURhdGFde0BsaW5rIFRhckVudHJ5RGF0YX0uXG4gKiBAcmV0dXJuIHt0aGlzfVxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuZmlsZSA9IGZ1bmN0aW9uKGZpbGVwYXRoLCBkYXRhKSB7XG4gIGlmICh0aGlzLl9zdGF0ZS5maW5hbGl6ZSB8fCB0aGlzLl9zdGF0ZS5hYm9ydGVkKSB7XG4gICAgdGhpcy5lbWl0KCdlcnJvcicsIG5ldyBBcmNoaXZlckVycm9yKCdRVUVVRUNMT1NFRCcpKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIGlmICh0eXBlb2YgZmlsZXBhdGggIT09ICdzdHJpbmcnIHx8IGZpbGVwYXRoLmxlbmd0aCA9PT0gMCkge1xuICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgQXJjaGl2ZXJFcnJvcignRklMRUZJTEVQQVRIUkVRVUlSRUQnKSk7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cblxuICB0aGlzLl9hcHBlbmQoZmlsZXBhdGgsIGRhdGEpO1xuXG4gIHJldHVybiB0aGlzO1xufTtcblxuLyoqXG4gKiBBcHBlbmRzIG11bHRpcGxlIGZpbGVzIHRoYXQgbWF0Y2ggYSBnbG9iIHBhdHRlcm4uXG4gKlxuICogQHBhcmFtICB7U3RyaW5nfSBwYXR0ZXJuIFRoZSBbZ2xvYiBwYXR0ZXJuXXtAbGluayBodHRwczovL2dpdGh1Yi5jb20vaXNhYWNzL21pbmltYXRjaH0gdG8gbWF0Y2guXG4gKiBAcGFyYW0gIHtPYmplY3R9IG9wdGlvbnMgU2VlIFtub2RlLXJlYWRkaXItZ2xvYl17QGxpbmsgaHR0cHM6Ly9naXRodWIuY29tL3lxbm4vbm9kZS1yZWFkZGlyLWdsb2Ijb3B0aW9uc30uXG4gKiBAcGFyYW0gIHtFbnRyeURhdGF9IGRhdGEgU2VlIGFsc28gW1ppcEVudHJ5RGF0YV17QGxpbmsgWmlwRW50cnlEYXRhfSBhbmRcbiAqIFtUYXJFbnRyeURhdGFde0BsaW5rIFRhckVudHJ5RGF0YX0uXG4gKiBAcmV0dXJuIHt0aGlzfVxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuZ2xvYiA9IGZ1bmN0aW9uKHBhdHRlcm4sIG9wdGlvbnMsIGRhdGEpIHtcbiAgdGhpcy5fcGVuZGluZysrO1xuXG4gIG9wdGlvbnMgPSB1dGlsLmRlZmF1bHRzKG9wdGlvbnMsIHtcbiAgICBzdGF0OiB0cnVlLFxuICAgIHBhdHRlcm46IHBhdHRlcm5cbiAgfSk7XG5cbiAgZnVuY3Rpb24gb25HbG9iRW5kKCkge1xuICAgIHRoaXMuX3BlbmRpbmctLTtcbiAgICB0aGlzLl9tYXliZUZpbmFsaXplKCk7XG4gIH1cblxuICBmdW5jdGlvbiBvbkdsb2JFcnJvcihlcnIpIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgZXJyKTtcbiAgfVxuXG4gIGZ1bmN0aW9uIG9uR2xvYk1hdGNoKG1hdGNoKXtcbiAgICBnbG9iYmVyLnBhdXNlKCk7XG4gICAgdmFyIGVudHJ5RGF0YSA9IE9iamVjdC5hc3NpZ24oe30sIGRhdGEpO1xuICAgIGVudHJ5RGF0YS5jYWxsYmFjayA9IGdsb2JiZXIucmVzdW1lLmJpbmQoZ2xvYmJlcik7XG4gICAgZW50cnlEYXRhLnN0YXRzID0gbWF0Y2guc3RhdDtcbiAgICBlbnRyeURhdGEubmFtZSA9IG1hdGNoLnJlbGF0aXZlO1xuXG4gICAgdGhpcy5fYXBwZW5kKG1hdGNoLmFic29sdXRlLCBlbnRyeURhdGEpO1xuICB9XG5cbiAgdmFyIGdsb2JiZXIgPSBnbG9iKG9wdGlvbnMuY3dkIHx8ICcuJywgb3B0aW9ucyk7XG4gIGdsb2JiZXIub24oJ2Vycm9yJywgb25HbG9iRXJyb3IuYmluZCh0aGlzKSk7XG4gIGdsb2JiZXIub24oJ21hdGNoJywgb25HbG9iTWF0Y2guYmluZCh0aGlzKSk7XG4gIGdsb2JiZXIub24oJ2VuZCcsIG9uR2xvYkVuZC5iaW5kKHRoaXMpKTtcblxuICByZXR1cm4gdGhpcztcbn07XG5cbi8qKlxuICogRmluYWxpemVzIHRoZSBpbnN0YW5jZSBhbmQgcHJldmVudHMgZnVydGhlciBhcHBlbmRpbmcgdG8gdGhlIGFyY2hpdmVcbiAqIHN0cnVjdHVyZSAocXVldWUgd2lsbCBjb250aW51ZSB0aWwgZHJhaW5lZCkuXG4gKlxuICogVGhlIGBlbmRgLCBgY2xvc2VgIG9yIGBmaW5pc2hgIGV2ZW50cyBvbiB0aGUgZGVzdGluYXRpb24gc3RyZWFtIG1heSBmaXJlXG4gKiByaWdodCBhZnRlciBjYWxsaW5nIHRoaXMgbWV0aG9kIHNvIHlvdSBzaG91bGQgc2V0IGxpc3RlbmVycyBiZWZvcmVoYW5kIHRvXG4gKiBwcm9wZXJseSBkZXRlY3Qgc3RyZWFtIGNvbXBsZXRpb24uXG4gKlxuICogQHJldHVybiB7UHJvbWlzZX1cbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLmZpbmFsaXplID0gZnVuY3Rpb24oKSB7XG4gIGlmICh0aGlzLl9zdGF0ZS5hYm9ydGVkKSB7XG4gICAgdmFyIGFib3J0ZWRFcnJvciA9IG5ldyBBcmNoaXZlckVycm9yKCdBQk9SVEVEJyk7XG4gICAgdGhpcy5lbWl0KCdlcnJvcicsIGFib3J0ZWRFcnJvcik7XG4gICAgcmV0dXJuIFByb21pc2UucmVqZWN0KGFib3J0ZWRFcnJvcik7XG4gIH1cblxuICBpZiAodGhpcy5fc3RhdGUuZmluYWxpemUpIHtcbiAgICB2YXIgZmluYWxpemluZ0Vycm9yID0gbmV3IEFyY2hpdmVyRXJyb3IoJ0ZJTkFMSVpJTkcnKTtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgZmluYWxpemluZ0Vycm9yKTtcbiAgICByZXR1cm4gUHJvbWlzZS5yZWplY3QoZmluYWxpemluZ0Vycm9yKTtcbiAgfVxuXG4gIHRoaXMuX3N0YXRlLmZpbmFsaXplID0gdHJ1ZTtcblxuICBpZiAodGhpcy5fcGVuZGluZyA9PT0gMCAmJiB0aGlzLl9xdWV1ZS5pZGxlKCkgJiYgdGhpcy5fc3RhdFF1ZXVlLmlkbGUoKSkge1xuICAgIHRoaXMuX2ZpbmFsaXplKCk7XG4gIH1cblxuICB2YXIgc2VsZiA9IHRoaXM7XG5cbiAgcmV0dXJuIG5ldyBQcm9taXNlKGZ1bmN0aW9uKHJlc29sdmUsIHJlamVjdCkge1xuICAgIHZhciBlcnJvcmVkO1xuXG4gICAgc2VsZi5fbW9kdWxlLm9uKCdlbmQnLCBmdW5jdGlvbigpIHtcbiAgICAgIGlmICghZXJyb3JlZCkge1xuICAgICAgICByZXNvbHZlKCk7XG4gICAgICB9XG4gICAgfSlcblxuICAgIHNlbGYuX21vZHVsZS5vbignZXJyb3InLCBmdW5jdGlvbihlcnIpIHtcbiAgICAgIGVycm9yZWQgPSB0cnVlO1xuICAgICAgcmVqZWN0KGVycik7XG4gICAgfSlcbiAgfSlcbn07XG5cbi8qKlxuICogU2V0cyB0aGUgbW9kdWxlIGZvcm1hdCBuYW1lIHVzZWQgZm9yIGFyY2hpdmluZy5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gZm9ybWF0IFRoZSBuYW1lIG9mIHRoZSBmb3JtYXQuXG4gKiBAcmV0dXJuIHt0aGlzfVxuICovXG5BcmNoaXZlci5wcm90b3R5cGUuc2V0Rm9ybWF0ID0gZnVuY3Rpb24oZm9ybWF0KSB7XG4gIGlmICh0aGlzLl9mb3JtYXQpIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgbmV3IEFyY2hpdmVyRXJyb3IoJ0ZPUk1BVFNFVCcpKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHRoaXMuX2Zvcm1hdCA9IGZvcm1hdDtcblxuICByZXR1cm4gdGhpcztcbn07XG5cbi8qKlxuICogU2V0cyB0aGUgbW9kdWxlIHVzZWQgZm9yIGFyY2hpdmluZy5cbiAqXG4gKiBAcGFyYW0ge0Z1bmN0aW9ufSBtb2R1bGUgVGhlIGZ1bmN0aW9uIGZvciBhcmNoaXZlciB0byBpbnRlcmFjdCB3aXRoLlxuICogQHJldHVybiB7dGhpc31cbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLnNldE1vZHVsZSA9IGZ1bmN0aW9uKG1vZHVsZSkge1xuICBpZiAodGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgQXJjaGl2ZXJFcnJvcignQUJPUlRFRCcpKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIGlmICh0aGlzLl9zdGF0ZS5tb2R1bGUpIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgbmV3IEFyY2hpdmVyRXJyb3IoJ01PRFVMRVNFVCcpKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHRoaXMuX21vZHVsZSA9IG1vZHVsZTtcbiAgdGhpcy5fbW9kdWxlUGlwZSgpO1xuXG4gIHJldHVybiB0aGlzO1xufTtcblxuLyoqXG4gKiBBcHBlbmRzIGEgc3ltbGluayB0byB0aGUgaW5zdGFuY2UuXG4gKlxuICogVGhpcyBkb2VzIE5PVCBpbnRlcmFjdCB3aXRoIGZpbGVzeXN0ZW0gYW5kIGlzIHVzZWQgZm9yIHByb2dyYW1tYXRpY2FsbHkgY3JlYXRpbmcgc3ltbGlua3MuXG4gKlxuICogQHBhcmFtICB7U3RyaW5nfSBmaWxlcGF0aCBUaGUgc3ltbGluayBwYXRoICh3aXRoaW4gYXJjaGl2ZSkuXG4gKiBAcGFyYW0gIHtTdHJpbmd9IHRhcmdldCBUaGUgdGFyZ2V0IHBhdGggKHdpdGhpbiBhcmNoaXZlKS5cbiAqIEBwYXJhbSAge051bWJlcn0gbW9kZSBTZXRzIHRoZSBlbnRyeSBwZXJtaXNzaW9ucy5cbiAqIEByZXR1cm4ge3RoaXN9XG4gKi9cbkFyY2hpdmVyLnByb3RvdHlwZS5zeW1saW5rID0gZnVuY3Rpb24oZmlsZXBhdGgsIHRhcmdldCwgbW9kZSkge1xuICBpZiAodGhpcy5fc3RhdGUuZmluYWxpemUgfHwgdGhpcy5fc3RhdGUuYWJvcnRlZCkge1xuICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgQXJjaGl2ZXJFcnJvcignUVVFVUVDTE9TRUQnKSk7XG4gICAgcmV0dXJuIHRoaXM7XG4gIH1cblxuICBpZiAodHlwZW9mIGZpbGVwYXRoICE9PSAnc3RyaW5nJyB8fCBmaWxlcGF0aC5sZW5ndGggPT09IDApIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgbmV3IEFyY2hpdmVyRXJyb3IoJ1NZTUxJTktGSUxFUEFUSFJFUVVJUkVEJykpO1xuICAgIHJldHVybiB0aGlzO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0YXJnZXQgIT09ICdzdHJpbmcnIHx8IHRhcmdldC5sZW5ndGggPT09IDApIHtcbiAgICB0aGlzLmVtaXQoJ2Vycm9yJywgbmV3IEFyY2hpdmVyRXJyb3IoJ1NZTUxJTktUQVJHRVRSRVFVSVJFRCcsIHsgZmlsZXBhdGg6IGZpbGVwYXRoIH0pKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIGlmICghdGhpcy5fbW9kdWxlU3VwcG9ydHMoJ3N5bWxpbmsnKSkge1xuICAgIHRoaXMuZW1pdCgnZXJyb3InLCBuZXcgQXJjaGl2ZXJFcnJvcignU1lNTElOS05PVFNVUFBPUlRFRCcsIHsgZmlsZXBhdGg6IGZpbGVwYXRoIH0pKTtcbiAgICByZXR1cm4gdGhpcztcbiAgfVxuXG4gIHZhciBkYXRhID0ge307XG4gIGRhdGEudHlwZSA9ICdzeW1saW5rJztcbiAgZGF0YS5uYW1lID0gZmlsZXBhdGgucmVwbGFjZSgvXFxcXC9nLCAnLycpO1xuICBkYXRhLmxpbmtuYW1lID0gdGFyZ2V0LnJlcGxhY2UoL1xcXFwvZywgJy8nKTtcbiAgZGF0YS5zb3VyY2VUeXBlID0gJ2J1ZmZlcic7XG5cbiAgaWYgKHR5cGVvZiBtb2RlID09PSBcIm51bWJlclwiKSB7XG4gICAgZGF0YS5tb2RlID0gbW9kZTtcbiAgfVxuXG4gIHRoaXMuX2VudHJpZXNDb3VudCsrO1xuICB0aGlzLl9xdWV1ZS5wdXNoKHtcbiAgICBkYXRhOiBkYXRhLFxuICAgIHNvdXJjZTogQnVmZmVyLmNvbmNhdChbXSlcbiAgfSk7XG5cbiAgcmV0dXJuIHRoaXM7XG59O1xuXG4vKipcbiAqIFJldHVybnMgdGhlIGN1cnJlbnQgbGVuZ3RoIChpbiBieXRlcykgdGhhdCBoYXMgYmVlbiBlbWl0dGVkLlxuICpcbiAqIEByZXR1cm4ge051bWJlcn1cbiAqL1xuQXJjaGl2ZXIucHJvdG90eXBlLnBvaW50ZXIgPSBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHRoaXMuX3BvaW50ZXI7XG59O1xuXG4vKipcbiAqIE1pZGRsZXdhcmUtbGlrZSBoZWxwZXIgdGhhdCBoYXMgeWV0IHRvIGJlIGZ1bGx5IGltcGxlbWVudGVkLlxuICpcbiAqIEBwcml2YXRlXG4gKiBAcGFyYW0gIHtGdW5jdGlvbn0gcGx1Z2luXG4gKiBAcmV0dXJuIHt0aGlzfVxuICovXG5BcmNoaXZlci5wcm90b3R5cGUudXNlID0gZnVuY3Rpb24ocGx1Z2luKSB7XG4gIHRoaXMuX3N0cmVhbXMucHVzaChwbHVnaW4pO1xuICByZXR1cm4gdGhpcztcbn07XG5cbm1vZHVsZS5leHBvcnRzID0gQXJjaGl2ZXI7XG5cbi8qKlxuICogQHR5cGVkZWYge09iamVjdH0gQ29yZU9wdGlvbnNcbiAqIEBnbG9iYWxcbiAqIEBwcm9wZXJ0eSB7TnVtYmVyfSBbc3RhdENvbmN1cnJlbmN5PTRdIFNldHMgdGhlIG51bWJlciBvZiB3b3JrZXJzIHVzZWQgdG9cbiAqIHByb2Nlc3MgdGhlIGludGVybmFsIGZzIHN0YXQgcXVldWUuXG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiB7T2JqZWN0fSBUcmFuc2Zvcm1PcHRpb25zXG4gKiBAcHJvcGVydHkge0Jvb2xlYW59IFthbGxvd0hhbGZPcGVuPXRydWVdIElmIHNldCB0byBmYWxzZSwgdGhlbiB0aGUgc3RyZWFtXG4gKiB3aWxsIGF1dG9tYXRpY2FsbHkgZW5kIHRoZSByZWFkYWJsZSBzaWRlIHdoZW4gdGhlIHdyaXRhYmxlIHNpZGUgZW5kcyBhbmQgdmljZVxuICogdmVyc2EuXG4gKiBAcHJvcGVydHkge0Jvb2xlYW59IFtyZWFkYWJsZU9iamVjdE1vZGU9ZmFsc2VdIFNldHMgb2JqZWN0TW9kZSBmb3IgcmVhZGFibGVcbiAqIHNpZGUgb2YgdGhlIHN0cmVhbS4gSGFzIG5vIGVmZmVjdCBpZiBvYmplY3RNb2RlIGlzIHRydWUuXG4gKiBAcHJvcGVydHkge0Jvb2xlYW59IFt3cml0YWJsZU9iamVjdE1vZGU9ZmFsc2VdIFNldHMgb2JqZWN0TW9kZSBmb3Igd3JpdGFibGVcbiAqIHNpZGUgb2YgdGhlIHN0cmVhbS4gSGFzIG5vIGVmZmVjdCBpZiBvYmplY3RNb2RlIGlzIHRydWUuXG4gKiBAcHJvcGVydHkge0Jvb2xlYW59IFtkZWNvZGVTdHJpbmdzPXRydWVdIFdoZXRoZXIgb3Igbm90IHRvIGRlY29kZSBzdHJpbmdzXG4gKiBpbnRvIEJ1ZmZlcnMgYmVmb3JlIHBhc3NpbmcgdGhlbSB0byBfd3JpdGUoKS4gYFdyaXRhYmxlYFxuICogQHByb3BlcnR5IHtTdHJpbmd9IFtlbmNvZGluZz1OVUxMXSBJZiBzcGVjaWZpZWQsIHRoZW4gYnVmZmVycyB3aWxsIGJlIGRlY29kZWRcbiAqIHRvIHN0cmluZ3MgdXNpbmcgdGhlIHNwZWNpZmllZCBlbmNvZGluZy4gYFJlYWRhYmxlYFxuICogQHByb3BlcnR5IHtOdW1iZXJ9IFtoaWdoV2F0ZXJNYXJrPTE2a2JdIFRoZSBtYXhpbXVtIG51bWJlciBvZiBieXRlcyB0byBzdG9yZVxuICogaW4gdGhlIGludGVybmFsIGJ1ZmZlciBiZWZvcmUgY2Vhc2luZyB0byByZWFkIGZyb20gdGhlIHVuZGVybHlpbmcgcmVzb3VyY2UuXG4gKiBgUmVhZGFibGVgIGBXcml0YWJsZWBcbiAqIEBwcm9wZXJ0eSB7Qm9vbGVhbn0gW29iamVjdE1vZGU9ZmFsc2VdIFdoZXRoZXIgdGhpcyBzdHJlYW0gc2hvdWxkIGJlaGF2ZSBhcyBhXG4gKiBzdHJlYW0gb2Ygb2JqZWN0cy4gTWVhbmluZyB0aGF0IHN0cmVhbS5yZWFkKG4pIHJldHVybnMgYSBzaW5nbGUgdmFsdWUgaW5zdGVhZFxuICogb2YgYSBCdWZmZXIgb2Ygc2l6ZSBuLiBgUmVhZGFibGVgIGBXcml0YWJsZWBcbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIHtPYmplY3R9IEVudHJ5RGF0YVxuICogQHByb3BlcnR5IHtTdHJpbmd9IG5hbWUgU2V0cyB0aGUgZW50cnkgbmFtZSBpbmNsdWRpbmcgaW50ZXJuYWwgcGF0aC5cbiAqIEBwcm9wZXJ0eSB7KFN0cmluZ3xEYXRlKX0gW2RhdGU9Tk9XKCldIFNldHMgdGhlIGVudHJ5IGRhdGUuXG4gKiBAcHJvcGVydHkge051bWJlcn0gW21vZGU9RDowNzU1L0Y6MDY0NF0gU2V0cyB0aGUgZW50cnkgcGVybWlzc2lvbnMuXG4gKiBAcHJvcGVydHkge1N0cmluZ30gW3ByZWZpeF0gU2V0cyBhIHBhdGggcHJlZml4IGZvciB0aGUgZW50cnkgbmFtZS4gVXNlZnVsXG4gKiB3aGVuIHdvcmtpbmcgd2l0aCBtZXRob2RzIGxpa2UgYGRpcmVjdG9yeWAgb3IgYGdsb2JgLlxuICogQHByb3BlcnR5IHtmcy5TdGF0c30gW3N0YXRzXSBTZXRzIHRoZSBmcyBzdGF0IGRhdGEgZm9yIHRoaXMgZW50cnkgYWxsb3dpbmdcbiAqIGZvciByZWR1Y3Rpb24gb2YgZnMgc3RhdCBjYWxscyB3aGVuIHN0YXQgZGF0YSBpcyBhbHJlYWR5IGtub3duLlxuICovXG5cbi8qKlxuICogQHR5cGVkZWYge09iamVjdH0gRXJyb3JEYXRhXG4gKiBAcHJvcGVydHkge1N0cmluZ30gbWVzc2FnZSBUaGUgbWVzc2FnZSBvZiB0aGUgZXJyb3IuXG4gKiBAcHJvcGVydHkge1N0cmluZ30gY29kZSBUaGUgZXJyb3IgY29kZSBhc3NpZ25lZCB0byB0aGlzIGVycm9yLlxuICogQHByb3BlcnR5IHtTdHJpbmd9IGRhdGEgQWRkaXRpb25hbCBkYXRhIHByb3ZpZGVkIGZvciByZXBvcnRpbmcgb3IgZGVidWdnaW5nICh3aGVyZSBhdmFpbGFibGUpLlxuICovXG5cbi8qKlxuICogQHR5cGVkZWYge09iamVjdH0gUHJvZ3Jlc3NEYXRhXG4gKiBAcHJvcGVydHkge09iamVjdH0gZW50cmllc1xuICogQHByb3BlcnR5IHtOdW1iZXJ9IGVudHJpZXMudG90YWwgTnVtYmVyIG9mIGVudHJpZXMgdGhhdCBoYXZlIGJlZW4gYXBwZW5kZWQuXG4gKiBAcHJvcGVydHkge051bWJlcn0gZW50cmllcy5wcm9jZXNzZWQgTnVtYmVyIG9mIGVudHJpZXMgdGhhdCBoYXZlIGJlZW4gcHJvY2Vzc2VkLlxuICogQHByb3BlcnR5IHtPYmplY3R9IGZzXG4gKiBAcHJvcGVydHkge051bWJlcn0gZnMudG90YWxCeXRlcyBOdW1iZXIgb2YgYnl0ZXMgdGhhdCBoYXZlIGJlZW4gYXBwZW5kZWQuIENhbGN1bGF0ZWQgYXN5bmNocm9ub3VzbHkgYW5kIG1pZ2h0IG5vdCBiZSBhY2N1cmF0ZTogaXQgZ3Jvd3RoIHdoaWxlIGVudHJpZXMgYXJlIGFkZGVkLiAoYmFzZWQgb24gZnMuU3RhdHMpXG4gKiBAcHJvcGVydHkge051bWJlcn0gZnMucHJvY2Vzc2VkQnl0ZXMgTnVtYmVyIG9mIGJ5dGVzIHRoYXQgaGF2ZSBiZWVuIHByb2Nlc3NlZC4gKGJhc2VkIG9uIGZzLlN0YXRzKVxuICovXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/error.js":
/*!********************************************!*\
  !*** ./node_modules/archiver/lib/error.js ***!
  \********************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/**\n * Archiver Core\n *\n * @ignore\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\n\nvar util = __webpack_require__(/*! util */ \"util\");\n\nconst ERROR_CODES = {\n  'ABORTED': 'archive was aborted',\n  'DIRECTORYDIRPATHREQUIRED': 'diretory dirpath argument must be a non-empty string value',\n  'DIRECTORYFUNCTIONINVALIDDATA': 'invalid data returned by directory custom data function',\n  'ENTRYNAMEREQUIRED': 'entry name must be a non-empty string value',\n  'FILEFILEPATHREQUIRED': 'file filepath argument must be a non-empty string value',\n  'FINALIZING': 'archive already finalizing',\n  'QUEUECLOSED': 'queue closed',\n  'NOENDMETHOD': 'no suitable finalize/end method defined by module',\n  'DIRECTORYNOTSUPPORTED': 'support for directory entries not defined by module',\n  'FORMATSET': 'archive format already set',\n  'INPUTSTEAMBUFFERREQUIRED': 'input source must be valid Stream or Buffer instance',\n  'MODULESET': 'module already set',\n  'SYMLINKNOTSUPPORTED': 'support for symlink entries not defined by module',\n  'SYMLINKFILEPATHREQUIRED': 'symlink filepath argument must be a non-empty string value',\n  'SYMLINKTARGETREQUIRED': 'symlink target argument must be a non-empty string value',\n  'ENTRYNOTSUPPORTED': 'entry not supported'\n};\n\nfunction ArchiverError(code, data) {\n  Error.captureStackTrace(this, this.constructor);\n  //this.name = this.constructor.name;\n  this.message = ERROR_CODES[code] || code;\n  this.code = code;\n  this.data = data;\n}\n\nutil.inherits(ArchiverError, Error);\n\nexports = module.exports = ArchiverError;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/plugins/json.js":
/*!***************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/json.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * JSON Format Plugin\n *\n * @module plugins/json\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\nvar Transform = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Transform);\n\nvar crc32 = __webpack_require__(/*! buffer-crc32 */ \"(ssr)/./node_modules/buffer-crc32/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {(JsonOptions|TransformOptions)} options\n */\nvar Json = function(options) {\n  if (!(this instanceof Json)) {\n    return new Json(options);\n  }\n\n  options = this.options = util.defaults(options, {});\n\n  Transform.call(this, options);\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.files = [];\n};\n\ninherits(Json, Transform);\n\n/**\n * [_transform description]\n *\n * @private\n * @param  {Buffer}   chunk\n * @param  {String}   encoding\n * @param  {Function} callback\n * @return void\n */\nJson.prototype._transform = function(chunk, encoding, callback) {\n  callback(null, chunk);\n};\n\n/**\n * [_writeStringified description]\n *\n * @private\n * @return void\n */\nJson.prototype._writeStringified = function() {\n  var fileString = JSON.stringify(this.files);\n  this.write(fileString);\n};\n\n/**\n * [append description]\n *\n * @param  {(Buffer|Stream)}   source\n * @param  {EntryData}   data\n * @param  {Function} callback\n * @return void\n */\nJson.prototype.append = function(source, data, callback) {\n  var self = this;\n\n  data.crc32 = 0;\n\n  function onend(err, sourceBuffer) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    data.size = sourceBuffer.length || 0;\n    data.crc32 = crc32.unsigned(sourceBuffer);\n\n    self.files.push(data);\n\n    callback(null, data);\n  }\n\n  if (data.sourceType === 'buffer') {\n    onend(null, source);\n  } else if (data.sourceType === 'stream') {\n    util.collectStream(source, onend);\n  }\n};\n\n/**\n * [finalize description]\n *\n * @return void\n */\nJson.prototype.finalize = function() {\n  this._writeStringified();\n  this.end();\n};\n\nmodule.exports = Json;\n\n/**\n * @typedef {Object} JsonOptions\n * @global\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/plugins/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/plugins/tar.js":
/*!**************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/tar.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * TAR Format Plugin\n *\n * @module plugins/tar\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar zlib = __webpack_require__(/*! zlib */ \"zlib\");\n\nvar engine = __webpack_require__(/*! tar-stream */ \"(ssr)/./node_modules/tar-stream/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {TarOptions} options\n */\nvar Tar = function(options) {\n  if (!(this instanceof Tar)) {\n    return new Tar(options);\n  }\n\n  options = this.options = util.defaults(options, {\n    gzip: false\n  });\n\n  if (typeof options.gzipOptions !== 'object') {\n    options.gzipOptions = {};\n  }\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.engine = engine.pack(options);\n  this.compressor = false;\n\n  if (options.gzip) {\n    this.compressor = zlib.createGzip(options.gzipOptions);\n    this.compressor.on('error', this._onCompressorError.bind(this));\n  }\n};\n\n/**\n * [_onCompressorError description]\n *\n * @private\n * @param  {Error} err\n * @return void\n */\nTar.prototype._onCompressorError = function(err) {\n  this.engine.emit('error', err);\n};\n\n/**\n * [append description]\n *\n * @param  {(Buffer|Stream)} source\n * @param  {TarEntryData} data\n * @param  {Function} callback\n * @return void\n */\nTar.prototype.append = function(source, data, callback) {\n  var self = this;\n\n  data.mtime = data.date;\n\n  function append(err, sourceBuffer) {\n    if (err) {\n      callback(err);\n      return;\n    }\n\n    self.engine.entry(data, sourceBuffer, function(err) {\n      callback(err, data);\n    });\n  }\n\n  if (data.sourceType === 'buffer') {\n    append(null, source);\n  } else if (data.sourceType === 'stream' && data.stats) {\n    data.size = data.stats.size;\n\n    var entry = self.engine.entry(data, function(err) {\n      callback(err, data);\n    });\n\n    source.pipe(entry);\n  } else if (data.sourceType === 'stream') {\n    util.collectStream(source, append);\n  }\n};\n\n/**\n * [finalize description]\n *\n * @return void\n */\nTar.prototype.finalize = function() {\n  this.engine.finalize();\n};\n\n/**\n * [on description]\n *\n * @return this.engine\n */\nTar.prototype.on = function() {\n  return this.engine.on.apply(this.engine, arguments);\n};\n\n/**\n * [pipe description]\n *\n * @param  {String} destination\n * @param  {Object} options\n * @return this.engine\n */\nTar.prototype.pipe = function(destination, options) {\n  if (this.compressor) {\n    return this.engine.pipe.apply(this.engine, [this.compressor]).pipe(destination, options);\n  } else {\n    return this.engine.pipe.apply(this.engine, arguments);\n  }\n};\n\n/**\n * [unpipe description]\n *\n * @return this.engine\n */\nTar.prototype.unpipe = function() {\n  if (this.compressor) {\n    return this.compressor.unpipe.apply(this.compressor, arguments);\n  } else {\n    return this.engine.unpipe.apply(this.engine, arguments);\n  }\n};\n\nmodule.exports = Tar;\n\n/**\n * @typedef {Object} TarOptions\n * @global\n * @property {Boolean} [gzip=false] Compress the tar archive using gzip.\n * @property {Object} [gzipOptions] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n * @property {*} [*] See [tar-stream]{@link https://github.com/mafintosh/tar-stream} documentation for additional properties.\n */\n\n/**\n * @typedef {Object} TarEntryData\n * @global\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n */\n\n/**\n * TarStream Module\n * @external TarStream\n * @see {@link https://github.com/mafintosh/tar-stream}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/plugins/tar.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/archiver/lib/plugins/zip.js":
/*!**************************************************!*\
  !*** ./node_modules/archiver/lib/plugins/zip.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * ZIP Format Plugin\n *\n * @module plugins/zip\n * @license [MIT]{@link https://github.com/archiverjs/node-archiver/blob/master/LICENSE}\n * @copyright (c) 2012-2014 Chris Talkington, contributors.\n */\nvar engine = __webpack_require__(/*! zip-stream */ \"(ssr)/./node_modules/zip-stream/index.js\");\nvar util = __webpack_require__(/*! archiver-utils */ \"(ssr)/./node_modules/archiver-utils/index.js\");\n\n/**\n * @constructor\n * @param {ZipOptions} [options]\n * @param {String} [options.comment] Sets the zip archive comment.\n * @param {Boolean} [options.forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @param {Boolean} [options.forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @param {Boolean} [options.namePrependSlash=false] Prepends a forward slash to archive file paths.\n * @param {Boolean} [options.store=false] Sets the compression method to STORE.\n * @param {Object} [options.zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n */\nvar Zip = function(options) {\n  if (!(this instanceof Zip)) {\n    return new Zip(options);\n  }\n\n  options = this.options = util.defaults(options, {\n    comment: '',\n    forceUTC: false,\n    namePrependSlash: false,\n    store: false\n  });\n\n  this.supports = {\n    directory: true,\n    symlink: true\n  };\n\n  this.engine = new engine(options);\n};\n\n/**\n * @param  {(Buffer|Stream)} source\n * @param  {ZipEntryData} data\n * @param  {String} data.name Sets the entry name including internal path.\n * @param  {(String|Date)} [data.date=NOW()] Sets the entry date.\n * @param  {Number} [data.mode=D:0755/F:0644] Sets the entry permissions.\n * @param  {String} [data.prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @param  {fs.Stats} [data.stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n * @param  {Boolean} [data.store=ZipOptions.store] Sets the compression method to STORE.\n * @param  {Function} callback\n * @return void\n */\nZip.prototype.append = function(source, data, callback) {\n  this.engine.entry(source, data, callback);\n};\n\n/**\n * @return void\n */\nZip.prototype.finalize = function() {\n  this.engine.finalize();\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.on = function() {\n  return this.engine.on.apply(this.engine, arguments);\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.pipe = function() {\n  return this.engine.pipe.apply(this.engine, arguments);\n};\n\n/**\n * @return this.engine\n */\nZip.prototype.unpipe = function() {\n  return this.engine.unpipe.apply(this.engine, arguments);\n};\n\nmodule.exports = Zip;\n\n/**\n * @typedef {Object} ZipOptions\n * @global\n * @property {String} [comment] Sets the zip archive comment.\n * @property {Boolean} [forceLocalTime=false] Forces the archive to contain local file times instead of UTC.\n * @property {Boolean} [forceZip64=false] Forces the archive to contain ZIP64 headers.\n * @prpperty {Boolean} [namePrependSlash=false] Prepends a forward slash to archive file paths.\n * @property {Boolean} [store=false] Sets the compression method to STORE.\n * @property {Object} [zlib] Passed to [zlib]{@link https://nodejs.org/api/zlib.html#zlib_class_options}\n * to control compression.\n * @property {*} [*] See [zip-stream]{@link https://archiverjs.com/zip-stream/ZipStream.html} documentation for current list of properties.\n */\n\n/**\n * @typedef {Object} ZipEntryData\n * @global\n * @property {String} name Sets the entry name including internal path.\n * @property {(String|Date)} [date=NOW()] Sets the entry date.\n * @property {Number} [mode=D:0755/F:0644] Sets the entry permissions.\n * @property {Boolean} [namePrependSlash=ZipOptions.namePrependSlash] Prepends a forward slash to archive file paths.\n * @property {String} [prefix] Sets a path prefix for the entry name. Useful\n * when working with methods like `directory` or `glob`.\n * @property {fs.Stats} [stats] Sets the fs stat data for this entry allowing\n * for reduction of fs stat calls when stat data is already known.\n * @property {Boolean} [store=ZipOptions.store] Sets the compression method to STORE.\n */\n\n/**\n * ZipStream Module\n * @external ZipStream\n * @see {@link https://www.archiverjs.com/zip-stream/ZipStream.html}\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYXJjaGl2ZXIvbGliL3BsdWdpbnMvemlwLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQSxhQUFhLG1CQUFPLENBQUMsNERBQVk7QUFDakMsV0FBVyxtQkFBTyxDQUFDLG9FQUFnQjs7QUFFbkM7QUFDQTtBQUNBLFdBQVcsWUFBWTtBQUN2QixXQUFXLFFBQVE7QUFDbkIsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsU0FBUztBQUNwQixXQUFXLFNBQVM7QUFDcEIsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsUUFBUSxnQ0FBZ0M7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRzs7QUFFSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsWUFBWSxpQkFBaUI7QUFDN0IsWUFBWSxjQUFjO0FBQzFCLFlBQVksUUFBUTtBQUNwQixZQUFZLGVBQWU7QUFDM0IsWUFBWSxRQUFRO0FBQ3BCLFlBQVksUUFBUTtBQUNwQjtBQUNBLFlBQVksVUFBVTtBQUN0QjtBQUNBLFlBQVksU0FBUztBQUNyQixZQUFZLFVBQVU7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQSxjQUFjLFFBQVE7QUFDdEIsY0FBYyxTQUFTO0FBQ3ZCLGNBQWMsU0FBUztBQUN2QixjQUFjLFNBQVM7QUFDdkIsY0FBYyxTQUFTO0FBQ3ZCLGNBQWMsUUFBUSx3QkFBd0I7QUFDOUM7QUFDQSxjQUFjLEdBQUcscUJBQXFCLHdEQUF3RDtBQUM5Rjs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBLGNBQWMsUUFBUTtBQUN0QixjQUFjLGVBQWU7QUFDN0IsY0FBYyxRQUFRO0FBQ3RCLGNBQWMsU0FBUztBQUN2QixjQUFjLFFBQVE7QUFDdEI7QUFDQSxjQUFjLFVBQVU7QUFDeEI7QUFDQSxjQUFjLFNBQVM7QUFDdkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcYXJjaGl2ZXJcXGxpYlxccGx1Z2luc1xcemlwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogWklQIEZvcm1hdCBQbHVnaW5cbiAqXG4gKiBAbW9kdWxlIHBsdWdpbnMvemlwXG4gKiBAbGljZW5zZSBbTUlUXXtAbGluayBodHRwczovL2dpdGh1Yi5jb20vYXJjaGl2ZXJqcy9ub2RlLWFyY2hpdmVyL2Jsb2IvbWFzdGVyL0xJQ0VOU0V9XG4gKiBAY29weXJpZ2h0IChjKSAyMDEyLTIwMTQgQ2hyaXMgVGFsa2luZ3RvbiwgY29udHJpYnV0b3JzLlxuICovXG52YXIgZW5naW5lID0gcmVxdWlyZSgnemlwLXN0cmVhbScpO1xudmFyIHV0aWwgPSByZXF1aXJlKCdhcmNoaXZlci11dGlscycpO1xuXG4vKipcbiAqIEBjb25zdHJ1Y3RvclxuICogQHBhcmFtIHtaaXBPcHRpb25zfSBbb3B0aW9uc11cbiAqIEBwYXJhbSB7U3RyaW5nfSBbb3B0aW9ucy5jb21tZW50XSBTZXRzIHRoZSB6aXAgYXJjaGl2ZSBjb21tZW50LlxuICogQHBhcmFtIHtCb29sZWFufSBbb3B0aW9ucy5mb3JjZUxvY2FsVGltZT1mYWxzZV0gRm9yY2VzIHRoZSBhcmNoaXZlIHRvIGNvbnRhaW4gbG9jYWwgZmlsZSB0aW1lcyBpbnN0ZWFkIG9mIFVUQy5cbiAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMuZm9yY2VaaXA2ND1mYWxzZV0gRm9yY2VzIHRoZSBhcmNoaXZlIHRvIGNvbnRhaW4gWklQNjQgaGVhZGVycy5cbiAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMubmFtZVByZXBlbmRTbGFzaD1mYWxzZV0gUHJlcGVuZHMgYSBmb3J3YXJkIHNsYXNoIHRvIGFyY2hpdmUgZmlsZSBwYXRocy5cbiAqIEBwYXJhbSB7Qm9vbGVhbn0gW29wdGlvbnMuc3RvcmU9ZmFsc2VdIFNldHMgdGhlIGNvbXByZXNzaW9uIG1ldGhvZCB0byBTVE9SRS5cbiAqIEBwYXJhbSB7T2JqZWN0fSBbb3B0aW9ucy56bGliXSBQYXNzZWQgdG8gW3psaWJde0BsaW5rIGh0dHBzOi8vbm9kZWpzLm9yZy9hcGkvemxpYi5odG1sI3psaWJfY2xhc3Nfb3B0aW9uc31cbiAqL1xudmFyIFppcCA9IGZ1bmN0aW9uKG9wdGlvbnMpIHtcbiAgaWYgKCEodGhpcyBpbnN0YW5jZW9mIFppcCkpIHtcbiAgICByZXR1cm4gbmV3IFppcChvcHRpb25zKTtcbiAgfVxuXG4gIG9wdGlvbnMgPSB0aGlzLm9wdGlvbnMgPSB1dGlsLmRlZmF1bHRzKG9wdGlvbnMsIHtcbiAgICBjb21tZW50OiAnJyxcbiAgICBmb3JjZVVUQzogZmFsc2UsXG4gICAgbmFtZVByZXBlbmRTbGFzaDogZmFsc2UsXG4gICAgc3RvcmU6IGZhbHNlXG4gIH0pO1xuXG4gIHRoaXMuc3VwcG9ydHMgPSB7XG4gICAgZGlyZWN0b3J5OiB0cnVlLFxuICAgIHN5bWxpbms6IHRydWVcbiAgfTtcblxuICB0aGlzLmVuZ2luZSA9IG5ldyBlbmdpbmUob3B0aW9ucyk7XG59O1xuXG4vKipcbiAqIEBwYXJhbSAgeyhCdWZmZXJ8U3RyZWFtKX0gc291cmNlXG4gKiBAcGFyYW0gIHtaaXBFbnRyeURhdGF9IGRhdGFcbiAqIEBwYXJhbSAge1N0cmluZ30gZGF0YS5uYW1lIFNldHMgdGhlIGVudHJ5IG5hbWUgaW5jbHVkaW5nIGludGVybmFsIHBhdGguXG4gKiBAcGFyYW0gIHsoU3RyaW5nfERhdGUpfSBbZGF0YS5kYXRlPU5PVygpXSBTZXRzIHRoZSBlbnRyeSBkYXRlLlxuICogQHBhcmFtICB7TnVtYmVyfSBbZGF0YS5tb2RlPUQ6MDc1NS9GOjA2NDRdIFNldHMgdGhlIGVudHJ5IHBlcm1pc3Npb25zLlxuICogQHBhcmFtICB7U3RyaW5nfSBbZGF0YS5wcmVmaXhdIFNldHMgYSBwYXRoIHByZWZpeCBmb3IgdGhlIGVudHJ5IG5hbWUuIFVzZWZ1bFxuICogd2hlbiB3b3JraW5nIHdpdGggbWV0aG9kcyBsaWtlIGBkaXJlY3RvcnlgIG9yIGBnbG9iYC5cbiAqIEBwYXJhbSAge2ZzLlN0YXRzfSBbZGF0YS5zdGF0c10gU2V0cyB0aGUgZnMgc3RhdCBkYXRhIGZvciB0aGlzIGVudHJ5IGFsbG93aW5nXG4gKiBmb3IgcmVkdWN0aW9uIG9mIGZzIHN0YXQgY2FsbHMgd2hlbiBzdGF0IGRhdGEgaXMgYWxyZWFkeSBrbm93bi5cbiAqIEBwYXJhbSAge0Jvb2xlYW59IFtkYXRhLnN0b3JlPVppcE9wdGlvbnMuc3RvcmVdIFNldHMgdGhlIGNvbXByZXNzaW9uIG1ldGhvZCB0byBTVE9SRS5cbiAqIEBwYXJhbSAge0Z1bmN0aW9ufSBjYWxsYmFja1xuICogQHJldHVybiB2b2lkXG4gKi9cblppcC5wcm90b3R5cGUuYXBwZW5kID0gZnVuY3Rpb24oc291cmNlLCBkYXRhLCBjYWxsYmFjaykge1xuICB0aGlzLmVuZ2luZS5lbnRyeShzb3VyY2UsIGRhdGEsIGNhbGxiYWNrKTtcbn07XG5cbi8qKlxuICogQHJldHVybiB2b2lkXG4gKi9cblppcC5wcm90b3R5cGUuZmluYWxpemUgPSBmdW5jdGlvbigpIHtcbiAgdGhpcy5lbmdpbmUuZmluYWxpemUoKTtcbn07XG5cbi8qKlxuICogQHJldHVybiB0aGlzLmVuZ2luZVxuICovXG5aaXAucHJvdG90eXBlLm9uID0gZnVuY3Rpb24oKSB7XG4gIHJldHVybiB0aGlzLmVuZ2luZS5vbi5hcHBseSh0aGlzLmVuZ2luZSwgYXJndW1lbnRzKTtcbn07XG5cbi8qKlxuICogQHJldHVybiB0aGlzLmVuZ2luZVxuICovXG5aaXAucHJvdG90eXBlLnBpcGUgPSBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHRoaXMuZW5naW5lLnBpcGUuYXBwbHkodGhpcy5lbmdpbmUsIGFyZ3VtZW50cyk7XG59O1xuXG4vKipcbiAqIEByZXR1cm4gdGhpcy5lbmdpbmVcbiAqL1xuWmlwLnByb3RvdHlwZS51bnBpcGUgPSBmdW5jdGlvbigpIHtcbiAgcmV0dXJuIHRoaXMuZW5naW5lLnVucGlwZS5hcHBseSh0aGlzLmVuZ2luZSwgYXJndW1lbnRzKTtcbn07XG5cbm1vZHVsZS5leHBvcnRzID0gWmlwO1xuXG4vKipcbiAqIEB0eXBlZGVmIHtPYmplY3R9IFppcE9wdGlvbnNcbiAqIEBnbG9iYWxcbiAqIEBwcm9wZXJ0eSB7U3RyaW5nfSBbY29tbWVudF0gU2V0cyB0aGUgemlwIGFyY2hpdmUgY29tbWVudC5cbiAqIEBwcm9wZXJ0eSB7Qm9vbGVhbn0gW2ZvcmNlTG9jYWxUaW1lPWZhbHNlXSBGb3JjZXMgdGhlIGFyY2hpdmUgdG8gY29udGFpbiBsb2NhbCBmaWxlIHRpbWVzIGluc3RlYWQgb2YgVVRDLlxuICogQHByb3BlcnR5IHtCb29sZWFufSBbZm9yY2VaaXA2ND1mYWxzZV0gRm9yY2VzIHRoZSBhcmNoaXZlIHRvIGNvbnRhaW4gWklQNjQgaGVhZGVycy5cbiAqIEBwcnBwZXJ0eSB7Qm9vbGVhbn0gW25hbWVQcmVwZW5kU2xhc2g9ZmFsc2VdIFByZXBlbmRzIGEgZm9yd2FyZCBzbGFzaCB0byBhcmNoaXZlIGZpbGUgcGF0aHMuXG4gKiBAcHJvcGVydHkge0Jvb2xlYW59IFtzdG9yZT1mYWxzZV0gU2V0cyB0aGUgY29tcHJlc3Npb24gbWV0aG9kIHRvIFNUT1JFLlxuICogQHByb3BlcnR5IHtPYmplY3R9IFt6bGliXSBQYXNzZWQgdG8gW3psaWJde0BsaW5rIGh0dHBzOi8vbm9kZWpzLm9yZy9hcGkvemxpYi5odG1sI3psaWJfY2xhc3Nfb3B0aW9uc31cbiAqIHRvIGNvbnRyb2wgY29tcHJlc3Npb24uXG4gKiBAcHJvcGVydHkgeyp9IFsqXSBTZWUgW3ppcC1zdHJlYW1de0BsaW5rIGh0dHBzOi8vYXJjaGl2ZXJqcy5jb20vemlwLXN0cmVhbS9aaXBTdHJlYW0uaHRtbH0gZG9jdW1lbnRhdGlvbiBmb3IgY3VycmVudCBsaXN0IG9mIHByb3BlcnRpZXMuXG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiB7T2JqZWN0fSBaaXBFbnRyeURhdGFcbiAqIEBnbG9iYWxcbiAqIEBwcm9wZXJ0eSB7U3RyaW5nfSBuYW1lIFNldHMgdGhlIGVudHJ5IG5hbWUgaW5jbHVkaW5nIGludGVybmFsIHBhdGguXG4gKiBAcHJvcGVydHkgeyhTdHJpbmd8RGF0ZSl9IFtkYXRlPU5PVygpXSBTZXRzIHRoZSBlbnRyeSBkYXRlLlxuICogQHByb3BlcnR5IHtOdW1iZXJ9IFttb2RlPUQ6MDc1NS9GOjA2NDRdIFNldHMgdGhlIGVudHJ5IHBlcm1pc3Npb25zLlxuICogQHByb3BlcnR5IHtCb29sZWFufSBbbmFtZVByZXBlbmRTbGFzaD1aaXBPcHRpb25zLm5hbWVQcmVwZW5kU2xhc2hdIFByZXBlbmRzIGEgZm9yd2FyZCBzbGFzaCB0byBhcmNoaXZlIGZpbGUgcGF0aHMuXG4gKiBAcHJvcGVydHkge1N0cmluZ30gW3ByZWZpeF0gU2V0cyBhIHBhdGggcHJlZml4IGZvciB0aGUgZW50cnkgbmFtZS4gVXNlZnVsXG4gKiB3aGVuIHdvcmtpbmcgd2l0aCBtZXRob2RzIGxpa2UgYGRpcmVjdG9yeWAgb3IgYGdsb2JgLlxuICogQHByb3BlcnR5IHtmcy5TdGF0c30gW3N0YXRzXSBTZXRzIHRoZSBmcyBzdGF0IGRhdGEgZm9yIHRoaXMgZW50cnkgYWxsb3dpbmdcbiAqIGZvciByZWR1Y3Rpb24gb2YgZnMgc3RhdCBjYWxscyB3aGVuIHN0YXQgZGF0YSBpcyBhbHJlYWR5IGtub3duLlxuICogQHByb3BlcnR5IHtCb29sZWFufSBbc3RvcmU9WmlwT3B0aW9ucy5zdG9yZV0gU2V0cyB0aGUgY29tcHJlc3Npb24gbWV0aG9kIHRvIFNUT1JFLlxuICovXG5cbi8qKlxuICogWmlwU3RyZWFtIE1vZHVsZVxuICogQGV4dGVybmFsIFppcFN0cmVhbVxuICogQHNlZSB7QGxpbmsgaHR0cHM6Ly93d3cuYXJjaGl2ZXJqcy5jb20vemlwLXN0cmVhbS9aaXBTdHJlYW0uaHRtbH1cbiAqL1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/archiver/lib/plugins/zip.js\n");

/***/ })

};
;