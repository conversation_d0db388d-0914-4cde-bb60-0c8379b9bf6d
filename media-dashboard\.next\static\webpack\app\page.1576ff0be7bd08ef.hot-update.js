"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(true);\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            const checkUserAndRedirect = {\n                \"Home.useEffect.checkUserAndRedirect\": async ()=>{\n                    try {\n                        // التحقق من وجود المستخدم في localStorage\n                        const savedUser = localStorage.getItem('user');\n                        const token = localStorage.getItem('token');\n                        if (!savedUser || !token) {\n                            // إذا لم يكن المستخدم مسجل دخول، توجيه لصفحة تسجيل الدخول\n                            router.replace('/login');\n                        } else {\n                            // تأخير إعادة التوجيه لإظهار الأزرار\n                            setTimeout({\n                                \"Home.useEffect.checkUserAndRedirect\": ()=>{\n                                    router.replace('/dashboard');\n                                }\n                            }[\"Home.useEffect.checkUserAndRedirect\"], 1500);\n                        }\n                    } catch (error) {\n                        console.error('Error checking user:', error);\n                        router.replace('/login');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"Home.useEffect.checkUserAndRedirect\"];\n            checkUserAndRedirect();\n        }\n    }[\"Home.useEffect\"], [\n        router\n    ]);\n    // عرض شاشة التحميل أثناء التحقق من المستخدم\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #1a1d29 0%, #2d3748 50%, #1a1d29 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr',\n                padding: '20px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    color: 'white'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '3rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.5rem'\n                        },\n                        children: t('home.loading')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            background: 'linear-gradient(135deg, #1a1d29 0%, #2d3748 50%, #1a1d29 100%)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            fontFamily: 'Cairo, Arial, sans-serif',\n            direction: isRTL ? 'rtl' : 'ltr',\n            padding: '20px'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                color: 'white',\n                width: '100%',\n                maxWidth: '1400px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginBottom: '30px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Logo, {\n                        size: \"large\",\n                        style: {\n                            fontSize: '2rem'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: '3rem',\n                        marginBottom: '20px'\n                    },\n                    children: \"⏳\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        fontSize: '1.5rem',\n                        marginBottom: '20px'\n                    },\n                    children: t('home.loading')\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',\n                        gap: '20px',\n                        justifyContent: 'center',\n                        marginBottom: '30px',\n                        maxWidth: '1200px',\n                        margin: '0 auto 30px auto',\n                        padding: '0 20px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/dashboard'),\n                            style: {\n                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #8b5cf6 100%)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '20px',\n                                padding: '25px 20px',\n                                cursor: 'pointer',\n                                fontSize: '1.1rem',\n                                fontWeight: 'bold',\n                                transition: 'all 0.3s ease',\n                                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)',\n                                display: 'flex',\n                                flexDirection: 'column',\n                                alignItems: 'center',\n                                gap: '10px',\n                                minHeight: '120px',\n                                position: 'relative',\n                                overflow: 'hidden'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';\n                                e.currentTarget.style.boxShadow = '0 15px 35px rgba(102, 126, 234, 0.6)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 126, 234, 0.4)';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))'\n                                    },\n                                    children: \"\\uD83D\\uDCCA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('home.dashboard')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 13\n                        }, this),\n                        hasPermission('SCHEDULE_READ') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/daily-schedule'),\n                            style: {\n                                background: 'linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '20px',\n                                padding: '25px 20px',\n                                cursor: 'pointer',\n                                fontSize: '1.1rem',\n                                fontWeight: 'bold',\n                                transition: 'all 0.3s ease',\n                                boxShadow: '0 8px 25px rgba(16, 185, 129, 0.4)',\n                                display: 'flex',\n                                flexDirection: 'column',\n                                alignItems: 'center',\n                                gap: '10px',\n                                minHeight: '120px',\n                                position: 'relative',\n                                overflow: 'hidden'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';\n                                e.currentTarget.style.boxShadow = '0 15px 35px rgba(16, 185, 129, 0.6)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                e.currentTarget.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.4)';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))'\n                                    },\n                                    children: \"\\uD83D\\uDCCB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('home.dailySchedule')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 15\n                        }, this),\n                        hasPermission('SCHEDULE_READ') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/weekly-schedule'),\n                            style: {\n                                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '20px',\n                                padding: '25px 20px',\n                                cursor: 'pointer',\n                                fontSize: '1.1rem',\n                                fontWeight: 'bold',\n                                transition: 'all 0.3s ease',\n                                boxShadow: '0 8px 25px rgba(245, 158, 11, 0.4)',\n                                display: 'flex',\n                                flexDirection: 'column',\n                                alignItems: 'center',\n                                gap: '10px',\n                                minHeight: '120px',\n                                position: 'relative',\n                                overflow: 'hidden'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';\n                                e.currentTarget.style.boxShadow = '0 15px 35px rgba(245, 158, 11, 0.6)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                e.currentTarget.style.boxShadow = '0 8px 25px rgba(245, 158, 11, 0.4)';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))'\n                                    },\n                                    children: \"\\uD83D\\uDCC5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('home.weeklySchedule')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 15\n                        }, this),\n                        hasPermission('MEDIA_READ') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/media-list'),\n                            style: {\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '20px',\n                                padding: '25px 20px',\n                                cursor: 'pointer',\n                                fontSize: '1.1rem',\n                                fontWeight: 'bold',\n                                transition: 'all 0.3s ease',\n                                boxShadow: '0 8px 25px rgba(239, 68, 68, 0.4)',\n                                display: 'flex',\n                                flexDirection: 'column',\n                                alignItems: 'center',\n                                gap: '10px',\n                                minHeight: '120px',\n                                position: 'relative',\n                                overflow: 'hidden'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';\n                                e.currentTarget.style.boxShadow = '0 15px 35px rgba(239, 68, 68, 0.6)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                e.currentTarget.style.boxShadow = '0 8px 25px rgba(239, 68, 68, 0.4)';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))'\n                                    },\n                                    children: \"\\uD83C\\uDFAC\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('home.mediaList')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, this),\n                        hasPermission('MEDIA_CREATE') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/add-media'),\n                            style: {\n                                background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffc107 100%)',\n                                color: '#1a1d29',\n                                border: 'none',\n                                borderRadius: '20px',\n                                padding: '25px 20px',\n                                cursor: 'pointer',\n                                fontSize: '1.1rem',\n                                fontWeight: 'bold',\n                                transition: 'all 0.3s ease',\n                                boxShadow: '0 8px 25px rgba(255, 215, 0, 0.4)',\n                                display: 'flex',\n                                flexDirection: 'column',\n                                alignItems: 'center',\n                                gap: '10px',\n                                minHeight: '120px',\n                                position: 'relative',\n                                overflow: 'hidden'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';\n                                e.currentTarget.style.boxShadow = '0 15px 35px rgba(255, 215, 0, 0.6)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 215, 0, 0.4)';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        filter: 'drop-shadow(0 0 10px rgba(0,0,0,0.3))'\n                                    },\n                                    children: \"➕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('home.addMedia')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 15\n                        }, this),\n                        (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/admin-dashboard'),\n                            style: {\n                                background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '20px',\n                                padding: '25px 20px',\n                                cursor: 'pointer',\n                                fontSize: '1.1rem',\n                                fontWeight: 'bold',\n                                transition: 'all 0.3s ease',\n                                boxShadow: '0 8px 25px rgba(139, 92, 246, 0.4)',\n                                display: 'flex',\n                                flexDirection: 'column',\n                                alignItems: 'center',\n                                gap: '10px',\n                                minHeight: '120px',\n                                position: 'relative',\n                                overflow: 'hidden'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(-5px) scale(1.02)';\n                                e.currentTarget.style.boxShadow = '0 15px 35px rgba(139, 92, 246, 0.6)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.transform = 'translateY(0) scale(1)';\n                                e.currentTarget.style.boxShadow = '0 8px 25px rgba(139, 92, 246, 0.4)';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '3rem',\n                                        filter: 'drop-shadow(0 0 10px rgba(255,255,255,0.5))'\n                                    },\n                                    children: \"\\uD83D\\uDC65\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('home.adminPanel')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        color: '#a0aec0',\n                        fontSize: '0.9rem',\n                        marginTop: '10px'\n                    },\n                    children: t('home.autoRedirect')\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 325,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"fAIVTFSUaS80h8n8iN8PXmaGl+Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});