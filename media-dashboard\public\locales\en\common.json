{"navigation": {"dashboard": "Dashboard", "mediaList": "Media List", "addMedia": "Add Media", "weeklySchedule": "Weekly Schedule", "dailySchedule": "Daily Schedule", "reports": "Reports", "unifiedSystem": "Import/Export", "adminDashboard": "Users", "statistics": "Statistics"}, "common": {"welcome": "Welcome", "loading": "Loading...", "loadingSchedule": "Loading weekly schedule...", "loadingData": "Loading data...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "yes": "Yes", "no": "No", "ok": "OK", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "select": "Select", "selectDate": "Select Date", "selectTime": "Select Time", "actions": "Actions", "status": "Status", "type": "Type", "name": "Name", "description": "Description", "duration": "Duration", "startTime": "Start Time", "endTime": "End Time", "date": "Date", "time": "Time", "content": "Content", "code": "Code", "episode": "Episode", "season": "Season", "part": "Part", "segments": "Segments", "available": "Available", "unavailable": "Unavailable", "active": "Active", "inactive": "Inactive", "valid": "<PERSON><PERSON>", "invalid": "Invalid", "expired": "Expired", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "total": "Total", "count": "Count", "items": "Items", "user": "User", "noData": "No Data Available", "noResults": "No Results Found", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information"}, "mediaTypes": {"ALL": "All Types", "PROGRAM": "Program", "SERIES": "Series", "FILM": "Film", "SONG": "Song", "PROMO": "Promo", "STING": "Sting", "FILLER": "Filler", "NEXT": "Next", "NOW": "Now", "MINI": "Mini", "CROSS": "Cross", "سنعود": "We'll Be Back", "عدنا": "We're Back"}, "roles": {"ADMIN": "System Administrator", "CONTENT_MANAGER": "Content Manager", "MEDIA_MANAGER": "Database Manager", "SCHEDULER": "Program Scheduler", "FULL_VIEWER": "Full View User", "DATA_ENTRY": "Data Entry", "MAP_SCHEDULER": "Map & Schedule Manager", "VIEWER": "Viewer", "EDITOR": "Editor", "OPERATOR": "Operator"}, "roleDescriptions": {"ADMIN": "Full system administration and user management permissions", "CONTENT_MANAGER": "Manage content and media materials", "MEDIA_MANAGER": "Manage media database", "SCHEDULER": "Create and edit program schedules", "FULL_VIEWER": "View all data and reports", "DATA_ENTRY": "Enter and edit basic data", "MAP_SCHEDULER": "Manage program map and schedules", "VIEWER": "View basic data only"}, "mediaStatus": {"ALL": "All Status", "VALID": "<PERSON><PERSON>", "REJECTED_CENSORSHIP": "Rejected - Censorship", "REJECTED_TECHNICAL": "Rejected - Technical", "EXPIRED": "Expired", "HOLD": "On Hold"}, "channels": {"DOCUMENTARY": "Documentary", "NEWS": "News", "OTHER": "Other"}, "dashboard": {"title": "Dashboard", "subtitle": "Media Management System", "totalMedia": "Total Media", "activeSchedules": "Active Schedules", "todayBroadcast": "Today's Broadcast", "systemUsers": "System Users", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "statistics": "Statistics", "overview": "Overview"}, "media": {"title": "Media Management", "addNew": "Add New Media", "list": "Media List", "details": "Media Details", "segments": "Segments", "addSegment": "Add Segment", "segmentCode": "Segment Code", "timeIn": "Time In", "timeOut": "Time Out", "hardDrive": "Hard Drive", "server": "Server", "notes": "Notes", "startDate": "Start Date", "endDate": "End Date", "showInTX": "Show in TX", "episodeNumber": "Episode Number", "seasonNumber": "Season Number", "partNumber": "Part Number", "totalSegments": "Total Segments", "validMedia": "Valid Media", "expiredMedia": "Expired Media", "pendingMedia": "Pending Media", "searchByName": "Search by name or description", "searchByCode": "Search by media or segment code", "searchPlaceholder": "Search for media...", "codePlaceholder": "Search by code...", "mediaType": "Media Type", "mediaStatus": "Status", "sortBy": "Sort By", "newest": "Newest First", "oldest": "Oldest First", "byName": "Name (A-Z)", "byType": "Type", "searchStats": "Showing {{filtered}} of {{total}} media items", "noMediaFound": "No media items found", "startAdding": "Start by adding a new media item", "exportExcel": "Export Excel", "exporting": "Exporting...", "searchAndFilter": "Search & Filter", "mediaOverview": "View and manage media content", "searchFilterExport": "You can search, filter and export data", "channel": "Channel", "segmentCount": "Segment Count", "description": "Description", "noCode": "[No Code]", "edit": "Edit", "delete": "Delete", "scrollToTop": "Back to top"}, "schedule": {"title": "Schedule Management", "weekly": "Weekly Program Schedule", "daily": "Daily Broadcast Schedule", "import": "Import Schedule", "export": "Export Schedule", "broadcast": "Broadcast", "rerun": "<PERSON><PERSON>", "prime": "Prime", "filler": "Filler", "empty": "Empty", "addRow": "Add Row", "deleteRow": "Delete Row", "moveUp": "Move Up", "moveDown": "Move Down", "showMap": "Show Map", "hideMap": "Hide Map", "saveChanges": "Save Changes", "discardChanges": "Discard Changes", "importFromTime": "Import from this time", "broadcastTime": "Broadcast Time", "programMap": "Program Map", "scheduleItems": "Schedule Items", "availableMedia": "Available Media", "scheduledMedia": "Today's Scheduled Programs", "weeklySchedule": "Weekly Program Schedule", "weeklySubtitle": "Weekly program scheduling", "importTitle": "Import Broadcast Schedule", "importSubtitle": "Import schedule from specific time", "importInstructions": "Select Date → Set Time → Import → Edit Media → Export Excel", "importSchedule": "Import Schedule", "importInstructionsLong": "Select date and time then click \"Import from this time\" to display the schedule", "loadingSchedule": "Loading weekly schedule...", "selectingDate": "Selecting date...", "mediaList": "Media List", "addTempMedia": "Add Temporary Media", "mediaName": "Media name...", "duration": "Duration (e.g.: 01:30:00)", "notes": "Notes (optional)...", "add": "Add", "updateReruns": "Update Reruns", "allTypes": "All Types", "searchMedia": "Search media...", "resultsCount": "{{count}} of {{total}} media", "noMedia": "No media found", "changeFilter": "Try changing filter or search", "addNewMedia": "Add new media from user page", "deleteTempMedia": "Delete temporary media", "liveProgram": "Live", "pendingDelivery": "Pending Delivery", "temporary": "Temporary", "broadcastSchedule": "Broadcast Schedule", "exportSchedule": "Export Schedule", "selectedWeek": "Selected Week", "previousWeek": "← Previous Week", "nextWeek": "Next Week →", "time": "Time", "rerunIndicator": "Rerun - can be deleted for editing", "hideSchedule": "Hide Schedule", "showSchedule": "Show Schedule", "unknown": "Unknown", "season": "Season", "episode": "Episode", "part": "Part", "confirmDelete": "Are you sure you want to delete {{type}}: \"{{name}}\"?", "deleteWarningOriginal": "Warning: Deleting original media will delete all its reruns", "deleteWarningRerun": "Warning: Deleting rerun will not affect original media", "deleteWarningTemp": "Temporary media will be deleted from schedule", "originalMaterial": "Original Material", "rerunMaterial": "<PERSON><PERSON>", "tempMaterial": "Temporary Media", "timeConflict": "Time conflict detected! Choose another time.", "enterMediaName": "Please enter media name", "confirmDeleteTemp": "Do you want to delete this temporary media?", "deleteMediaTitle": "🗑️ Delete Media:", "deleteOriginalInfo": "Original Media: Permanent deletion with all reruns", "deleteRerunInfo": "Reruns: Delete leaving field empty for editing", "deleteConfirmInfo": "Confirmation will appear before deletion", "confirmDeleteSegment": "Are you sure you want to delete this segment?", "usageInstructions": "📋 Usage Instructions:", "addMediaTitle": "🎯 Adding Media:", "addMediaInstruction1": "Drag media from the right panel to the schedule", "addMediaInstruction2": "🔄 Drag media within the schedule to copy to other time slots", "addMediaInstruction3": "🎬 Use type filter to filter by media type", "addMediaInstruction4": "🔍 Use search to find media quickly", "primeTimeTitle": "🌟 Original Media (Prime Time):", "primeTimeSchedule1": "Sunday-Wednesday: 18:00-00:00", "primeTimeSchedule2": "Thursday-Saturday: 18:00-02:00", "primeTimeColor": "🟡 Golden color in schedule", "rerunsTitle": "♻️ Automatic Reruns (Two Parts):", "rerunsSchedule1": "Sunday-Wednesday:", "rerunsSchedule2": "Thursday-Saturday:", "rerunsPart1Sun": "Part 1: Same column 00:00-07:59", "rerunsPart2Sun": "Part 2: Next column 08:00-17:59", "rerunsPart1Thu": "Part 1: Same column 02:00-07:59", "rerunsPart2Thu": "Part 2: Next column 08:00-17:59", "rerunsColor": "🔘 Gray color - can be deleted for editing", "dateManagementTitle": "📅 Date Management:", "dateManagementInfo": "Use calendar and buttons to navigate between weeks • Each week is saved separately", "importantNoteTitle": "💡 Important Note:", "importantNoteInfo": "When adding few media items (1-3 items) in prime time, they will appear with time gaps in reruns to avoid excessive repetition. Add more media for greater variety.", "weeklyScheduleTitle": "Weekly Schedule", "noWeeklyData": "No data available for weekly schedule", "types": {"program": "Program", "series": "Series", "film": "Film", "song": "Song", "sting": "Sting", "fillIn": "Fill In", "filler": "Filler", "promo": "Promo", "next": "Next", "now": "Now", "snawod": "We'll Be Back", "odna": "We're Back", "mini": "Mini", "cross": "Cross"}, "startTime": "Start Time"}, "days": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "loginButton": "Sign In", "loginError": "<PERSON><PERSON>", "accessDenied": "Access denied", "createUserError": "Error creating user", "deleteUserSuccess": "User deleted successfully!", "deleteUserError": "Error deleting user", "fillRequiredFields": "Please fill all required fields", "updateUserSuccess": "User updated successfully!", "updateUserError": "Error updating user", "fullName": "Full Name", "email": "Email", "phone": "Phone", "role": "Role", "permissions": "Permissions", "allPermissions": "All Permissions", "manageMedia": "Manage Media", "manageSchedules": "Manage Schedules", "viewMedia": "View Media", "viewSchedules": "View Schedules", "viewMap": "View Map", "viewBroadcast": "View Broadcast", "manageMap": "Manage Map", "roles": {"admin": "System Administrator", "contentManager": "Content Manager", "mediaManager": "Media Manager", "scheduler": "Scheduler", "fullViewer": "Full View User", "dataEntry": "Data Entry", "mapScheduler": "Map & Schedule Manager", "viewer": "Viewer", "adminDesc": "Full permissions for all system parts + user management", "contentManagerDesc": "Full media and schedule management (without user management)", "mediaManagerDesc": "Media management only (add, edit, delete)", "schedulerDesc": "Broadcast schedules and program map management only", "fullViewerDesc": "View entire application without editing or adding capabilities", "dataEntryDesc": "Data entry and editing only without viewing other parts of the application", "mapSchedulerDesc": "Map and daily broadcast schedule management with database viewing without editing", "viewerDesc": "View content only without editing or adding capabilities"}, "invalidCredentials": "Invalid credentials", "welcomeBack": "Welcome back", "pleaseLogin": "Please login to continue", "userRoles": "User Roles", "adminDesc": "Full permissions", "contentManagerDesc": "Media management", "schedulerDesc": "Schedule management", "viewerDesc": "View only", "sessionExpired": "Session expired", "insufficientPermissions": "Insufficient permissions", "status": {"valid": "<PERSON><PERSON>", "rejectedCensorship": "Rejected - Censorship", "rejectedTechnical": "Rejected - Technical", "waiting": "Waiting"}, "segments": "segments"}, "admin": {"title": "User Management", "subtitle": "Add and edit users", "users": "Users", "permissions": "Permissions", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "userRole": "User Role", "userStatus": "User Status", "lastLogin": "Last Login", "createdAt": "Created At", "updatedAt": "Updated At", "activeUsers": "Active Users", "inactiveUsers": "Inactive Users", "totalUsers": "Total Users", "loadingData": "Loading data...", "userManagement": "User Management", "addNewUser": "Add New User", "username": "Username", "password": "Password", "fullName": "Full Name", "email": "Email", "phone": "Phone", "role": "Role", "status": "Status", "actions": "Actions", "active": "Active", "inactive": "Inactive", "createUser": "Create User", "cancel": "Cancel", "saveChanges": "Save Changes", "edit": "Edit", "delete": "Delete", "noUsers": "No users found", "addUsersMessage": "Add new users using the \"Add New User\" button", "rolesExplanation": "Roles and Permissions Explanation", "allPermissions": "All Permissions", "mediaManagement": "Media Management", "scheduleManagement": "Schedule Management", "viewMedia": "View Media", "viewSchedules": "View Schedules", "viewMap": "View Map", "viewBroadcast": "View Broadcast", "mapManagement": "Map Management", "permissionsLabel": "Permissions:", "noLoginYet": "No login yet", "editingUser": "Edit User", "passwordNote": "(leave empty to keep current)", "confirmDelete": "Are you sure you want to delete this user?", "userCreated": "User created successfully!", "userUpdated": "User updated successfully!", "userDeleted": "User deleted successfully!", "fillRequired": "Please fill all required fields", "createError": "Error creating user", "updateError": "Error updating user", "deleteError": "Error deleting user", "fetchError": "Error fetching user data", "serverError": "Server connection error", "roles": {"admin": "System Administrator", "contentManager": "Content Manager", "mediaManager": "Media Manager", "scheduler": "Program Scheduler", "fullViewer": "Full Viewer", "dataEntry": "Data Entry", "mapScheduler": "Map & Schedule Manager", "viewer": "Viewer", "adminDesc": "Full system administration and user management permissions", "contentManagerDesc": "Manage content and media materials", "mediaManagerDesc": "Manage media database", "schedulerDesc": "Create and edit program schedules", "fullViewerDesc": "View all data and reports", "dataEntryDesc": "Enter and edit basic data", "mapSchedulerDesc": "Manage program map and schedules", "viewerDesc": "View basic data only"}}, "permissions": {"MEDIA_READ": "Read Media", "MEDIA_CREATE": "Create Media", "MEDIA_UPDATE": "Update Media", "MEDIA_DELETE": "Delete Media", "SCHEDULE_READ": "Read Schedules", "SCHEDULE_CREATE": "Create Schedules", "SCHEDULE_UPDATE": "Update Schedules", "SCHEDULE_DELETE": "Delete Schedules", "USER_MANAGEMENT": "User Management", "SYSTEM_ADMIN": "System Administration"}, "stats": {"totalMedia": "Total Media", "validMedia": "Valid Media", "maintenanceMedia": "Under Maintenance", "activeUsers": "Active Users", "efficiency": "Overall Efficiency", "operationalCost": "Operational Cost", "processingTime": "Average Processing Time", "activeOperations": "Active Operations", "growthRate": "Growth Rate", "healthRate": "Health Rate", "issueRate": "Issue Rate", "activityRate": "Activity Rate", "improvement": "Improvement", "dailyAverage": "Daily Average", "loadingDummyData": "Loading dummy data"}, "messages": {"success": {"mediaAdded": "Media added successfully", "mediaUpdated": "Media updated successfully", "mediaDeleted": "Media deleted successfully", "exportSuccess": "Export completed successfully", "importSuccess": "Import completed successfully", "scheduleUpdated": "Schedule updated successfully", "userCreated": "User created successfully", "userUpdated": "User updated successfully", "userDeleted": "User deleted successfully", "changesSaved": "Changes saved successfully"}, "error": {"serverConnection": "Server connection error", "mediaNotFound": "Media not found", "invalidData": "Invalid data", "permissionDenied": "Permission denied", "exportFailed": "Export failed", "importFailed": "Import failed", "unknownError": "Unknown error occurred", "timeFormatError": "Time format error", "calculationError": "Calculation error"}, "info": {"loading": "Loading...", "saving": "Saving...", "processing": "Processing...", "exporting": "Exporting...", "importing": "Importing..."}, "admin": {"title": "User Management", "subtitle": "Manage system users and permissions", "loadingData": "Loading data...", "userManagement": "User Management", "addNewUser": "Add New User", "username": "Username", "password": "Password", "fullName": "Full Name", "email": "Email", "phone": "Phone", "role": "Role", "createUser": "Create User", "cancel": "Cancel", "lastLogin": "Last Login", "actions": "Actions", "noLoginYet": "No login yet", "confirmDelete": "Are you sure you want to delete this user?", "noUsers": "No users found", "addUsersMessage": "Start by adding new users to the system", "rolesExplanation": "Roles and Permissions", "permissions": "Permissions", "allPermissions": "All Permissions", "manageMedia": "Manage Media", "manageSchedules": "Manage Schedules", "viewSchedules": "View Schedules", "viewMedia": "View Media", "viewMap": "View Map", "viewBroadcast": "View Broadcast", "manageMap": "Manage Map", "editingUser": "Editing User", "passwordNote": "(leave empty to keep current)", "status": "Status", "active": "Active", "inactive": "Inactive", "saveChanges": "Save Changes", "roles": {"admin": "Administrator", "contentManager": "Content Manager", "mediaManager": "Media Manager", "scheduler": "Scheduler", "fullViewer": "Full Viewer", "dataEntry": "Data Entry", "mapScheduler": "Map Scheduler", "viewer": "Viewer", "adminDesc": "Full system access and user management", "contentManagerDesc": "Manage media content and schedules", "mediaManagerDesc": "Manage media library and content", "schedulerDesc": "Create and manage broadcast schedules", "fullViewerDesc": "View all system data and reports", "dataEntryDesc": "Add and edit media content", "mapSchedulerDesc": "Manage program maps and schedules", "viewerDesc": "Basic viewing permissions"}}}, "home": {"title": "Prime-X", "subtitle": "Media Management System", "loading": "Loading...", "autoRedirect": "Or wait for automatic redirect to dashboard", "quickNavigation": "Quick Navigation", "dashboard": "Dashboard", "dailySchedule": "Daily Broadcast Schedule", "weeklySchedule": "Program Schedule", "mediaList": "Media List", "addMedia": "Add Media", "adminPanel": "User Management"}, "reports": {"title": "Broadcast Reports", "subtitle": "Search and analyze broadcast content", "searchFilters": "Search Filters", "mediaType": "Media Type", "allTypes": "All Types", "mediaName": "Media Name", "mediaCode": "Media Code", "source": "Source", "both": "Both", "weekly": "Weekly Schedule", "daily": "Daily Schedule", "dateFrom": "From Date", "dateTo": "To Date", "search": "Search", "exportExcel": "Export Excel", "showStatistics": "Show Statistics", "hideStatistics": "Hide Statistics", "searchResults": "Search Results", "noResults": "No results found", "searchMessage": "Use the filters above to search for broadcast content", "resultsCount": "{{count}} results", "statistics": "Media Statistics", "totalItems": "Total Items", "totalDuration": "Total Duration", "count": "Count", "duration": "Duration", "percentage": "Percentage", "loadingMedia": "Loading media...", "searchError": "Search error", "exportError": "Export error", "exportSuccess": "Export successful", "date": "Date", "time": "Time", "type": "Type", "name": "Name", "code": "Code", "rerun": "<PERSON><PERSON>", "temporary": "Temporary", "original": "Original", "programsFilmsSeries": "Programs, Films & Series", "promosFillersSting": "Promos, Fillers & Stings", "detailedStatistics": "Detailed Statistics"}, "unified": {"title": "Unified Import/Export System", "subtitle": "Manage import and export of media data", "importExport": "Import/Export", "selectFile": "Please select a file", "uploadFile": "Upload File", "exportData": "Export Data", "fileSelected": "File Selected", "processing": "Processing...", "success": "Operation Successful", "error": "An Error Occurred", "noFileSelected": "No File Selected", "invalidFileType": "Invalid File Type", "mediaLibrary": "Media Library", "totalMedia": "Total Media", "searchMedia": "Search media...", "filterByType": "Filter by Type", "loadingMedia": "Loading media...", "importSuccess": "Import successful", "itemsImported": "items imported", "importError": "Import error occurred", "exportSuccess": "File exported successfully", "exportError": "Export error", "noMedia": "No media available", "result": "Result", "searchDescription": "Search the program schedule and daily broadcast schedule easily", "noResultsMessage": "No results to display. Please set search criteria and click \"Search & Statistics\".", "searchAndStats": "Search & Statistics", "dailyScheduleHint": "Choose \"Daily Schedule\" to search for promos, fillers, and stings", "broadcastDayHint": "Broadcast day starts at 08:00 AM", "broadcastDayEndHint": "Broadcast day ends at 07:59 AM next day", "date": "Date", "time": "Time", "type": "Type", "name": "Name", "code": "Code", "rerun": "<PERSON><PERSON>", "temporary": "Temporary"}, "statistics": {"title": "System Statistics", "subtitle": "Detailed reports and statistics", "loadingStats": "Loading statistics...", "totalMedia": "Total Media", "allRegisteredMedia": "All registered media", "totalSegments": "Total Segments", "allSegments": "All segments", "differentTypes": "Different Types", "mediaTypes": "Media types", "averageSegments": "Average Segments", "perMedia": "Per media", "distributionByType": "Distribution by Type", "distributionByStatus": "Distribution by Status", "recentlyAdded": "Recently Added", "status": {"valid": "Valid for broadcast", "rejectedCensorship": "Rejected (Censorship) - Content review needed", "rejectedTechnical": "Rejected (Technical) - Quality issues", "waiting": "Awaiting review"}}, "addMedia": {"title": "Add New Media", "subtitle": "After saving, you'll stay on this page to add another media", "basicInfo": "Basic Information", "hardDiskNumber": "Hard Disk Number", "selectType": "Select Type", "channel": "Channel", "selectChannel": "Select Channel", "selectStatus": "Select Status", "source": "Source", "startDate": "Start Date", "endDate": "End Date", "segments": "Segments", "addSegment": "Add Segment", "segment": "Segment", "segmentCode": "Segment Code", "saveAndAddNew": "Save and Add New", "clearFields": "Clear Fields", "description": "Media Description", "notes": "Notes", "additionalNotes": "Additional notes", "showInSchedule": "Show in Schedule and Broadcast Lists", "txDescription": "When enabled, this media will appear in the sidebar of schedule and broadcast tables", "episodeNumber": "Episode Number", "seasonNumber": "Season Number", "partNumber": "Part Number", "durationAutoCalculated": "Duration (Auto-calculated)"}}