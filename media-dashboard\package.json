{"name": "media-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:mobile": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "start:mobile": "next start -H 0.0.0.0", "lint": "echo 'ESLint disabled'", "lint:fix": "echo 'ESLint disabled'", "type-check": "echo 'TypeScript check disabled'", "clean": "powershell -Command \"Remove-Item -Recurse -Force .next, out -ErrorAction SilentlyContinue\""}, "dependencies": {"@prisma/client": "^6.9.0", "@types/xlsx": "^0.0.35", "exceljs": "^4.4.0", "i18next": "^25.3.2", "next": "15.3.3", "next-i18next": "^15.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.6.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "20.19.0", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.9.0", "tailwindcss": "^4", "typescript": "5.8.3"}}