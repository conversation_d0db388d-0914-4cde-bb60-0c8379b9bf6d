'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { AuthGuard } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useTranslatedToast } from '@/hooks/useTranslatedToast';

interface Segment {
  id: number;
  segmentCode: string;
  timeIn: string;
  timeOut: string;
  duration: string;
}

export default function EditMediaPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const mediaId = searchParams.get('id');
  const { showErrorToast, showSuccessToast, ToastContainer } = useTranslatedToast();

  const [formData, setFormData] = useState({
    name: '',
    type: '',
    description: '',
    channel: '',
    source: '',
    status: '',
    startDate: '',
    endDate: '',
    notes: '',
    episodeNumber: '',
    seasonNumber: '',
    partNumber: '',
    hardDiskNumber: '',
    showInTX: false, // Add TX field
  });

  const [segments, setSegments] = useState<Segment[]>([
    {
      id: 1,
      segmentCode: 'SEG001',
      timeIn: '00:00:00',
      timeOut: '', // Empty value as in add media page
      duration: '' // Empty value as in add media page
    }
  ]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  // جلب بيانات المادة للتعديل
  useEffect(() => {
    if (mediaId) {
      fetchMediaData();
    } else {
      router.push('/media-list');
    }
  }, [mediaId]);

  const fetchMediaData = async () => {
    try {
      const response = await fetch(`/api/media?id=${mediaId}`);
      const result = await response.json();

      if (result.success && result.data) {
        const media = result.data;
        setFormData({
          name: media.name || '',
          type: media.type || '',
          description: media.description || '',
          channel: media.channel || '',
          source: media.source || '',
          status: media.status || '',
          startDate: media.startDate ? media.startDate.split('T')[0] : '',
          endDate: media.endDate ? media.endDate.split('T')[0] : '',
          notes: media.notes || '',
          episodeNumber: media.episodeNumber?.toString() || '',
          seasonNumber: media.seasonNumber?.toString() || '',
          partNumber: media.partNumber?.toString() || '',
          hardDiskNumber: media.hardDiskNumber || '',
          showInTX: media.showInTX || false,
        });

        if (media.segments && media.segments.length > 0) {
          // تنسيق بيانات السيجمنت وحساب المدة إذا كانت ناقصة
          const formattedSegments = media.segments.map((seg: any, index: number) => {
            // تنسيق الأوقات
            const timeIn = seg.timeIn || '00:00:00';
            let timeOut = seg.timeOut || '';
            
            // إذا كان وقت النهاية فارغاً، نضع وقت افتراضي (وقت البداية + 10 دقائق)
            if (!timeOut || timeOut === '') {
              const timeInParts = timeIn.split(':').map(Number);
              let minutes = timeInParts[1] + 10;
              let hours = timeInParts[0];
              
              if (minutes >= 60) {
                hours = (hours + Math.floor(minutes / 60)) % 24;
                minutes = minutes % 60;
              }
              
              timeOut = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${timeInParts[2].toString().padStart(2, '0')}`;
            }
            
            // حساب المدة إذا كانت ناقصة
            let duration = seg.duration || '';
            if (!duration || duration === '') {
              const timeInParts = timeIn.split(':').map(Number);
              const timeOutParts = timeOut.split(':').map(Number);
              
              const inSeconds = timeInParts[0] * 3600 + timeInParts[1] * 60 + (timeInParts[2] || 0);
              const outSeconds = timeOutParts[0] * 3600 + timeOutParts[1] * 60 + (timeOutParts[2] || 0);
              
              let durationSeconds = outSeconds - inSeconds;
              if (durationSeconds < 0) {
                durationSeconds += 24 * 3600;
              }
              
              const hours = Math.floor(durationSeconds / 3600);
              const minutes = Math.floor((durationSeconds % 3600) / 60);
              const seconds = durationSeconds % 60;
              
              duration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            
            return {
              id: index + 1,
              segmentCode: seg.segmentCode || seg.code || '',
              timeIn: timeIn,
              timeOut: timeOut,
              duration: duration
            };
          });
          
          setSegments(formattedSegments);
          console.log('✅ تم تحميل بيانات السيجمنت:', formattedSegments);
        }
      } else {
        showErrorToast('mediaNotFound');
        router.push('/media-list');
      }
    } catch (error) {
      console.error('Error fetching media:', error);
      showErrorToast('serverConnection');
      router.push('/media-list');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AuthGuard requiredPermissions={['MEDIA_UPDATE']}>
        <DashboardLayout title="Update Media" subtitle="Loading data..." icon="✏️">
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <div style={{ fontSize: '2rem', marginBottom: '20px' }}>⏳</div>
            <div style={{ color: '#a0aec0' }}>Loading media data...</div>
          </div>
        </DashboardLayout>
      </AuthGuard>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // التحقق من صحة بيانات السيجمنت قبل الإرسال
      const validSegments = segments.map(seg => {
        // إذا كان وقت النهاية فارغاً أو 00:00:00، نضع قيمة افتراضية
        let timeOut = seg.timeOut;
        if (!timeOut || timeOut === '00:00:00') {
          // نضع وقت نهاية افتراضي بعد وقت البداية بساعة واحدة
          const timeInParts = seg.timeIn.split(':').map(Number);
          let hours = timeInParts[0] + 1;
          if (hours >= 24) hours -= 24;
          timeOut = `${hours.toString().padStart(2, '0')}:${timeInParts[1].toString().padStart(2, '0')}:${timeInParts[2].toString().padStart(2, '0')}`;
        }
        
        // إعادة حساب المدة
        const timeIn = seg.timeIn.split(':').map(Number);
        const timeOutParts = timeOut.split(':').map(Number);
        
        // تحويل إلى ثواني
        const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + (timeIn[2] || 0);
        const outSeconds = timeOutParts[0] * 3600 + timeOutParts[1] * 60 + (timeOutParts[2] || 0);
        
        // حساب الفرق
        let durationSeconds = outSeconds - inSeconds;
        if (durationSeconds < 0) {
          durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب
        }
        
        // تحويل إلى تنسيق HH:MM:SS
        const hours = Math.floor(durationSeconds / 3600);
        const minutes = Math.floor((durationSeconds % 3600) / 60);
        const seconds = durationSeconds % 60;
        
        const duration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        return {
          segmentCode: seg.segmentCode,
          timeIn: seg.timeIn,
          timeOut: timeOut,
          duration: duration
        };
      });
      
      console.log('إرسال البيانات:', {
        ...formData,
        segments: validSegments
      });

      // إضافة التوكن للتحقق من الصلاحيات
      const token = localStorage.getItem('token');
      
      // تحويل التوكن إلى الصيغة المتوقعة
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tokenWithRole = `token_${user.id || 'unknown'}_${user.role || 'unknown'}`;
      
      console.log('Sending with token:', tokenWithRole);
      console.log('User data:', user);
      
      const response = await fetch(`/api/media?id=${mediaId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokenWithRole}`
        },
        body: JSON.stringify({
          formData: formData,
          segments: validSegments
        }),
      });

      const result = await response.json();

      if (result.success) {
        showSuccessToast('mediaUpdated');
        setTimeout(() => {
          router.push('/media-list');
        }, 1500);
      } else {
        console.error('Error updating media:', result.error);
        showErrorToast('unknownError');
      }
    } catch (error) {
      console.error('Error updating media:', error);
      showErrorToast('serverConnection');
    } finally {
      setIsSubmitting(false);
    }
  };

  const addSegment = () => {
    // إنشاء سيجمنت جديد
    // استخدام رقم تعريفي فريد بدلاً من الاعتماد على طول المصفوفة
    const newId = Math.max(0, ...segments.map(s => s.id)) + 1;
    const newSegment: Segment = {
      id: newId,
      segmentCode: `SEG${newId.toString().padStart(3, '0')}`,
      timeIn: '00:00:00',
      timeOut: '', // Empty value as in add media page
      duration: ''  // Empty value as in add media page
    };
    
    // Add new segment
    setSegments(prevSegments => [...prevSegments, newSegment]);
    
    // Log new segment addition
    console.log(`✅ New segment added with ID ${newId}`);
  };

  const removeSegment = (id: number) => {
    if (segments.length > 1) {
      setSegments(segments.filter(seg => seg.id !== id));
    }
  };

  const updateSegment = (id: number, field: keyof Segment, value: string) => {
    console.log(`🔄 تحديث السيجمنت ${id} - الحقل: ${field} - القيمة: ${value}`);
    
    // تحديث قيمة الحقل مباشرة في الحالة
    setSegments(prevSegments => {
      return prevSegments.map(seg => {
        if (seg.id === id) {
          return { ...seg, [field]: value };
        }
        return seg;
      });
    });
  };
  
  // حساب المدة بين timeIn و timeOut
  const calculateDuration = (segmentId: number) => {
    console.log(`🕒 بدء حساب المدة للسيجمنت رقم ${segmentId}`);
    
    // الحصول على السيجمنت الحالي
    const segment = segments.find(s => s.id === segmentId);
    
    if (!segment) {
      console.error(`❌ لم يتم العثور على السيجمنت رقم ${segmentId}`);
      return;
    }
    
    // إذا كان أحد الحقلين فارغاً
    if (!segment.timeIn || segment.timeIn === '') {
      console.log('⚠️ وقت البداية فارغ، لا يمكن حساب المدة');
      return;
    }
    
    if (!segment.timeOut || segment.timeOut === '') {
      console.log('⚠️ وقت النهاية فارغ، لا يمكن حساب المدة');
      return;
    }

    try {
      // تنسيق الأوقات للتأكد من صحتها
      let timeInFormatted = segment.timeIn;
      let timeOutFormatted = segment.timeOut;
      
      // تنسيق وقت البداية إذا لم يكن بالتنسيق الصحيح
     if (!/^\d{2}:\d{2}:\d{2}$/.test(timeInFormatted)) {
        const parts = timeInFormatted.split(':').map(p => parseInt(p) || 0);
        const hours = parts[0] % 24;
        const minutes = parts[1] % 60 || 0;
        const seconds = parts[2] % 60 || 0;
        timeInFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      }
      
      // تنسيق وقت النهاية إذا لم يكن بالتنسيق الصحيح
      if (!/^\d{2}:\d{2}:\d{2}$/.test(timeOutFormatted)) {
        const parts = timeOutFormatted.split(':').map(p => parseInt(p) || 0);
        const hours = parts[0] % 24;
        const minutes = parts[1] % 60 || 0;
        const seconds = parts[2] % 60 || 0;
        timeOutFormatted = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      }
      
       console.log(`🔄 الأوقات بعد التنسيق: ${timeInFormatted} إلى ${timeOutFormatted}`);
      
      // تحويل الأوقات إلى ثواني
      const timeIn = timeInFormatted.split(':').map(Number);
      const timeOut = timeOutFormatted.split(':').map(Number);
      
      const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + (timeIn[2] || 0);
      const outSeconds = timeOut[0] * 3600 + timeOut[1] * 60 + (timeOut[2] || 0);
      
      console.log(`⏱️ الأوقات بالثواني: ${inSeconds}s إلى ${outSeconds}s`);

      // حساب الفرق
      let durationSeconds = outSeconds - inSeconds;
      if (durationSeconds < 0) {
        console.log(`⚠️ وقت النهاية أقل من وقت البداية، إضافة يوم كامل (${24 * 3600} ثانية)`);
        durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب
      }
      
      console.log(`⏱️ فرق الوقت بالثواني: ${durationSeconds}s`);

      // تحويل إلى تنسيق HH:MM:SS
      const hours = Math.floor(durationSeconds / 3600);
      const minutes = Math.floor((durationSeconds % 3600) / 60);
      const seconds = durationSeconds % 60;

      const duration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      
      console.log(`✅ المدة المحسوبة: ${duration} (${durationSeconds} ثانية)`);

      // تحديث المدة فقط
      updateSegment(segmentId, 'duration', duration);
      
      console.log(`✅ Segment ${segmentId} data updated`);
      
      return duration;
    } catch (error) {
      console.error('❌ Error calculating duration:', error);
      return null;
    }
  };

  return (
    <AuthGuard requiredPermissions={['MEDIA_UPDATE']}>
      <DashboardLayout title="Update Media" subtitle="Edit media information" icon="✏️">
        <form onSubmit={handleSubmit} style={{ maxWidth: '800px', margin: '0 auto' }}>
          {/* Basic Information */}
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #6b7280'
          }}>
            <h2 style={{ color: '#f3f4f6', marginBottom: '20px', fontSize: '1.3rem' }}>
              📝 Basic Information
            </h2>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '20px' }}>
              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  اسم المادة *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  نوع المادة *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                >
                  <option value="">اختر نوع المادة</option>
                  <option value="FILM">Film</option>
                  <option value="SERIES">Series</option>
                  <option value="PROGRAM">Program</option>
                  <option value="SONG">Song</option>
                  <option value="FILLER">Filler</option>
                  <option value="STING">Sting</option>
                  <option value="PROMO">Promo</option>
                  <option value="NEXT">Next</option>
                  <option value="NOW">Now</option>
                  <option value="سنعود">سنعود</option>
                  <option value="عدنا">عدنا</option>
                  <option value="MINI">Mini</option>
                  <option value="CROSS">Cross</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  الوصف
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white',
                    minHeight: '80px',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  القناة *
                </label>
                <select
                  value={formData.channel}
                  onChange={(e) => setFormData({...formData, channel: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                >
                  <option value="">اختر القناة</option>
                  <option value="DOCUMENTARY">الوثائقية</option>
                  <option value="NEWS">قطاع الأخبار</option>
                  <option value="OTHER">أخرى</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  المصدر
                </label>
                <input
                  type="text"
                  value={formData.source}
                  onChange={(e) => setFormData({...formData, source: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  الحالة *
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({...formData, status: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                >
                  <option value="">اختر الحالة</option>
                  <option value="VALID">صالح</option>
                  <option value="REJECTED_CENSORSHIP">مرفوض رقابياً</option>
                  <option value="REJECTED_TECHNICAL">مرفوض هندسياً</option>
                  <option value="EXPIRED">منتهى</option>
                  <option value="HOLD">Hold</option>
                </select>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  تاريخ البداية
                </label>
                <input
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  تاريخ النهاية
                </label>
                <input
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الحلقة
                </label>
                <input
                  type="number"
                  value={formData.episodeNumber}
                  onChange={(e) => setFormData({...formData, episodeNumber: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الموسم
                </label>
                <input
                  type="number"
                  value={formData.seasonNumber}
                  onChange={(e) => setFormData({...formData, seasonNumber: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الجزء
                </label>
                <input
                  type="number"
                  value={formData.partNumber}
                  onChange={(e) => setFormData({...formData, partNumber: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  رقم الهارد ديسك *
                </label>
                <input
                  type="text"
                  value={formData.hardDiskNumber}
                  onChange={(e) => setFormData({...formData, hardDiskNumber: e.target.value})}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white'
                  }}
                />
              </div>

              <div style={{ gridColumn: '1 / -1' }}>
                <label style={{ display: 'block', marginBottom: '8px', color: '#f3f4f6', fontWeight: 'bold' }}>
                  ملاحظات
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  style={{
                    width: '100%',
                    padding: '12px',
                    border: '2px solid #6b7280',
                    borderRadius: '8px',
                    fontSize: '1rem',
                    direction: 'rtl',
                    color: '#333',
                    background: 'white',
                    minHeight: '100px',
                    resize: 'vertical'
                  }}
                />
              </div>
              
              {/* إضافة خيار TX */}
              <div style={{ gridColumn: '1 / -1', marginTop: '15px' }}>
                <label style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  color: '#f3f4f6', 
                  fontSize: '1rem',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={formData.showInTX}
                    onChange={(e) => setFormData({...formData, showInTX: e.target.checked})}
                    style={{ marginLeft: '10px', width: '18px', height: '18px' }}
                  />
                  <span style={{ 
                    background: '#10b981', 
                    color: 'white', 
                    padding: '2px 8px', 
                    borderRadius: '4px', 
                    marginLeft: '10px',
                    fontWeight: 'bold'
                  }}>
                    TX
                  </span>
                  إظهار في قائمة الخريطة وجدول الإذاعة
                </label>
                <div style={{ 
                  fontSize: '0.9rem', 
                  color: '#9ca3af', 
                  marginTop: '5px', 
                  marginRight: '35px' 
                }}>
                  عند تفعيل هذا الخيار، ستظهر المادة في القائمة الجانبية لجدول الخريطة وجدول الإذاعة
                </div>
              </div>
            </div>
          </div>

          {/* السيجمانت */}
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #6b7280'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ color: '#f3f4f6', fontSize: '1.3rem', margin: 0 }}>
                🎬 السيجمانت
              </h2>
              <button
                type="button"
                onClick={addSegment}
                style={{
                  background: 'linear-gradient(45deg, #10b981, #059669)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem'
                }}
              >
                ➕ إضافة سيجمنت
              </button>
            </div>

            {segments.map((segment, index) => (
              <div key={segment.id} style={{
                background: '#1f2937',
                borderRadius: '10px',
                padding: '20px',
                marginBottom: '15px',
                border: '1px solid #6b7280'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                  <h3 style={{ color: '#f3f4f6', margin: 0 }}>سيجمنت #{segment.id}</h3>
                  {segments.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeSegment(segment.id)}
                      style={{
                        background: '#ef4444',
                        color: 'white',
                        border: 'none',
                        borderRadius: '6px',
                        padding: '6px 12px',
                        cursor: 'pointer',
                        fontSize: '0.8rem'
                      }}
                    >
                      🗑️ حذف
                    </button>
                  )}
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      كود السيجمنت
                    </label>
                    <input
                      type="text"
                      value={segment.segmentCode}
                      onChange={(e) => updateSegment(segment.id, 'segmentCode', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'rtl',
                        color: '#333',
                        background: 'white'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      وقت البداية
                    </label>
                    <input
                      type="text"
                      placeholder="00:00:00"
                      value={segment.timeIn}
                      onChange={(e) => updateSegment(segment.id, 'timeIn', e.target.value)}
                      onBlur={(e) => {
                        // تنسيق الوقت عند مغادرة الحقل
                        let value = e.target.value;
                        if (!value) return;
                        
                        try {
                          // تنسيق الوقت
                          let parts = value.split(':');
                          if (parts.length === 1) {
                            // إذا كان المستخدم أدخل رقماً واحداً فقط، نعتبره ساعات
                            const hours = parseInt(parts[0]) % 24;
                            value = `${hours.toString().padStart(2, '0')}:00:00`;
                          } else if (parts.length === 2) {
                            // إذا كان المستخدم أدخل ساعات ودقائق فقط
                            const hours = parseInt(parts[0] || '0') % 24;
                            const minutes = parseInt(parts[1] || '0') % 60;
                            value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
                          } else if (parts.length === 3) {
                            // إذا كان المستخدم أدخل ساعات ودقائق وثواني
                            const hours = parseInt(parts[0] || '0') % 24;
                            const minutes = parseInt(parts[1] || '0') % 60;
                            const seconds = parseInt(parts[2] || '0') % 60;
                            value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                          }
                          
                          updateSegment(segment.id, 'timeIn', value);
                          
                          // حساب المدة إذا كان وقت النهاية موجوداً
                          if (segment.timeOut) {
                            setTimeout(() => calculateDuration(segment.id), 100);
                          }
                        } catch (error) {
                          console.error('Time format error:', error);
                        }
                      }}
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'ltr',
                        color: '#333',
                        background: 'white'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      وقت النهاية <span style={{ color: '#ef4444' }}>*</span>
                    </label>
                    <input
                      type="text"
                      placeholder="00:00:00"
                      value={segment.timeOut}
                      onChange={(e) => updateSegment(segment.id, 'timeOut', e.target.value)}
                      onFocus={(e) => {
                        // إزالة القيمة الوهمية عند النقر
                        if (e.target.value === '00:00:00') {
                          updateSegment(segment.id, 'timeOut', '');
                        }
                      }}
                      onBlur={(e) => {
                        // تنسيق الوقت عند مغادرة الحقل
                        let value = e.target.value;
                        if (!value) return;
                        
                        try {
                          // تنسيق الوقت
                          let parts = value.split(':');
                          if (parts.length === 1) {
                            // إذا كان المستخدم أدخل رقماً واحداً فقط، نعتبره ساعات
                            const hours = parseInt(parts[0]) % 24;
                            value = `${hours.toString().padStart(2, '0')}:00:00`;
                          } else if (parts.length === 2) {
                            // إذا كان المستخدم أدخل ساعات ودقائق فقط
                            const hours = parseInt(parts[0] || '0') % 24;
                            const minutes = parseInt(parts[1] || '0') % 60;
                            value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`;
                          } else if (parts.length === 3) {
                            // إذا كان المستخدم أدخل ساعات ودقائق وثواني
                            const hours = parseInt(parts[0] || '0') % 24;
                            const minutes = parseInt(parts[1] || '0') % 60;
                            const seconds = parseInt(parts[2] || '0') % 60;
                            value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                          }
                          
                          updateSegment(segment.id, 'timeOut', value);
                          
                          // حساب المدة إذا كان وقت البداية موجوداً
                          if (segment.timeIn) {
                            setTimeout(() => calculateDuration(segment.id), 100);
                          }
                        } catch (error) {
                          console.error('Time format error:', error);
                        }
                      }}
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'ltr',
                        color: '#333',
                        background: 'white'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#d1d5db', fontSize: '0.9rem' }}>
                      المدة (تحسب تلقائياً)
                    </label>
                    <input
                      type="text"
                      placeholder="00:00:00"
                      value={segment.duration}
                      readOnly
                      style={{
                        width: '100%',
                        padding: '10px',
                        border: '1px solid #6b7280',
                        borderRadius: '6px',
                        fontSize: '0.9rem',
                        direction: 'ltr',
                        color: '#333',
                        background: '#f3f4f6',
                        cursor: 'not-allowed'
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* أزرار الحفظ */}
          <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
            <button
              type="button"
              onClick={() => router.push('/media-list')}
              style={{
                background: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '12px 30px',
                fontSize: '1rem',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                background: isSubmitting
                  ? '#6c757d'
                  : 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '12px 30px',
                fontSize: '1rem',
                cursor: isSubmitting ? 'not-allowed' : 'pointer'
              }}
            >
              {isSubmitting ? '⏳ Saving...' : '💾 Save Changes'}
            </button>
          </div>
        </form>
        <ToastContainer />
      </DashboardLayout>
    </AuthGuard>
  );
}
