'use client';

import { useState, useEffect } from 'react';
import { AuthGuard } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import StatsCard from '@/components/StatsCard';
import { useTranslation } from 'react-i18next';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  status: string;
  segments: any[];
  createdAt: string;
}

interface Statistics {
  totalItems: number;
  byType: { [key: string]: number };
  byStatus: { [key: string]: number };
  totalSegments: number;
  recentItems: MediaItem[];
}

export default function StatisticsPage() {
  const { t, i18n } = useTranslation('common');

  // Get current language and direction
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';

  const [statistics, setStatistics] = useState<Statistics>({
    totalItems: 0,
    byType: {},
    byStatus: {},
    totalSegments: 0,
    recentItems: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchStatistics = async () => {
    try {
      const response = await fetch('/api/media');
      const result = await response.json();

      if (result.success) {
        const items = result.data;
        
        // حساب الإحصائيات
        const stats: Statistics = {
          totalItems: items.length,
          byType: {},
          byStatus: {},
          totalSegments: 0,
          recentItems: items.slice(0, 5)
        };

        // إحصائيات الأنواع
        items.forEach((item: MediaItem) => {
          stats.byType[item.type] = (stats.byType[item.type] || 0) + 1;
          stats.byStatus[item.status] = (stats.byStatus[item.status] || 0) + 1;
          stats.totalSegments += item.segments?.length || 0;
        });

        setStatistics(stats);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeLabel = (type: string) => {
    const types: { [key: string]: string } = {
      PROGRAM: 'Program',
      SERIES: 'Series',
      FILM: 'Film',
      MOVIE: 'Film', // للتوافق مع البيانات القديمة
      SONG: 'Song',
      FILLER: 'Filler',
      STING: 'Sting',
      PROMO: 'Promo',
      NEXT: 'Next',
      NOW: 'Now',
      'سنعود': 'سنعود',
      'عدنا': 'عدنا'
    };
    return types[type] || type;
  };

  const getStatusLabel = (status: string) => {
    const statuses: { [key: string]: string } = {
      VALID: t('statistics.status.valid'),
      REJECTED_CENSORSHIP: t('statistics.status.rejectedCensorship'),
      REJECTED_TECHNICAL: t('statistics.status.rejectedTechnical'),
      WAITING: t('statistics.status.waiting')
    };
    return statuses[status] || status;
  };

  const getShortStatusLabel = (status: string) => {
    const statuses: { [key: string]: string } = {
      VALID: t('statistics.status.valid').split(' ')[0], // أخذ أول كلمة فقط
      REJECTED_CENSORSHIP: currentLang === 'ar' ? 'مرفوض رقابي' : 'Rejected (Censorship)',
      REJECTED_TECHNICAL: currentLang === 'ar' ? 'مرفوض هندسي' : 'Rejected (Technical)',
      WAITING: currentLang === 'ar' ? 'في الانتظار' : 'Waiting'
    };
    return statuses[status] || status;
  };

  const getTypeIcon = (type: string) => {
    const icons: { [key: string]: string } = {
      PROGRAM: '📺',
      SERIES: '🎭',
      FILM: '🎬',
      MOVIE: '🎬', // للتوافق مع البيانات القديمة
      SONG: '🎵',
      FILLER: '📦',
      STING: '⚡',
      PROMO: '📢',
      NEXT: '▶️',
      NOW: '🔴',
      'سنعود': '⏰',
      'عدنا': '✅'
    };
    return icons[type] || '📺';
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>⏳ {t('statistics.loadingStats')}</div>
      </div>
    );
  }

  return (
    <AuthGuard requiredRole="ADMIN">
      <DashboardLayout title={t('statistics.title')} subtitle={t('statistics.subtitle')} icon="📊">

        {/* الإحصائيات الرئيسية */}
        <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px', marginBottom: '30px' }}>
          <StatsCard
            icon="📺"
            title={t('statistics.totalMedia')}
            value={statistics.totalItems}
            subtitle={t('statistics.allRegisteredMedia')}
            gradient="linear-gradient(135deg, #28a745 0%, #20c997 100%)"
          />

          <StatsCard
            icon="🎬"
            title={t('statistics.totalSegments')}
            value={statistics.totalSegments}
            subtitle={t('statistics.allSegments')}
            gradient="linear-gradient(135deg, #007bff 0%, #0056b3 100%)"
          />

          <StatsCard
            icon="📈"
            title={t('statistics.differentTypes')}
            value={Object.keys(statistics.byType).length}
            subtitle={t('statistics.mediaTypes')}
            gradient="linear-gradient(135deg, #ffc107 0%, #e0a800 100%)"
          />

          <StatsCard
            icon="⚡"
            title={t('statistics.averageSegments')}
            value={statistics.totalItems > 0 ? Math.round(statistics.totalSegments / statistics.totalItems) : 0}
            subtitle={t('statistics.perMedia')}
            gradient="linear-gradient(135deg, #dc3545 0%, #c82333 100%)"
          />
        </div>

        {/* إحصائيات الأنواع */}
        <div style={{
          background: '#4a5568',
          borderRadius: '15px',
          padding: '25px',
          marginBottom: '25px',
          border: '1px solid #6b7280'
        }}>
          <h2 style={{ color: '#f3f4f6', marginBottom: '20px', fontSize: '1.5rem' }}>
            📊 {t('statistics.distributionByType')}
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            {Object.entries(statistics.byType).map(([type, count]) => (
              <div key={type} style={{
                background: '#1f2937',
                borderRadius: '10px',
                padding: '15px',
                textAlign: 'center',
                border: '2px solid #6b7280'
              }}>
                <div style={{ fontSize: '2rem', marginBottom: '10px' }}>{getTypeIcon(type)}</div>
                <h4 style={{ margin: '0 0 5px 0', color: '#f3f4f6' }}>{getTypeLabel(type)}</h4>
                <p style={{ margin: 0, fontSize: '1.5rem', fontWeight: 'bold', color: '#60a5fa' }}>{count}</p>
              </div>
            ))}
          </div>
        </div>

        {/* إحصائيات الحالات */}
        <div style={{
          background: '#4a5568',
          borderRadius: '15px',
          padding: '25px',
          marginBottom: '25px',
          border: '1px solid #6b7280'
        }}>
          <h2 style={{ color: '#f3f4f6', marginBottom: '20px', fontSize: '1.5rem' }}>
            ✅ {t('statistics.distributionByStatus')}
          </h2>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
            {Object.entries(statistics.byStatus).map(([status, count]) => (
              <div key={status} style={{
                background: '#1f2937',
                borderRadius: '10px',
                padding: '15px',
                textAlign: 'center',
                border: `2px solid ${status === 'VALID' ? '#10b981' : status.includes('REJECTED') ? '#ef4444' : '#f59e0b'}`
              }}>
                <h4 style={{ margin: '0 0 5px 0', color: '#f3f4f6' }}>{getShortStatusLabel(status)}</h4>
                <p style={{ margin: 0, fontSize: '1.5rem', fontWeight: 'bold',
                  color: status === 'VALID' ? '#10b981' : status.includes('REJECTED') ? '#ef4444' : '#f59e0b'
                }}>{count}</p>
              </div>
            ))}
          </div>
        </div>

        {/* المواد الحديثة */}
        {statistics.recentItems.length > 0 && (
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '25px',
            border: '1px solid #6b7280'
          }}>
            <h2 style={{ color: '#f3f4f6', marginBottom: '20px', fontSize: '1.5rem' }}>
              🕒 {t('statistics.recentlyAdded')}
            </h2>
            
            <div style={{ display: 'grid', gap: '15px' }}>
              {statistics.recentItems.map((item) => (
                <div key={item.id} style={{
                  background: '#1f2937',
                  borderRadius: '10px',
                  padding: '15px',
                  border: '1px solid #6b7280',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div>
                    <h4 style={{ margin: '0 0 5px 0', color: '#f3f4f6' }}>{item.name}</h4>
                    <p style={{ margin: 0, color: '#d1d5db', fontSize: '0.9rem' }}>
                      {getTypeIcon(item.type)} {getTypeLabel(item.type)} • {item.segments?.length || 0} سيجمنت
                    </p>
                  </div>
                  <div style={{
                    background: item.status === 'VALID' ? '#10b981' : item.status.includes('REJECTED') ? '#ef4444' : '#f59e0b',
                    color: 'white',
                    padding: '5px 10px',
                    borderRadius: '15px',
                    fontSize: '0.8rem'
                  }}>
                    {getStatusLabel(item.status)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </DashboardLayout>
    </AuthGuard>
  );
}
