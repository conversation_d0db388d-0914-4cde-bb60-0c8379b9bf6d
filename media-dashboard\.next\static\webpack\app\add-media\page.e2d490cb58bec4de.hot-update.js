"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'استيراد جدول',\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: \"\".concat(isRTL ? 'right' : 'left', \" 0.3s ease\"),\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? '12px 20px 12px 8px' : '12px 8px 12px 20px',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr'\n                                },\n                                children: item.name\n                            }, index, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '10px',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '8px',\n                                fontSize: '0.9rem',\n                                fontWeight: 'bold',\n                                marginBottom: '15px'\n                            },\n                            children: [\n                                \"\\uD83D\\uDEAA \",\n                                t('navigation.logout')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"iD1E/nIq6RMBb5wMAss7HkG8MNs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});