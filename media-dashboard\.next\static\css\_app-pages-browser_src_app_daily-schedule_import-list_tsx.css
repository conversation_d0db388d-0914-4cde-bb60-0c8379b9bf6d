/*!***************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!./src/app/daily-schedule/daily-schedule.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************/
/* Daily Schedule Styles */
.daily-schedule-container {
  min-height: 100vh;
  background: #b8dce8;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  color: #333;
  font-weight: 500;
}

/* Header */
.schedule-header {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  color: #333;
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-name {
  font-weight: bold;
  color: #333;
  font-size: 1.1rem;
}

.user-role {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Controls */
.schedule-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.date-selector {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-selector label {
  font-weight: bold;
  color: #333;
}

.glass-input, .glass-button {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  padding: 10px 15px;
  font-size: 1rem;
  color: #000 !important;
  transition: all 0.3s ease;
}

.glass-input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.glass-button {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  border: 1px solid rgba(0, 123, 255, 0.2);
  cursor: pointer;
  font-weight: bold;
}

.glass-button:hover {
  background: rgba(0, 123, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

.glass-button.primary {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.2);
}

.glass-button.primary:hover {
  background: rgba(40, 167, 69, 0.2);
  box-shadow: 0 5px 15px rgba(40, 167, 69, 0.2);
}

.header-buttons {
  display: flex;
  gap: 10px;
}

/* Main Content */
.schedule-content {
  display: flex;
  gap: 15px;
  height: calc(100vh - 200px);
}

.weekly-sidebar {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.weekly-schedule-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.weekly-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background: rgba(0, 123, 255, 0.05);
  border-radius: 8px;
  border-right: 3px solid #007bff;
}

.weekly-time {
  font-weight: bold;
  color: #007bff;
  min-width: 60px;
  font-size: 0.9rem;
}

.weekly-content {
  flex: 1;
}

.weekly-name {
  font-weight: bold;
  color: #333;
  font-size: 0.9rem;
}

.weekly-details {
  font-size: 0.8rem;
  color: #666;
  margin-top: 2px;
}

.weekly-status {
  font-size: 1.2rem;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

/* Sidebar */
.media-sidebar {
  width: 450px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 140px);
}

.loading-message {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #999;
  font-style: italic;
}

.sidebar-title {
  color: #333;
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 15px;
  text-align: center;
}

.sidebar-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
}

.filter-select, .search-input {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 0.9rem;
  color: #000 !important;
}

.search-input::placeholder {
  color: #666;
}

/* Media Table - New Design */
.media-table {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.95);
}

.media-table-header {
  display: grid;
  grid-template-columns: 3fr 1.2fr 90px 90px;
  gap: 1px;
  background: #007bff;
  color: white;
  font-weight: bold;
  font-size: 0.95rem;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.media-table-header > div {
  padding: 14px 10px;
  text-align: center;
  background: #0056b3;
  border-left: 1px solid #004085;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.media-table-header > div:first-child {
  border-left: none;
  text-align: right;
}

.media-table-body {
  flex: 1;
  overflow-y: auto;
  max-height: calc(100vh - 300px);
}

.media-row {
  display: grid;
  grid-template-columns: 3fr 1.2fr 90px 90px;
  gap: 1px;
  background: rgba(255, 255, 255, 0.9);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: grab;
  transition: all 0.2s ease;
  min-height: 65px;
  align-items: center;
}

.media-row:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.media-row:active {
  cursor: grabbing;
}

.media-row > div {
  padding: 10px 8px;
  font-size: 0.9rem;
  line-height: 1.3;
}

.media-col-name {
  text-align: right;
  font-weight: 600;
  position: relative;
  color: #2c3e50;
  overflow: visible;
  white-space: normal;
  word-wrap: break-word;
  max-height: none;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.media-name-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
  word-wrap: break-word;
}

.media-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 2px;
  align-items: center;
}

.media-col-type {
  text-align: center;
  font-weight: 600;
  font-size: 0.85rem;
  color: #495057;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.media-col-duration {
  text-align: center;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #2c3e50;
  font-size: 0.9rem;
  white-space: nowrap;
}

.media-col-code {
  text-align: center;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #6c757d;
  font-size: 0.8rem;
  white-space: nowrap;
}

/* Media Row Colors by Type */
.media-row.program {
  border-right: 4px solid #28a745;
  background: rgba(40, 167, 69, 0.05);
}

.media-row.series {
  border-right: 4px solid #007bff;
  background: rgba(0, 123, 255, 0.05);
}

.media-row.movie {
  border-right: 4px solid #6f42c1;
  background: rgba(111, 66, 193, 0.05);
}

.media-row.promo {
  border-right: 4px solid #fd7e14;
  background: rgba(253, 126, 20, 0.05);
}

.media-row.sting {
  border-right: 4px solid #e83e8c;
  background: rgba(232, 62, 140, 0.05);
}

.media-row.fill_in, .media-row.filler {
  border-right: 4px solid #ffc107;
  background: rgba(255, 193, 7, 0.05);
}

.media-row.next {
  border-right: 4px solid #17a2b8;
  background: rgba(23, 162, 184, 0.05);
}

.media-row.now {
  border-right: 4px solid #dc3545;
  background: rgba(220, 53, 69, 0.05);
}

/* Episode/Season/Part Tags */
.episode-tag, .season-tag, .part-tag {
  display: inline-block;
  background: rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  margin: 1px 3px 1px 0;
  font-weight: bold;
  white-space: nowrap;
}

.episode-tag {
  background: rgba(40, 167, 69, 0.15);
  color: #155724;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.season-tag {
  background: rgba(0, 123, 255, 0.15);
  color: #004085;
  border: 1px solid rgba(0, 123, 255, 0.3);
}

.part-tag {
  background: rgba(111, 66, 193, 0.15);
  color: #3d1a78;
  border: 1px solid rgba(111, 66, 193, 0.3);
}

.media-item {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 10px;
  cursor: grab;
  transition: all 0.3s ease;
}

/* تمييز أنواع المواد بألوان مختلفة */
.media-item.program {
  border-right: 4px solid #28a745; /* برنامج - أخضر */
}

.media-item.series {
  border-right: 4px solid #007bff; /* مسلسل - أزرق */
}

.media-item.movie {
  border-right: 4px solid #6f42c1; /* فيلم - بنفسجي */
}

.media-item.promo {
  border-right: 4px solid #fd7e14; /* برومو - برتقالي */
}

.media-item.sting {
  border-right: 4px solid #e83e8c; /* ستينج - وردي */
}

.media-item.fill_in, .media-item.filler {
  border-right: 4px solid #ffc107; /* فواصل - أصفر */
}

.media-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.media-item:active {
  cursor: grabbing;
}

.media-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.media-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #666;
}

.media-type {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  padding: 2px 6px;
  border-radius: 4px;
}

.media-episode {
  font-size: 0.8rem;
  color: #28a745;
  font-weight: bold;
}

.media-info {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 4px;
}

.info-tag {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: bold;
  white-space: nowrap;
}

/* Grid */
.schedule-grid {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-width: calc(100vw - 500px);
}

.grid-header {
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  display: grid;
  grid-template-columns: 140px 120px 1fr 100px 140px 160px;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
  gap: 2px;
}

.grid-header > div {
  padding: 15px 10px;
  text-align: center;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.grid-body {
  flex: 1;
  overflow-y: auto;
}

.grid-row {
  display: grid;
  grid-template-columns: 140px 120px 1fr 100px 140px 160px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  min-height: 35px;
  transition: all 0.3s ease;
  gap: 2px;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}

/* صفوف فارغة - حجم كافي للأيقونات */
.grid-row.empty {
  min-height: 50px !important;
  height: 50px !important;
}

/* المواد الأساسية - كبيرة ومميزة */
.grid-row.segment {
  min-height: 60px !important;
}

/* الفواصل - حجم عادي */
.grid-row.filler {
  min-height: 35px !important;
}

.grid-row:hover {
  background: rgba(0, 123, 255, 0.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.grid-row.segment {
  background: rgba(40, 167, 69, 0.05);
  border-left: 3px solid #28a745;
  position: relative;
  color: #495057;
  font-weight: bold;
}

.grid-row.segment.rerun {
  background: rgba(108, 117, 125, 0.05);
  border-left: 3px solid #6c757d;
  color: #495057;
  font-weight: bold;
}

.grid-row.segment.temporary {
  background: rgba(156, 39, 176, 0.05);
  border-left: 3px solid #9c27b0;
  position: relative;
  color: #495057;
  font-weight: bold;
}

.grid-row.segment.temporary::before {
  content: "🟣";
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 0.8rem;
}

.grid-row.segment:hover .action-btn {
  opacity: 1;
}

/* تمييز أنواع الفواصل بألوان مختلفة في الجدول - ألوان ناعمة */
.grid-row.filler {
  background: rgba(255, 193, 7, 0.05);
  border-left: 3px solid #ffc107;
  position: relative;
  color: #495057;
  font-weight: bold;
}

/* تمييز أنواع الفواصل حسب المحتوى بألوان واضحة */
.grid-row.filler[data-type="PROMO"] {
  background: rgba(253, 126, 20, 0.05);
  border-left: 3px solid #fd7e14; /* برومو - برتقالي */
  color: #495057;
  font-weight: bold;
}

.grid-row.filler[data-type="STING"] {
  background: rgba(232, 62, 140, 0.05);
  border-left: 3px solid #e83e8c; /* ستينج - وردي */
  color: #495057;
  font-weight: bold;
}

.grid-row.filler[data-type="FILL_IN"] {
  background: rgba(255, 193, 7, 0.05);
  border-left: 3px solid #ffc107; /* فيل إن - أصفر */
  color: #495057;
  font-weight: bold;
}

/* إضافة مؤشر لنوع الفاصل */
.grid-row.filler[data-type="PROMO"]::before {
  content: "برومو";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 0.65rem;
  background-color: #fd7e14;
  color: white;
  padding: 1px 4px;
  border-radius: 3px;
  opacity: 0.8;
}

.grid-row.filler[data-type="STING"]::before {
  content: "ستينج";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 0.65rem;
  background-color: #e83e8c;
  color: white;
  padding: 1px 4px;
  border-radius: 3px;
  opacity: 0.8;
}

.grid-row.filler[data-type="FILL_IN"]::before {
  content: "فيل إن";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 0.65rem;
  background-color: #ffc107;
  color: black;
  padding: 1px 4px;
  border-radius: 3px;
  opacity: 0.8;
}

.grid-row.empty {
  background: #b8dce8 !important;
  border-style: dashed !important;
  color: #6c757d !important;
  font-style: italic;
  border-color: #a0c4d4 !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
  position: relative !important;
}

.grid-row > div {
  padding: 8px 10px;
  display: flex;
  align-items: center;
  border-left: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.85rem;
  line-height: 1.3;
}

.code-cell {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #007bff;
  background: rgba(0, 123, 255, 0.05);
  text-align: center;
  font-size: 0.85rem;
  padding: 8px 5px !important;
  word-break: break-all;
  border-right: 2px solid #007bff;
}

.code-column {
  text-align: center;
  font-weight: bold;
  font-size: 0.9rem;
}

.time-cell {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #28a745;
  text-align: center;
  font-size: 0.9rem;
  padding: 8px 5px !important;
  background: rgba(40, 167, 69, 0.05);
  border-right: 2px solid #28a745;
}

.time-cell {
  font-weight: bold;
  color: #007bff;
  justify-content: center;
}

.content-cell {
  color: #333;
  min-height: 40px;
  position: relative;
}

.content-cell:empty::after {
  content: "اسحب المادة هنا...";
  color: #999;
  font-style: italic;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.duration-cell {
  color: #666;
  font-size: 0.9rem;
  justify-content: center;
}

.status-cell {
  color: #666;
  font-size: 0.8rem;
  justify-content: center;
  font-weight: bold;
}

.actions-cell {
  justify-content: center;
  gap: 6px;
  flex-wrap: wrap;
  padding: 6px !important;
}

.action-btn {
  background: #007bff;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 0.7rem;
  padding: 4px 8px;
  border-radius: 3px;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 45px;
  text-align: center;
}

.action-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.action-btn.edit {
  background: #28a745;
}

.action-btn.edit:hover {
  background: #1e7e34;
}

.action-btn.delete {
  background: #dc3545;
}

.action-btn.delete:hover {
  background: #c82333;
}

.add-row {
  color: #28a745;
}

.add-multiple-rows {
  color: #17a2b8;
  font-weight: bold;
}

.delete-row {
  color: #dc3545;
}

.action-btn {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.action-btn:hover {
  opacity: 1;
}

.actions-cell:hover .action-btn {
  opacity: 1;
}

.move-up, .move-down {
  color: #007bff;
}

.move-up:disabled, .move-down:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.grid-row[draggable="true"] {
  cursor: move;
}

.grid-row[draggable="true"]:hover {
  background: rgba(0, 123, 255, 0.05);
  border-left: 3px solid #007bff;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
  font-size: 1.2rem;
}

/* Media Type Colors */
.media-item.program { border-left: 4px solid #007bff; }
.media-item.series { border-left: 4px solid #28a745; }
.media-item.movie { border-left: 4px solid #dc3545; }
.media-item.promo { border-left: 4px solid #ffc107; }
.media-item.sting { border-left: 4px solid #6f42c1; }
.media-item.filler { border-left: 4px solid #fd7e14; }

/* تحسينات الألوان والتصميم الجديدة */

/* ألوان المواد الأساسية (البرايم تايم) - ذهبي مثل الخريطة */
.primary-content {
  background: #fff3e0 !important;
  color: #000 !important;
  border: 2px solid #ff9800 !important;
  font-weight: bold !important;
  min-height: 60px !important;
  font-size: 1rem !important;
  padding: 12px 8px !important;
}

/* ألوان المواد المعادة - رمادي مثل الخريطة */
.rerun-content {
  background: #f0f0f0 !important;
  color: #000 !important;
  border: 2px solid #888888 !important;
  font-weight: bold !important;
  min-height: 60px !important;
  font-size: 1rem !important;
  padding: 12px 8px !important;
}

/* ألوان المواد المؤقتة - بنفسجي فاتح */
.temp-content {
  background: #f3e5f5 !important;
  color: #000 !important;
  border: 2px solid #9c27b0 !important;
  font-weight: bold !important;
  min-height: 60px !important;
  font-size: 1rem !important;
  padding: 12px 8px !important;
}

/* ألوان الفواصل - أبيض مع حدود خفيفة */
.break-content {
  background: #ffffff !important;
  color: #333 !important;
  border: 1px solid #ddd !important;
  min-height: 35px !important;
  font-size: 0.85rem !important;
  padding: 8px !important;
}

/* ألوان إضافية لأنواع المواد المختلفة */
.film-content {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
  color: white !important;
  font-weight: bold;
  border: 2px solid #991b1b !important;
  box-shadow: 0 6px 15px rgba(220, 38, 38, 0.4) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

.series-content {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%) !important;
  color: white !important;
  font-weight: bold;
  border: 2px solid #5b21b6 !important;
  box-shadow: 0 6px 15px rgba(124, 58, 237, 0.4) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

.program-content {
  background: linear-gradient(135deg, #db2777 0%, #be185d 100%) !important;
  color: white !important;
  font-weight: bold;
  border: 2px solid #9d174d !important;
  box-shadow: 0 6px 15px rgba(219, 39, 119, 0.4) !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
}

/* ألوان البرومو - برتقالي واضح */
.promo-content {
  background: #ffe0b3 !important;
  color: #000 !important;
  border: 2px solid #ff8c00 !important;
  min-height: 35px !important;
  font-size: 0.85rem !important;
  padding: 8px !important;
}

/* ألوان الفيلر - أخضر فاتح */
.filler-content {
  background: #e8f5e8 !important;
  color: #000 !important;
  border: 2px solid #4caf50 !important;
  min-height: 35px !important;
  font-size: 0.85rem !important;
  padding: 8px !important;
}

/* ألوان الستينج - وردي واضح */
.sting-content {
  background: #fce4ec !important;
  color: #000 !important;
  border: 2px solid #e91e63 !important;
  min-height: 35px !important;
  font-size: 0.85rem !important;
  padding: 8px !important;
}

/* تحسين الجدول - خلفية بيضاء */
.schedule-table {
  border-collapse: separate !important;
  border-spacing: 2px !important;
  background: #ffffff !important;
  border-radius: 10px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid #e0e0e0 !important;
}

/* خلفية الجدول الشبكي */
.grid-container {
  background: #ffffff !important;
  border-radius: 10px !important;
  padding: 10px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  border: 1px solid #e0e0e0 !important;
}

.grid-body {
  background: #ffffff !important;
  border-radius: 8px !important;
  padding: 5px !important;
}

/* تحسين الشبكة الأساسية */
.schedule-grid {
  background: #ffffff !important;
  border: 1px solid #e0e0e0 !important;
}

/* تحسين رأس الجدول */
.grid-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  border-bottom: 2px solid #007bff !important;
}

/* تحسين الصفوف */
.grid-row {
  margin-bottom: 1px !important;
  border-radius: 4px !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* تحسين خلايا المحتوى */
.code-cell, .time-cell, .content-cell, .duration-cell, .status-cell, .actions-cell {
  padding: 6px 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  font-weight: normal !important;
  border-radius: 4px !important;
  margin: 1px !important;
  background: transparent !important;
}

/* خلايا المواد الأساسية أكبر */
.primary-content .code-cell,
.primary-content .time-cell,
.primary-content .content-cell,
.primary-content .duration-cell,
.primary-content .status-cell,
.primary-content .actions-cell,
.rerun-content .code-cell,
.rerun-content .time-cell,
.rerun-content .content-cell,
.rerun-content .duration-cell,
.rerun-content .status-cell,
.rerun-content .actions-cell,
.temp-content .code-cell,
.temp-content .time-cell,
.temp-content .content-cell,
.temp-content .duration-cell,
.temp-content .status-cell,
.temp-content .actions-cell {
  padding: 10px 8px !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
}

/* خلايا الفواصل أصغر */
.break-content .code-cell,
.break-content .time-cell,
.break-content .content-cell,
.break-content .duration-cell,
.break-content .status-cell,
.break-content .actions-cell,
.promo-content .code-cell,
.promo-content .time-cell,
.promo-content .content-cell,
.promo-content .duration-cell,
.promo-content .status-cell,
.promo-content .actions-cell,
.filler-content .code-cell,
.filler-content .time-cell,
.filler-content .content-cell,
.filler-content .duration-cell,
.filler-content .status-cell,
.filler-content .actions-cell,
.sting-content .code-cell,
.sting-content .time-cell,
.sting-content .content-cell,
.sting-content .duration-cell,
.sting-content .status-cell,
.sting-content .actions-cell {
  padding: 4px 6px !important;
  font-size: 0.8rem !important;
}

/* تحسين النصوص */
.content-code, .content-time, .content-text {
  font-size: 0.85rem !important;
  font-weight: normal !important;
  text-shadow: none !important;
}

/* تحسين الأزرار */
.action-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  color: white !important;
  font-weight: bold !important;
  transition: all 0.3s ease !important;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
}

.schedule-table th {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%) !important;
  color: white !important;
  padding: 18px 12px !important;
  text-align: center !important;
  font-weight: bold !important;
  border: none !important;
  font-size: 15px !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4) !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
}

.schedule-table td {
  padding: 15px 10px !important;
  border: 2px solid #e2e8f0 !important;
  text-align: center !important;
  vertical-align: middle !important;
  min-height: 60px !important;
  background: white !important;
  transition: all 0.3s ease !important;
  border-radius: 8px !important;
  position: relative !important;
}

.schedule-table td:hover {
  background: #f1f5f9 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15) !important;
  border-color: #cbd5e1 !important;
}

/* تحسين النصوص */
.content-text {
  font-size: 13px;
  line-height: 1.4;
  margin: 2px 0;
}

.content-time {
  font-size: 11px;
  opacity: 0.9;
  font-weight: normal;
}

.content-code {
  font-size: 10px;
  opacity: 0.8;
  font-style: italic;
}

/* Responsive */
@media (max-width: 1400px) {
  .media-sidebar {
    width: 400px;
  }

  .schedule-grid {
    max-width: calc(100vw - 450px);
  }
}

@media (max-width: 1200px) {
  .schedule-content {
    flex-direction: column;
  }

  .media-sidebar {
    width: 100%;
    height: 250px;
    max-height: 250px;
  }

  .media-table-body {
    max-height: 180px;
  }

  .media-row {
    min-height: 40px;
  }

  .schedule-grid {
    max-width: 100%;
  }
}

/* تحسينات خاصة لصفحة الاستيراد */
.import-page .grid-row.empty {
  background: #b8dce8 !important;
  min-height: 50px !important;
  height: 50px !important;
  border-color: #a0c4d4 !important;
  border-style: dashed !important;
}

.import-page .grid-row.segment,
.import-page .grid-row.filler {
  font-weight: bold !important;
}

.import-page .grid-row.segment {
  background: rgba(40, 167, 69, 0.05) !important;
  border-left: 3px solid #28a745 !important;
}

/* ألوان المواد الأساسية في صفحة الاستيراد */
.import-page .grid-row.segment.rerun {
  background: rgba(108, 117, 125, 0.05) !important;
  border-left: 3px solid #6c757d !important;
  color: #495057 !important;
  font-weight: bold !important;
}

.import-page .grid-row.segment.temporary {
  background: rgba(156, 39, 176, 0.05) !important;
  border-left: 3px solid #9c27b0 !important;
  position: relative !important;
  color: #495057 !important;
  font-weight: bold !important;
}

.import-page .grid-row.segment.temporary::before {
  content: "🟣";
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 0.8rem;
}

.import-page .grid-row.filler {
  background: rgba(255, 193, 7, 0.05) !important;
  border-left: 3px solid #ffc107 !important;
}

/* ألوان المواد المختلفة في صفحة الاستيراد */
.import-page .grid-row.filler[data-type="PROMO"] {
  background: rgba(253, 126, 20, 0.05) !important;
  border-left: 3px solid #fd7e14 !important; /* برومو - برتقالي */
  color: #495057 !important;
  font-weight: bold !important;
}

.import-page .grid-row.filler[data-type="STING"] {
  background: rgba(232, 62, 140, 0.05) !important;
  border-left: 3px solid #e83e8c !important; /* ستينج - وردي */
  color: #495057 !important;
  font-weight: bold !important;
}

.import-page .grid-row.filler[data-type="FILL_IN"] {
  background: rgba(255, 193, 7, 0.05) !important;
  border-left: 3px solid #ffc107 !important; /* فيل إن - أصفر */
  color: #495057 !important;
  font-weight: bold !important;
}

/* مؤشرات أنواع المواد في صفحة الاستيراد */
.import-page .grid-row.filler[data-type="PROMO"]::before {
  content: "برومو";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 0.65rem;
  background: #fd7e14;
  color: white;
  padding: 1px 4px;
  border-radius: 3px;
  opacity: 0.8;
}

.import-page .grid-row.filler[data-type="STING"]::before {
  content: "ستينج";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 0.65rem;
  background: #e83e8c;
  color: white;
  padding: 1px 4px;
  border-radius: 3px;
  opacity: 0.8;
}

.import-page .grid-row.filler[data-type="FILL_IN"]::before {
  content: "فيل إن";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 0.65rem;
  background: #ffc107;
  color: white;
  padding: 1px 4px;
  border-radius: 3px;
  opacity: 0.8;
}

/* قاعدة إضافية لضمان تطبيق لون الصفوف الفارغة */
.grid-container .grid-row.empty,
.grid-body .grid-row.empty {
  background-color: #b8dce8 !important;
  background: #b8dce8 !important;
}

.import-page .grid-container .grid-row.empty,
.import-page .grid-body .grid-row.empty {
  background-color: #b8dce8 !important;
  background: #b8dce8 !important;
}

/* تصغير المادة أثناء السحب لتتناسب مع الصفوف الفارغة */
.media-item.dragging,
.media-item:active {
  height: 50px !important;
  min-height: 50px !important;
  max-height: 50px !important;
  padding: 8px 12px !important;
  font-size: 0.9rem !important;
  line-height: 1.3 !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  transform: scale(0.98) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000 !important;
  opacity: 0.9 !important;
}

/* تأثير بصري أثناء السحب */
.media-item.dragging .media-name,
.media-item:active .media-name {
  font-size: 0.8rem !important;
  font-weight: bold !important;
  margin-bottom: 0 !important;
}

.media-item.dragging .media-details,
.media-item:active .media-details {
  font-size: 0.7rem !important;
  margin-top: 2px !important;
}

.media-item.dragging .media-type,
.media-item:active .media-type {
  font-size: 0.65rem !important;
  padding: 1px 4px !important;
}

.media-item.dragging .media-duration,
.media-item:active .media-duration {
  font-size: 0.65rem !important;
  padding: 1px 4px !important;
}

/* تأثير السحب للصفوف الجديدة */
.media-row.dragging,
.media-row:active {
  height: 50px !important;
  min-height: 50px !important;
  max-height: 50px !important;
  padding: 8px 12px !important;
  font-size: 0.9rem !important;
  line-height: 1.3 !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  transform: scale(0.98) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000 !important;
  opacity: 0.9 !important;
  display: flex !important;
  align-items: center !important;
  gap: 10px !important;
  grid-template-columns: none !important;
  justify-content: space-between !important;
  border-radius: 8px !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid #007bff !important;
}

.media-row.dragging > div,
.media-row:active > div {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  font-size: 0.8rem !important;
  padding: 4px 6px !important;
  flex: 1 !important;
}

.media-row.dragging .media-col-name,
.media-row:active .media-col-name {
  flex: 2 !important;
  font-weight: bold !important;
}

.media-row.dragging .episode-tag,
.media-row.dragging .season-tag,
.media-row.dragging .part-tag,
.media-row:active .episode-tag,
.media-row:active .season-tag,
.media-row:active .part-tag {
  display: none !important;
}

/* تأثير hover للصفوف الفارغة عند السحب */
.grid-row.empty:hover {
  background: #a8d2e0 !important;
  border-color: #8bc4d8 !important;
  transition: all 0.2s ease !important;
}

/* تأثير عند السحب فوق الصف الفارغ */
.grid-row.empty.drag-over {
  background: #98c8d8 !important;
  border-color: #7bb8d0 !important;
  border-width: 2px !important;
  transform: scale(1.02) !important;
  transition: all 0.2s ease !important;
}

/* تحسين الأيقونات في الصفوف الفارغة */
.grid-row.empty .action-btn {
  font-size: 0.75rem !important;
  padding: 5px 8px !important;
  min-width: 30px !important;
  height: 30px !important;
  line-height: 1.2 !important;
  border-radius: 4px !important;
  margin: 2px !important;
  box-sizing: border-box !important;
  overflow: hidden !important;
  white-space: nowrap !important;
}

.grid-row.empty .actions-cell {
  padding: 8px !important;
  gap: 5px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 50px !important;
  max-height: 50px !important;
  overflow: hidden !important;
  box-sizing: border-box !important;
}

/* تحسين ظهور الأيقونات في الصفوف الفارغة */
.grid-row.empty:hover .action-btn {
  opacity: 1 !important;
  background: rgba(0, 123, 255, 0.8) !important;
}

.grid-row.empty .action-btn.add-row {
  background: rgba(40, 167, 69, 0.8) !important;
  color: white !important;
}

.grid-row.empty .action-btn.add-multiple-rows {
  background: rgba(40, 167, 69, 0.6) !important;
  color: white !important;
}

.grid-row.empty .action-btn.delete {
  background: rgba(220, 53, 69, 0.8) !important;
  color: white !important;
}

.grid-row.empty .action-btn.move-up,
.grid-row.empty .action-btn.move-down {
  background: rgba(0, 123, 255, 0.8) !important;
  color: white !important;
}

/* تحسينات خاصة للأيقونات في صفحة الاستيراد */
.import-page .grid-row.empty .action-btn {
  font-size: 0.75rem !important;
  padding: 4px 8px !important;
  min-width: 28px !important;
  height: 28px !important;
  line-height: 1.2 !important;
  border-radius: 4px !important;
  margin: 2px !important;
}

.import-page .grid-row.empty .actions-cell {
  padding: 6px !important;
  gap: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
}

.import-page .grid-row.empty:hover .action-btn {
  opacity: 1 !important;
  background: rgba(0, 123, 255, 0.8) !important;
}

/* ألوان المواد حسب نوع البرنامج في صفحة الاستيراد */
.import-page .grid-row.segment[data-type="FILM"] {
  background: rgba(0, 123, 255, 0.05) !important;
  border-left: 3px solid #007bff !important; /* فيلم - أزرق */
}

.import-page .grid-row.segment[data-type="SERIES"] {
  background: rgba(40, 167, 69, 0.05) !important;
  border-left: 3px solid #28a745 !important; /* مسلسل - أخضر */
}

.import-page .grid-row.segment[data-type="PROGRAM"] {
  background: rgba(255, 193, 7, 0.05) !important;
  border-left: 3px solid #ffc107 !important; /* برنامج - أصفر */
}

.import-page .grid-row.segment[data-type="NEWS"] {
  background: rgba(220, 53, 69, 0.05) !important;
  border-left: 3px solid #dc3545 !important; /* أخبار - أحمر */
}

.import-page .grid-row.segment[data-type="NEXT"] {
  background: rgba(111, 66, 193, 0.05) !important;
  border-left: 3px solid #6f42c1 !important; /* نكست - بنفسجي */
}

.import-page .grid-row.segment[data-type="NOW"] {
  background: rgba(253, 126, 20, 0.05) !important;
  border-left: 3px solid #fd7e14 !important; /* ناو - برتقالي */
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .daily-schedule-container {
    padding: 10px;
  }

  .schedule-header {
    padding: 15px;
    margin-bottom: 15px;
  }

  .header-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .header-title {
    font-size: 1.5rem;
  }

  .user-info {
    justify-content: center;
  }

  .schedule-content {
    flex-direction: column !important;
    gap: 15px;
  }

  .media-sidebar {
    width: 100% !important;
    max-width: none !important;
    order: 2;
  }

  .weekly-sidebar {
    width: 100% !important;
    max-width: none !important;
    order: 1;
  }

  .schedule-grid {
    max-width: 100% !important;
    order: 3;
  }

  .grid-header {
    grid-template-columns: 80px 80px 1fr 60px 80px 100px;
    font-size: 0.8rem;
  }

  .grid-row {
    grid-template-columns: 80px 80px 1fr 60px 80px 100px;
    min-height: 40px;
  }

  .grid-header > div {
    padding: 10px 5px;
  }

  .grid-cell {
    padding: 5px 3px !important;
    font-size: 0.8rem;
  }

  .media-list {
    max-height: 300px;
  }

  .media-item {
    padding: 8px;
    margin-bottom: 8px;
  }

  .media-name {
    font-size: 0.9rem;
  }

  .media-info {
    font-size: 0.7rem;
  }

  .sidebar-controls {
    flex-direction: column;
    gap: 10px;
  }

  .filter-select,
  .search-input {
    width: 100%;
  }

  .controls-section {
    flex-direction: column;
    gap: 10px;
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
  }

  .control-group label {
    margin-bottom: 5px;
  }

  .control-group input,
  .control-group select {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .action-buttons button {
    width: 100%;
    padding: 12px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .daily-schedule-container {
    padding: 5px;
  }

  .schedule-header {
    padding: 10px;
  }

  .header-title {
    font-size: 1.2rem;
  }

  .grid-header {
    grid-template-columns: 60px 60px 1fr 50px 60px 80px;
    font-size: 0.7rem;
  }

  .grid-row {
    grid-template-columns: 60px 60px 1fr 50px 60px 80px;
    min-height: 35px;
  }

  .grid-header > div {
    padding: 8px 2px;
  }

  .grid-cell {
    padding: 3px 2px !important;
    font-size: 0.7rem;
  }

  .media-item {
    padding: 6px;
  }

  .media-name {
    font-size: 0.8rem;
  }

  .sidebar-title {
    font-size: 1rem;
  }
}

