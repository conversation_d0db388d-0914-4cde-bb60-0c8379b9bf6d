/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/buffers";
exports.ids = ["vendor-chunks/buffers"];
exports.modules = {

/***/ "(ssr)/./node_modules/buffers/index.js":
/*!***************************************!*\
  !*** ./node_modules/buffers/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("module.exports = Buffers;\n\nfunction Buffers (bufs) {\n    if (!(this instanceof Buffers)) return new Buffers(bufs);\n    this.buffers = bufs || [];\n    this.length = this.buffers.reduce(function (size, buf) {\n        return size + buf.length\n    }, 0);\n}\n\nBuffers.prototype.push = function () {\n    for (var i = 0; i < arguments.length; i++) {\n        if (!Buffer.isBuffer(arguments[i])) {\n            throw new TypeError('Tried to push a non-buffer');\n        }\n    }\n    \n    for (var i = 0; i < arguments.length; i++) {\n        var buf = arguments[i];\n        this.buffers.push(buf);\n        this.length += buf.length;\n    }\n    return this.length;\n};\n\nBuffers.prototype.unshift = function () {\n    for (var i = 0; i < arguments.length; i++) {\n        if (!Buffer.isBuffer(arguments[i])) {\n            throw new TypeError('Tried to unshift a non-buffer');\n        }\n    }\n    \n    for (var i = 0; i < arguments.length; i++) {\n        var buf = arguments[i];\n        this.buffers.unshift(buf);\n        this.length += buf.length;\n    }\n    return this.length;\n};\n\nBuffers.prototype.copy = function (dst, dStart, start, end) {\n    return this.slice(start, end).copy(dst, dStart, 0, end - start);\n};\n\nBuffers.prototype.splice = function (i, howMany) {\n    var buffers = this.buffers;\n    var index = i >= 0 ? i : this.length - i;\n    var reps = [].slice.call(arguments, 2);\n    \n    if (howMany === undefined) {\n        howMany = this.length - index;\n    }\n    else if (howMany > this.length - index) {\n        howMany = this.length - index;\n    }\n    \n    for (var i = 0; i < reps.length; i++) {\n        this.length += reps[i].length;\n    }\n    \n    var removed = new Buffers();\n    var bytes = 0;\n    \n    var startBytes = 0;\n    for (\n        var ii = 0;\n        ii < buffers.length && startBytes + buffers[ii].length < index;\n        ii ++\n    ) { startBytes += buffers[ii].length }\n    \n    if (index - startBytes > 0) {\n        var start = index - startBytes;\n        \n        if (start + howMany < buffers[ii].length) {\n            removed.push(buffers[ii].slice(start, start + howMany));\n            \n            var orig = buffers[ii];\n            //var buf = new Buffer(orig.length - howMany);\n            var buf0 = new Buffer(start);\n            for (var i = 0; i < start; i++) {\n                buf0[i] = orig[i];\n            }\n            \n            var buf1 = new Buffer(orig.length - start - howMany);\n            for (var i = start + howMany; i < orig.length; i++) {\n                buf1[ i - howMany - start ] = orig[i]\n            }\n            \n            if (reps.length > 0) {\n                var reps_ = reps.slice();\n                reps_.unshift(buf0);\n                reps_.push(buf1);\n                buffers.splice.apply(buffers, [ ii, 1 ].concat(reps_));\n                ii += reps_.length;\n                reps = [];\n            }\n            else {\n                buffers.splice(ii, 1, buf0, buf1);\n                //buffers[ii] = buf;\n                ii += 2;\n            }\n        }\n        else {\n            removed.push(buffers[ii].slice(start));\n            buffers[ii] = buffers[ii].slice(0, start);\n            ii ++;\n        }\n    }\n    \n    if (reps.length > 0) {\n        buffers.splice.apply(buffers, [ ii, 0 ].concat(reps));\n        ii += reps.length;\n    }\n    \n    while (removed.length < howMany) {\n        var buf = buffers[ii];\n        var len = buf.length;\n        var take = Math.min(len, howMany - removed.length);\n        \n        if (take === len) {\n            removed.push(buf);\n            buffers.splice(ii, 1);\n        }\n        else {\n            removed.push(buf.slice(0, take));\n            buffers[ii] = buffers[ii].slice(take);\n        }\n    }\n    \n    this.length -= removed.length;\n    \n    return removed;\n};\n \nBuffers.prototype.slice = function (i, j) {\n    var buffers = this.buffers;\n    if (j === undefined) j = this.length;\n    if (i === undefined) i = 0;\n    \n    if (j > this.length) j = this.length;\n    \n    var startBytes = 0;\n    for (\n        var si = 0;\n        si < buffers.length && startBytes + buffers[si].length <= i;\n        si ++\n    ) { startBytes += buffers[si].length }\n    \n    var target = new Buffer(j - i);\n    \n    var ti = 0;\n    for (var ii = si; ti < j - i && ii < buffers.length; ii++) {\n        var len = buffers[ii].length;\n        \n        var start = ti === 0 ? i - startBytes : 0;\n        var end = ti + len >= j - i\n            ? Math.min(start + (j - i) - ti, len)\n            : len\n        ;\n        \n        buffers[ii].copy(target, ti, start, end);\n        ti += end - start;\n    }\n    \n    return target;\n};\n\nBuffers.prototype.pos = function (i) {\n    if (i < 0 || i >= this.length) throw new Error('oob');\n    var l = i, bi = 0, bu = null;\n    for (;;) {\n        bu = this.buffers[bi];\n        if (l < bu.length) {\n            return {buf: bi, offset: l};\n        } else {\n            l -= bu.length;\n        }\n        bi++;\n    }\n};\n\nBuffers.prototype.get = function get (i) {\n    var pos = this.pos(i);\n\n    return this.buffers[pos.buf].get(pos.offset);\n};\n\nBuffers.prototype.set = function set (i, b) {\n    var pos = this.pos(i);\n\n    return this.buffers[pos.buf].set(pos.offset, b);\n};\n\nBuffers.prototype.indexOf = function (needle, offset) {\n    if (\"string\" === typeof needle) {\n        needle = new Buffer(needle);\n    } else if (needle instanceof Buffer) {\n        // already a buffer\n    } else {\n        throw new Error('Invalid type for a search string');\n    }\n\n    if (!needle.length) {\n        return 0;\n    }\n\n    if (!this.length) {\n        return -1;\n    }\n\n    var i = 0, j = 0, match = 0, mstart, pos = 0;\n\n    // start search from a particular point in the virtual buffer\n    if (offset) {\n        var p = this.pos(offset);\n        i = p.buf;\n        j = p.offset;\n        pos = offset;\n    }\n\n    // for each character in virtual buffer\n    for (;;) {\n        while (j >= this.buffers[i].length) {\n            j = 0;\n            i++;\n\n            if (i >= this.buffers.length) {\n                // search string not found\n                return -1;\n            }\n        }\n\n        var char = this.buffers[i][j];\n\n        if (char == needle[match]) {\n            // keep track where match started\n            if (match == 0) {\n                mstart = {\n                    i: i,\n                    j: j,\n                    pos: pos\n                };\n            }\n            match++;\n            if (match == needle.length) {\n                // full match\n                return mstart.pos;\n            }\n        } else if (match != 0) {\n            // a partial match ended, go back to match starting position\n            // this will continue the search at the next character\n            i = mstart.i;\n            j = mstart.j;\n            pos = mstart.pos;\n            match = 0;\n        }\n\n        j++;\n        pos++;\n    }\n};\n\nBuffers.prototype.toBuffer = function() {\n    return this.slice();\n}\n\nBuffers.prototype.toString = function(encoding, start, end) {\n    return this.slice(start, end).toString(encoding);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/buffers/index.js\n");

/***/ })

};
;