'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  permissions?: string[];
}

interface AuthGuardProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: string;
  fallbackComponent?: React.ReactNode;
}

export function AuthGuard({ 
  children, 
  requiredPermissions = [], 
  requiredRole,
  fallbackComponent 
}: AuthGuardProps) {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      // التحقق من وجود بيانات المستخدم في localStorage
      const userData = localStorage.getItem('user');
      const token = localStorage.getItem('token');

      if (!userData || !token) {
        router.push('/login');
        return;
      }

      const parsedUser = JSON.parse(userData);
      setUser(parsedUser);

      // التحقق من الصلاحيات
      const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);
      setHasAccess(access);

      if (!access && fallbackComponent === undefined) {
        router.push('/unauthorized');
      }

    } catch (error) {
      console.error('Auth check error:', error);
      router.push('/login');
    } finally {
      setIsLoading(false);
    }
  };

  const checkPermissions = (user: User, permissions: string[], role?: string): boolean => {
    // المدير له صلاحيات كاملة
    if (user.role === 'ADMIN') {
      return true;
    }

    // التحقق من الدور المطلوب
    if (role && user.role !== role) {
      return false;
    }

    // التحقق من الصلاحيات المطلوبة
    if (permissions.length > 0) {
      const userPermissions = getUserPermissions(user.role);
      return permissions.every(permission => 
        userPermissions.includes(permission) || userPermissions.includes('ALL')
      );
    }

    return true;
  };

  const getUserPermissions = (role: string): string[] => {
    const rolePermissions: { [key: string]: string[] } = {
      'ADMIN': ['ALL'],
      'CONTENT_MANAGER': ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE', 'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE'],
      'MEDIA_MANAGER': ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE', 'SCHEDULE_READ'],
      'SCHEDULER': ['SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ'],
      'FULL_VIEWER': ['MEDIA_READ', 'SCHEDULE_READ', 'MAP_READ', 'BROADCAST_READ', 'REPORT_READ', 'DASHBOARD_READ'],
      'DATA_ENTRY': ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE'],
      'MAP_SCHEDULER': ['MAP_CREATE', 'MAP_READ', 'MAP_UPDATE', 'MAP_DELETE', 'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ'],
      'VIEWER': ['MEDIA_READ', 'SCHEDULE_READ']
    };

    return rolePermissions[role] || [];
  };

  if (isLoading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Cairo, Arial, sans-serif'
      }}>
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '40px',
          textAlign: 'center',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #667eea',
            borderRadius: '50%',
            margin: '0 auto 20px'
          }} />
          <h2 style={{ color: '#333', margin: 0 }}>⏳ جاري التحقق من الصلاحيات...</h2>
        </div>
      </div>
    );
  }

  if (!hasAccess) {
    if (fallbackComponent) {
      return <>{fallbackComponent}</>;
    }

    return (
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '40px',
          textAlign: 'center',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          maxWidth: '500px'
        }}>
          <div style={{
            fontSize: '4rem',
            marginBottom: '20px'
          }}>
            🚫
          </div>
          <h2 style={{ 
            color: '#dc3545', 
            marginBottom: '15px',
            fontSize: '1.5rem'
          }}>
            غير مصرح لك بالوصول
          </h2>
          <p style={{ 
            color: '#6c757d', 
            marginBottom: '25px',
            fontSize: '1rem'
          }}>
            ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة
          </p>
          <div style={{
            background: '#f8f9fa',
            padding: '15px',
            borderRadius: '10px',
            marginBottom: '25px',
            textAlign: 'right'
          }}>
            <strong>معلومات المستخدم:</strong><br />
            الاسم: {user?.name}<br />
            الدور: {user?.role}<br />
            الصلاحيات المطلوبة: {requiredPermissions.join(', ') || 'غير محدد'}
          </div>
          <button
            onClick={() => router.push('/')}
            style={{
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              color: 'white',
              border: 'none',
              borderRadius: '10px',
              padding: '12px 25px',
              fontSize: '1rem',
              cursor: 'pointer',
              fontFamily: 'Cairo, Arial, sans-serif'
            }}
          >
            🏠 العودة للرئيسية
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Hook لاستخدام بيانات المستخدم الحالي
export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
    setIsLoading(false);
  }, []);

  const logout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    window.location.href = '/login';
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.role === 'ADMIN') return true;

    const userPermissions = getUserPermissions(user.role);
    return userPermissions.includes(permission) || userPermissions.includes('ALL');
  };

  const getUserPermissions = (role: string): string[] => {
    const rolePermissions: { [key: string]: string[] } = {
      'ADMIN': ['ALL'],
      'CONTENT_MANAGER': ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE', 'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE'],
      'MEDIA_MANAGER': ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE', 'SCHEDULE_READ'],
      'SCHEDULER': ['SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ'],
      'FULL_VIEWER': ['MEDIA_READ', 'SCHEDULE_READ', 'MAP_READ', 'BROADCAST_READ', 'REPORT_READ', 'DASHBOARD_READ'],
      'DATA_ENTRY': ['MEDIA_CREATE', 'MEDIA_READ', 'MEDIA_UPDATE', 'MEDIA_DELETE'],
      'MAP_SCHEDULER': ['MAP_CREATE', 'MAP_READ', 'MAP_UPDATE', 'MAP_DELETE', 'SCHEDULE_CREATE', 'SCHEDULE_READ', 'SCHEDULE_UPDATE', 'SCHEDULE_DELETE', 'MEDIA_READ'],
      'VIEWER': ['MEDIA_READ', 'SCHEDULE_READ']
    };

    return rolePermissions[role] || [];
  };

  return {
    user,
    isLoading,
    logout,
    hasPermission,
    isAdmin: user?.role === 'ADMIN',
    isMediaManager: user?.role === 'MEDIA_MANAGER',
    isScheduler: user?.role === 'SCHEDULER',
    isViewer: user?.role === 'VIEWER',
    isFullViewer: user?.role === 'FULL_VIEWER',
    isDataEntry: user?.role === 'DATA_ENTRY',
    isMapScheduler: user?.role === 'MAP_SCHEDULER',
    isContentManager: user?.role === 'CONTENT_MANAGER'
  };
}