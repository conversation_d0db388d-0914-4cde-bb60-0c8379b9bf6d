# 📺 نظام البث الإذاعي - Broadcasting Rerun System

## 📋 نظرة عامة

نظام الإعادات التلقائية للبث الإذاعي يعمل بمفهوم **اليوم الإذاعي 24 ساعة** حيث تُعاد المواد في نفس اليوم الإذاعي.

## ⏰ مفهوم اليوم الإذاعي

### 📺 **اليوم الإذاعي = 24 ساعة متواصلة**
- **يبدأ**: الساعة 08:00 صباحاً
- **ينتهي**: الساعة 08:00 صباح اليوم التالي
- **المدة**: 24 ساعة كاملة في **عمود واحد**

### 🔄 **نظام الإعادات الإذاعي**
- **البرايم تايم**: 18:00 - 00:00 (جميع الأيام)
- **الإعادات**: 00:00 - 08:00 (**نفس اليوم الإذاعي**)
- **مثال**: مادة الأحد 19:00 → إعادة الأحد 01:00 (نفس العمود!)

## 🔧 كيف يعمل النظام

### 1. **التحقق من البرايم تايم**
```javascript
function isPrimeTime(startTime, endTime, dayOfWeek) {
  // يتحقق إذا كانت المادة تبدأ في البرايم تايم
}
```

### 2. **توليد الإعادات المتتالية**
```javascript
function generateSequentialReruns(scheduleItems, mediaItems, enrichedSchedule) {
  // يولد إعادات متتالية لكل يوم
  // يحافظ على ترتيب المواد الأصلي
  // يتوقف عند 17:00
}
```

### 3. **حساب الأوقات**
- **المدة**: تُحسب من الوقت الأصلي
- **التتابع**: كل إعادة تبدأ عند انتهاء السابقة
- **الحد الأقصى**: 17:00 (لا تتجاوز بداية البرايم تايم التالي)

## 📊 مثال عملي

### الأحد (البرايم تايم):
- 18:00-19:00: مسلسل الدراما (60 دقيقة)
- 19:00-19:30: برنامج الطبخ (30 دقيقة)  
- 19:30-21:45: فيلم الأكشن (135 دقيقة)

### الاثنين (الإعادات):
- 00:00-01:00: مسلسل الدراما [إعادة]
- 01:00-01:30: برنامج الطبخ [إعادة]
- 01:30-03:45: فيلم الأكشن [إعادة]

## 🎯 المميزات

### ✅ **التوليد التلقائي**
- إعادات تلقائية عند إضافة مواد في البرايم تايم
- لا حاجة لتدخل يدوي

### ✅ **الحفاظ على الترتيب**
- الإعادات تحافظ على نفس ترتيب البث الأصلي
- التتابع الزمني محفوظ

### ✅ **منع التداخل**
- النظام يتوقف عند 17:00
- لا يتداخل مع البرايم تايم التالي

### ✅ **المرونة**
- يدعم أيام الأسبوع ونهاية الأسبوع
- قواعد مختلفة لكل نوع

## 🔍 استكشاف الأخطاء

### ❌ **لا توجد إعادات**
**الأسباب المحتملة:**
1. المادة ليست في البرايم تايم
2. المادة تتجاوز 17:00
3. خطأ في حساب الأوقات

**الحلول:**
1. تحقق من أوقات البرايم تايم
2. تحقق من مدة المواد
3. راجع دالة `isPrimeTime()`

### ❌ **إعادات خاطئة**
**الأسباب المحتملة:**
1. خطأ في حساب المدة
2. خطأ في تحديد اليوم
3. مشكلة في التتابع الزمني

**الحلول:**
1. راجع دالة `calculateDurationBetweenTimes()`
2. تحقق من `dayOfWeek` mapping
3. راجع منطق `currentRerunTime`

## 📝 ملاحظات مهمة

### 🚨 **قيود النظام**
- الإعادات تتوقف عند 17:00
- لا يمكن إعادة المواد غير البرايم تايم
- النظام يعتمد على التوقيت المحلي

### 💡 **نصائح للاستخدام**
- تأكد من دقة أوقات المواد
- راقب طول المواد لتجنب تجاوز 17:00
- استخدم مواد قصيرة في نهاية البرايم تايم

## 🔧 التطوير المستقبلي

### 📈 **تحسينات مقترحة**
- إضافة إعادات متعددة (3 مرات يومياً)
- دعم الإعادات الأسبوعية
- نظام أولويات للمواد
- تحسين خوارزمية التوزيع الزمني

### 🎛️ **إعدادات قابلة للتخصيص**
- أوقات البرايم تايم
- عدد الإعادات
- فترات الإعادة
- قواعد التوزيع

---

## 📞 الدعم الفني

للمساعدة في استكشاف الأخطاء أو التطوير، راجع:
- ملف `src/app/api/schedule/route.ts`
- دالة `generateSequentialReruns()`
- دالة `isPrimeTime()`
- دالة `calculateDurationBetweenTimes()`
