import React from 'react';
// Fixed Logo: Prime - X

interface LogoProps {
  size?: 'small' | 'medium' | 'large';
  className?: string;
  style?: React.CSSProperties;
}

export default function Logo({ size = 'medium', className, style }: LogoProps) {
  const sizes = {
    small: {
      fontSize: '1rem',
      gap: '3px',
      xSize: '1.2rem'
    },
    medium: {
      fontSize: '1.2rem',
      gap: '5px',
      xSize: '1.5rem'
    },
    large: {
      fontSize: '2rem',
      gap: '8px',
      xSize: '2.5rem'
    }
  };

  const currentSize = sizes[size];

  return (
    <div 
      className={className}
      style={{
        display: 'flex',
        alignItems: 'center',
        fontSize: currentSize.fontSize,
        fontWeight: '900',
        fontFamily: 'Arial, sans-serif',
        gap: currentSize.gap,
        direction: 'ltr',
        ...style
      }}
    >
      <span style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontWeight: '800',
        letterSpacing: '1px'
      }}>
        Prime
      </span>
      <span style={{
        color: '#6c757d',
        fontSize: '0.8em',
        fontWeight: '300'
      }}>
        -
      </span>
      <span style={{
        background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        fontWeight: '900',
        fontSize: currentSize.xSize
      }}>
        X
      </span>
    </div>
  );
}
