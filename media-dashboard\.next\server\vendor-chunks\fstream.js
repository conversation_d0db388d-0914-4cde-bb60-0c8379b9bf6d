/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fstream";
exports.ids = ["vendor-chunks/fstream"];
exports.modules = {

/***/ "(ssr)/./node_modules/fstream/fstream.js":
/*!*****************************************!*\
  !*** ./node_modules/fstream/fstream.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("exports.Abstract = __webpack_require__(/*! ./lib/abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\nexports.Reader = __webpack_require__(/*! ./lib/reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nexports.Writer = __webpack_require__(/*! ./lib/writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\n\nexports.File = {\n  Reader: __webpack_require__(/*! ./lib/file-reader.js */ \"(ssr)/./node_modules/fstream/lib/file-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/file-writer.js */ \"(ssr)/./node_modules/fstream/lib/file-writer.js\")\n}\n\nexports.Dir = {\n  Reader: __webpack_require__(/*! ./lib/dir-reader.js */ \"(ssr)/./node_modules/fstream/lib/dir-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/dir-writer.js */ \"(ssr)/./node_modules/fstream/lib/dir-writer.js\")\n}\n\nexports.Link = {\n  Reader: __webpack_require__(/*! ./lib/link-reader.js */ \"(ssr)/./node_modules/fstream/lib/link-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/link-writer.js */ \"(ssr)/./node_modules/fstream/lib/link-writer.js\")\n}\n\nexports.Proxy = {\n  Reader: __webpack_require__(/*! ./lib/proxy-reader.js */ \"(ssr)/./node_modules/fstream/lib/proxy-reader.js\"),\n  Writer: __webpack_require__(/*! ./lib/proxy-writer.js */ \"(ssr)/./node_modules/fstream/lib/proxy-writer.js\")\n}\n\nexports.Reader.Dir = exports.DirReader = exports.Dir.Reader\nexports.Reader.File = exports.FileReader = exports.File.Reader\nexports.Reader.Link = exports.LinkReader = exports.Link.Reader\nexports.Reader.Proxy = exports.ProxyReader = exports.Proxy.Reader\n\nexports.Writer.Dir = exports.DirWriter = exports.Dir.Writer\nexports.Writer.File = exports.FileWriter = exports.File.Writer\nexports.Writer.Link = exports.LinkWriter = exports.Link.Writer\nexports.Writer.Proxy = exports.ProxyWriter = exports.Proxy.Writer\n\nexports.collect = __webpack_require__(/*! ./lib/collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/fstream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/abstract.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/abstract.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// the parent class for all fstreams.\n\nmodule.exports = Abstract\n\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\n\nfunction Abstract () {\n  Stream.call(this)\n}\n\ninherits(Abstract, Stream)\n\nAbstract.prototype.on = function (ev, fn) {\n  if (ev === 'ready' && this.ready) {\n    process.nextTick(fn.bind(this))\n  } else {\n    Stream.prototype.on.call(this, ev, fn)\n  }\n  return this\n}\n\nAbstract.prototype.abort = function () {\n  this._aborted = true\n  this.emit('abort')\n}\n\nAbstract.prototype.destroy = function () {}\n\nAbstract.prototype.warn = function (msg, code) {\n  var self = this\n  var er = decorate(msg, code, self)\n  if (!self.listeners('warn')) {\n    console.error('%s %s\\n' +\n    'path = %s\\n' +\n    'syscall = %s\\n' +\n    'fstream_type = %s\\n' +\n    'fstream_path = %s\\n' +\n    'fstream_unc_path = %s\\n' +\n    'fstream_class = %s\\n' +\n    'fstream_stack =\\n%s\\n',\n      code || 'UNKNOWN',\n      er.stack,\n      er.path,\n      er.syscall,\n      er.fstream_type,\n      er.fstream_path,\n      er.fstream_unc_path,\n      er.fstream_class,\n      er.fstream_stack.join('\\n'))\n  } else {\n    self.emit('warn', er)\n  }\n}\n\nAbstract.prototype.info = function (msg, code) {\n  this.emit('info', msg, code)\n}\n\nAbstract.prototype.error = function (msg, code, th) {\n  var er = decorate(msg, code, this)\n  if (th) throw er\n  else this.emit('error', er)\n}\n\nfunction decorate (er, code, self) {\n  if (!(er instanceof Error)) er = new Error(er)\n  er.code = er.code || code\n  er.path = er.path || self.path\n  er.fstream_type = er.fstream_type || self.type\n  er.fstream_path = er.fstream_path || self.path\n  if (self._path !== self.path) {\n    er.fstream_unc_path = er.fstream_unc_path || self._path\n  }\n  if (self.linkpath) {\n    er.fstream_linkpath = er.fstream_linkpath || self.linkpath\n  }\n  er.fstream_class = er.fstream_class || self.constructor.name\n  er.fstream_stack = er.fstream_stack ||\n    new Error().stack.split(/\\n/).slice(3).map(function (s) {\n      return s.replace(/^ {4}at /, '')\n    })\n\n  return er\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/abstract.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/collect.js":
/*!*********************************************!*\
  !*** ./node_modules/fstream/lib/collect.js ***!
  \*********************************************/
/***/ ((module) => {

eval("module.exports = collect\n\nfunction collect (stream) {\n  if (stream._collected) return\n\n  if (stream._paused) return stream.on('resume', collect.bind(null, stream))\n\n  stream._collected = true\n  stream.pause()\n\n  stream.on('data', save)\n  stream.on('end', save)\n  var buf = []\n  function save (b) {\n    if (typeof b === 'string') b = new Buffer(b)\n    if (Buffer.isBuffer(b) && !b.length) return\n    buf.push(b)\n  }\n\n  stream.on('entry', saveEntry)\n  var entryBuffer = []\n  function saveEntry (e) {\n    collect(e)\n    entryBuffer.push(e)\n  }\n\n  stream.on('proxy', proxyPause)\n  function proxyPause (p) {\n    p.pause()\n  }\n\n  // replace the pipe method with a new version that will\n  // unlock the buffered stuff.  if you just call .pipe()\n  // without a destination, then it'll re-play the events.\n  stream.pipe = (function (orig) {\n    return function (dest) {\n      // console.error(' === open the pipes', dest && dest.path)\n\n      // let the entries flow through one at a time.\n      // Once they're all done, then we can resume completely.\n      var e = 0\n      ;(function unblockEntry () {\n        var entry = entryBuffer[e++]\n        // console.error(\" ==== unblock entry\", entry && entry.path)\n        if (!entry) return resume()\n        entry.on('end', unblockEntry)\n        if (dest) dest.add(entry)\n        else stream.emit('entry', entry)\n      })()\n\n      function resume () {\n        stream.removeListener('entry', saveEntry)\n        stream.removeListener('data', save)\n        stream.removeListener('end', save)\n\n        stream.pipe = orig\n        if (dest) stream.pipe(dest)\n\n        buf.forEach(function (b) {\n          if (b) stream.emit('data', b)\n          else stream.emit('end')\n        })\n\n        stream.resume()\n      }\n\n      return dest\n    }\n  })(stream.pipe)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/collect.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/dir-reader.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-reader.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A thing that emits \"entry\" events with Reader objects\n// Pausing it causes it to stop emitting entry events, and also\n// pauses the current entry if there is one.\n\nmodule.exports = DirReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar assert = (__webpack_require__(/*! assert */ \"assert\").ok)\n\ninherits(DirReader, Reader)\n\nfunction DirReader (props) {\n  var self = this\n  if (!(self instanceof DirReader)) {\n    throw new Error('DirReader must be called as constructor.')\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    throw new Error('Non-directory type ' + props.type)\n  }\n\n  self.entries = null\n  self._index = -1\n  self._paused = false\n  self._length = -1\n\n  if (props.sort) {\n    this.sort = props.sort\n  }\n\n  Reader.call(this, props)\n}\n\nDirReader.prototype._getEntries = function () {\n  var self = this\n\n  // race condition.  might pause() before calling _getEntries,\n  // and then resume, and try to get them a second time.\n  if (self._gotEntries) return\n  self._gotEntries = true\n\n  fs.readdir(self._path, function (er, entries) {\n    if (er) return self.error(er)\n\n    self.entries = entries\n\n    self.emit('entries', entries)\n    if (self._paused) self.once('resume', processEntries)\n    else processEntries()\n\n    function processEntries () {\n      self._length = self.entries.length\n      if (typeof self.sort === 'function') {\n        self.entries = self.entries.sort(self.sort.bind(self))\n      }\n      self._read()\n    }\n  })\n}\n\n// start walking the dir, and emit an \"entry\" event for each one.\nDirReader.prototype._read = function () {\n  var self = this\n\n  if (!self.entries) return self._getEntries()\n\n  if (self._paused || self._currentEntry || self._aborted) {\n    // console.error('DR paused=%j, current=%j, aborted=%j', self._paused, !!self._currentEntry, self._aborted)\n    return\n  }\n\n  self._index++\n  if (self._index >= self.entries.length) {\n    if (!self._ended) {\n      self._ended = true\n      self.emit('end')\n      self.emit('close')\n    }\n    return\n  }\n\n  // ok, handle this one, then.\n\n  // save creating a proxy, by stat'ing the thing now.\n  var p = path.resolve(self._path, self.entries[self._index])\n  assert(p !== self._path)\n  assert(self.entries[self._index])\n\n  // set this to prevent trying to _read() again in the stat time.\n  self._currentEntry = p\n  fs[ self.props.follow ? 'stat' : 'lstat' ](p, function (er, stat) {\n    if (er) return self.error(er)\n\n    var who = self._proxy || self\n\n    stat.path = p\n    stat.basename = path.basename(p)\n    stat.dirname = path.dirname(p)\n    var childProps = self.getChildProps.call(who, stat)\n    childProps.path = p\n    childProps.basename = path.basename(p)\n    childProps.dirname = path.dirname(p)\n\n    var entry = Reader(childProps, stat)\n\n    // console.error(\"DR Entry\", p, stat.size)\n\n    self._currentEntry = entry\n\n    // \"entry\" events are for direct entries in a specific dir.\n    // \"child\" events are for any and all children at all levels.\n    // This nomenclature is not completely final.\n\n    entry.on('pause', function (who) {\n      if (!self._paused && !entry._disowned) {\n        self.pause(who)\n      }\n    })\n\n    entry.on('resume', function (who) {\n      if (self._paused && !entry._disowned) {\n        self.resume(who)\n      }\n    })\n\n    entry.on('stat', function (props) {\n      self.emit('_entryStat', entry, props)\n      if (entry._aborted) return\n      if (entry._paused) {\n        entry.once('resume', function () {\n          self.emit('entryStat', entry, props)\n        })\n      } else self.emit('entryStat', entry, props)\n    })\n\n    entry.on('ready', function EMITCHILD () {\n      // console.error(\"DR emit child\", entry._path)\n      if (self._paused) {\n        // console.error(\"  DR emit child - try again later\")\n        // pause the child, and emit the \"entry\" event once we drain.\n        // console.error(\"DR pausing child entry\")\n        entry.pause(self)\n        return self.once('resume', EMITCHILD)\n      }\n\n      // skip over sockets.  they can't be piped around properly,\n      // so there's really no sense even acknowledging them.\n      // if someone really wants to see them, they can listen to\n      // the \"socket\" events.\n      if (entry.type === 'Socket') {\n        self.emit('socket', entry)\n      } else {\n        self.emitEntry(entry)\n      }\n    })\n\n    var ended = false\n    entry.on('close', onend)\n    entry.on('disown', onend)\n    function onend () {\n      if (ended) return\n      ended = true\n      self.emit('childEnd', entry)\n      self.emit('entryEnd', entry)\n      self._currentEntry = null\n      if (!self._paused) {\n        self._read()\n      }\n    }\n\n    // XXX Remove this.  Works in node as of 0.6.2 or so.\n    // Long filenames should not break stuff.\n    entry.on('error', function (er) {\n      if (entry._swallowErrors) {\n        self.warn(er)\n        entry.emit('end')\n        entry.emit('close')\n      } else {\n        self.emit('error', er)\n      }\n    })\n\n    // proxy up some events.\n    ;[\n      'child',\n      'childEnd',\n      'warn'\n    ].forEach(function (ev) {\n      entry.on(ev, self.emit.bind(self, ev))\n    })\n  })\n}\n\nDirReader.prototype.disown = function (entry) {\n  entry.emit('beforeDisown')\n  entry._disowned = true\n  entry.parent = entry.root = null\n  if (entry === this._currentEntry) {\n    this._currentEntry = null\n  }\n  entry.emit('disown')\n}\n\nDirReader.prototype.getChildProps = function () {\n  return {\n    depth: this.depth + 1,\n    root: this.root || this,\n    parent: this,\n    follow: this.follow,\n    filter: this.filter,\n    sort: this.props.sort,\n    hardlinks: this.props.hardlinks\n  }\n}\n\nDirReader.prototype.pause = function (who) {\n  var self = this\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._currentEntry && self._currentEntry.pause) {\n    self._currentEntry.pause(who)\n  }\n  self.emit('pause', who)\n}\n\nDirReader.prototype.resume = function (who) {\n  var self = this\n  if (!self._paused) return\n  who = who || self\n\n  self._paused = false\n  // console.error('DR Emit Resume', self._path)\n  self.emit('resume', who)\n  if (self._paused) {\n    // console.error('DR Re-paused', self._path)\n    return\n  }\n\n  if (self._currentEntry) {\n    if (self._currentEntry.resume) self._currentEntry.resume(who)\n  } else self._read()\n}\n\nDirReader.prototype.emitEntry = function (entry) {\n  this.emit('entry', entry)\n  this.emit('child', entry)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/dir-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/dir-writer.js":
/*!************************************************!*\
  !*** ./node_modules/fstream/lib/dir-writer.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// It is expected that, when .add() returns false, the consumer\n// of the DirWriter will pause until a \"drain\" event occurs. Note\n// that this is *almost always going to be the case*, unless the\n// thing being written is some sort of unsupported type, and thus\n// skipped over.\n\nmodule.exports = DirWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(ssr)/./node_modules/fstream/node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\n\ninherits(DirWriter, Writer)\n\nfunction DirWriter (props) {\n  var self = this\n  if (!(self instanceof DirWriter)) {\n    self.error('DirWriter must be called as constructor.', null, true)\n  }\n\n  // should already be established as a Directory type\n  if (props.type !== 'Directory' || !props.Directory) {\n    self.error('Non-directory type ' + props.type + ' ' +\n      JSON.stringify(props), null, true)\n  }\n\n  Writer.call(this, props)\n}\n\nDirWriter.prototype._create = function () {\n  var self = this\n  mkdir(self._path, Writer.dirmode, function (er) {\n    if (er) return self.error(er)\n    // ready to start getting entries!\n    self.ready = true\n    self.emit('ready')\n    self._process()\n  })\n}\n\n// a DirWriter has an add(entry) method, but its .write() doesn't\n// do anything.  Why a no-op rather than a throw?  Because this\n// leaves open the door for writing directory metadata for\n// gnu/solaris style dumpdirs.\nDirWriter.prototype.write = function () {\n  return true\n}\n\nDirWriter.prototype.end = function () {\n  this._ended = true\n  this._process()\n}\n\nDirWriter.prototype.add = function (entry) {\n  var self = this\n\n  // console.error('\\tadd', entry._path, '->', self._path)\n  collect(entry)\n  if (!self.ready || self._currentEntry) {\n    self._buffer.push(entry)\n    return false\n  }\n\n  // create a new writer, and pipe the incoming entry into it.\n  if (self._ended) {\n    return self.error('add after end')\n  }\n\n  self._buffer.push(entry)\n  self._process()\n\n  return this._buffer.length === 0\n}\n\nDirWriter.prototype._process = function () {\n  var self = this\n\n  // console.error('DW Process p=%j', self._processing, self.basename)\n\n  if (self._processing) return\n\n  var entry = self._buffer.shift()\n  if (!entry) {\n    // console.error(\"DW Drain\")\n    self.emit('drain')\n    if (self._ended) self._finish()\n    return\n  }\n\n  self._processing = true\n  // console.error(\"DW Entry\", entry._path)\n\n  self.emit('entry', entry)\n\n  // ok, add this entry\n  //\n  // don't allow recursive copying\n  var p = entry\n  var pp\n  do {\n    pp = p._path || p.path\n    if (pp === self.root._path || pp === self._path ||\n      (pp && pp.indexOf(self._path) === 0)) {\n      // console.error('DW Exit (recursive)', entry.basename, self._path)\n      self._processing = false\n      if (entry._collected) entry.pipe()\n      return self._process()\n    }\n    p = p.parent\n  } while (p)\n\n  // console.error(\"DW not recursive\")\n\n  // chop off the entry's root dir, replace with ours\n  var props = {\n    parent: self,\n    root: self.root || self,\n    type: entry.type,\n    depth: self.depth + 1\n  }\n\n  pp = entry._path || entry.path || entry.props.path\n  if (entry.parent) {\n    pp = pp.substr(entry.parent._path.length + 1)\n  }\n  // get rid of any ../../ shenanigans\n  props.path = path.join(self.path, path.join('/', pp))\n\n  // if i have a filter, the child should inherit it.\n  props.filter = self.filter\n\n  // all the rest of the stuff, copy over from the source.\n  Object.keys(entry.props).forEach(function (k) {\n    if (!props.hasOwnProperty(k)) {\n      props[k] = entry.props[k]\n    }\n  })\n\n  // not sure at this point what kind of writer this is.\n  var child = self._currentChild = new Writer(props)\n  child.on('ready', function () {\n    // console.error(\"DW Child Ready\", child.type, child._path)\n    // console.error(\"  resuming\", entry._path)\n    entry.pipe(child)\n    entry.resume()\n  })\n\n  // XXX Make this work in node.\n  // Long filenames should not break stuff.\n  child.on('error', function (er) {\n    if (child._swallowErrors) {\n      self.warn(er)\n      child.emit('end')\n      child.emit('close')\n    } else {\n      self.emit('error', er)\n    }\n  })\n\n  // we fire _end internally *after* end, so that we don't move on\n  // until any \"end\" listeners have had their chance to do stuff.\n  child.on('close', onend)\n  var ended = false\n  function onend () {\n    if (ended) return\n    ended = true\n    // console.error(\"* DW Child end\", child.basename)\n    self._currentChild = null\n    self._processing = false\n    self._process()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/dir-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/file-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.ReadStream\n\nmodule.exports = FileReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar EOF = {EOF: true}\nvar CLOSE = {CLOSE: true}\n\ninherits(FileReader, Reader)\n\nfunction FileReader (props) {\n  // console.error(\"    FR create\", props.path, props.size, new Error().stack)\n  var self = this\n  if (!(self instanceof FileReader)) {\n    throw new Error('FileReader must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  // XXX Todo: preserve hardlinks by tracking dev+inode+nlink,\n  // with a HardLinkReader class.\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'File' && props.File))) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesEmitted = 0\n  Reader.call(self, props)\n}\n\nFileReader.prototype._getStream = function () {\n  var self = this\n  var stream = self._stream = fs.createReadStream(self._path, self.props)\n\n  if (self.props.blksize) {\n    stream.bufferSize = self.props.blksize\n  }\n\n  stream.on('open', self.emit.bind(self, 'open'))\n\n  stream.on('data', function (c) {\n    // console.error('\\t\\t%d %s', c.length, self.basename)\n    self._bytesEmitted += c.length\n    // no point saving empty chunks\n    if (!c.length) {\n      return\n    } else if (self._paused || self._buffer.length) {\n      self._buffer.push(c)\n      self._read()\n    } else self.emit('data', c)\n  })\n\n  stream.on('end', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering End', self._path)\n      self._buffer.push(EOF)\n      self._read()\n    } else {\n      self.emit('end')\n    }\n\n    if (self._bytesEmitted !== self.props.size) {\n      self.error(\"Didn't get expected byte count\\n\" +\n        'expect: ' + self.props.size + '\\n' +\n        'actual: ' + self._bytesEmitted)\n    }\n  })\n\n  stream.on('close', function () {\n    if (self._paused || self._buffer.length) {\n      // console.error('FR Buffering Close', self._path)\n      self._buffer.push(CLOSE)\n      self._read()\n    } else {\n      // console.error('FR close 1', self._path)\n      self.emit('close')\n    }\n  })\n\n  stream.on('error', function (e) {\n    self.emit('error', e)\n  })\n\n  self._read()\n}\n\nFileReader.prototype._read = function () {\n  var self = this\n  // console.error('FR _read', self._path)\n  if (self._paused) {\n    // console.error('FR _read paused', self._path)\n    return\n  }\n\n  if (!self._stream) {\n    // console.error('FR _getStream calling', self._path)\n    return self._getStream()\n  }\n\n  // clear out the buffer, if there is one.\n  if (self._buffer.length) {\n    // console.error('FR _read has buffer', self._buffer.length, self._path)\n    var buf = self._buffer\n    for (var i = 0, l = buf.length; i < l; i++) {\n      var c = buf[i]\n      if (c === EOF) {\n        // console.error('FR Read emitting buffered end', self._path)\n        self.emit('end')\n      } else if (c === CLOSE) {\n        // console.error('FR Read emitting buffered close', self._path)\n        self.emit('close')\n      } else {\n        // console.error('FR Read emitting buffered data', self._path)\n        self.emit('data', c)\n      }\n\n      if (self._paused) {\n        // console.error('FR Read Re-pausing at '+i, self._path)\n        self._buffer = buf.slice(i)\n        return\n      }\n    }\n    self._buffer.length = 0\n  }\n// console.error(\"FR _read done\")\n// that's about all there is to it.\n}\n\nFileReader.prototype.pause = function (who) {\n  var self = this\n  // console.error('FR Pause', self._path)\n  if (self._paused) return\n  who = who || self\n  self._paused = true\n  if (self._stream) self._stream.pause()\n  self.emit('pause', who)\n}\n\nFileReader.prototype.resume = function (who) {\n  var self = this\n  // console.error('FR Resume', self._path)\n  if (!self._paused) return\n  who = who || self\n  self.emit('resume', who)\n  self._paused = false\n  if (self._stream) self._stream.resume()\n  self._read()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/file-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/file-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/file-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = FileWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar EOF = {}\n\ninherits(FileWriter, Writer)\n\nfunction FileWriter (props) {\n  var self = this\n  if (!(self instanceof FileWriter)) {\n    throw new Error('FileWriter must be called as constructor.')\n  }\n\n  // should already be established as a File type\n  if (props.type !== 'File' || !props.File) {\n    throw new Error('Non-file type ' + props.type)\n  }\n\n  self._buffer = []\n  self._bytesWritten = 0\n\n  Writer.call(this, props)\n}\n\nFileWriter.prototype._create = function () {\n  var self = this\n  if (self._stream) return\n\n  var so = {}\n  if (self.props.flags) so.flags = self.props.flags\n  so.mode = Writer.filemode\n  if (self._old && self._old.blksize) so.bufferSize = self._old.blksize\n\n  self._stream = fs.createWriteStream(self._path, so)\n\n  self._stream.on('open', function () {\n    // console.error(\"FW open\", self._buffer, self._path)\n    self.ready = true\n    self._buffer.forEach(function (c) {\n      if (c === EOF) self._stream.end()\n      else self._stream.write(c)\n    })\n    self.emit('ready')\n    // give this a kick just in case it needs it.\n    self.emit('drain')\n  })\n\n  self._stream.on('error', function (er) { self.emit('error', er) })\n\n  self._stream.on('drain', function () { self.emit('drain') })\n\n  self._stream.on('close', function () {\n    // console.error('\\n\\nFW Stream Close', self._path, self.size)\n    self._finish()\n  })\n}\n\nFileWriter.prototype.write = function (c) {\n  var self = this\n\n  self._bytesWritten += c.length\n\n  if (!self.ready) {\n    if (!Buffer.isBuffer(c) && typeof c !== 'string') {\n      throw new Error('invalid write data')\n    }\n    self._buffer.push(c)\n    return false\n  }\n\n  var ret = self._stream.write(c)\n  // console.error('\\t-- fw wrote, _stream says', ret, self._stream._queue.length)\n\n  // allow 2 buffered writes, because otherwise there's just too\n  // much stop and go bs.\n  if (ret === false && self._stream._queue) {\n    return self._stream._queue.length <= 2\n  } else {\n    return ret\n  }\n}\n\nFileWriter.prototype.end = function (c) {\n  var self = this\n\n  if (c) self.write(c)\n\n  if (!self.ready) {\n    self._buffer.push(EOF)\n    return false\n  }\n\n  return self._stream.end()\n}\n\nFileWriter.prototype._finish = function () {\n  var self = this\n  if (typeof self.size === 'number' && self._bytesWritten !== self.size) {\n    self.error(\n      'Did not get expected byte count.\\n' +\n      'expect: ' + self.size + '\\n' +\n      'actual: ' + self._bytesWritten)\n  }\n  Writer.prototype._finish.call(self)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/file-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/get-type.js":
/*!**********************************************!*\
  !*** ./node_modules/fstream/lib/get-type.js ***!
  \**********************************************/
/***/ ((module) => {

eval("module.exports = getType\n\nfunction getType (st) {\n  var types = [\n    'Directory',\n    'File',\n    'SymbolicLink',\n    'Link', // special for hardlinks from tarballs\n    'BlockDevice',\n    'CharacterDevice',\n    'FIFO',\n    'Socket'\n  ]\n  var type\n\n  if (st.type && types.indexOf(st.type) !== -1) {\n    st[st.type] = true\n    return st.type\n  }\n\n  for (var i = 0, l = types.length; i < l; i++) {\n    type = types[i]\n    var is = st[type] || st['is' + type]\n    if (typeof is === 'function') is = is.call(st)\n    if (is) {\n      st[type] = true\n      st.type = type\n      return type\n    }\n  }\n\n  return null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvZ2V0LXR5cGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9DQUFvQyxPQUFPO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcZnN0cmVhbVxcbGliXFxnZXQtdHlwZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IGdldFR5cGVcblxuZnVuY3Rpb24gZ2V0VHlwZSAoc3QpIHtcbiAgdmFyIHR5cGVzID0gW1xuICAgICdEaXJlY3RvcnknLFxuICAgICdGaWxlJyxcbiAgICAnU3ltYm9saWNMaW5rJyxcbiAgICAnTGluaycsIC8vIHNwZWNpYWwgZm9yIGhhcmRsaW5rcyBmcm9tIHRhcmJhbGxzXG4gICAgJ0Jsb2NrRGV2aWNlJyxcbiAgICAnQ2hhcmFjdGVyRGV2aWNlJyxcbiAgICAnRklGTycsXG4gICAgJ1NvY2tldCdcbiAgXVxuICB2YXIgdHlwZVxuXG4gIGlmIChzdC50eXBlICYmIHR5cGVzLmluZGV4T2Yoc3QudHlwZSkgIT09IC0xKSB7XG4gICAgc3Rbc3QudHlwZV0gPSB0cnVlXG4gICAgcmV0dXJuIHN0LnR5cGVcbiAgfVxuXG4gIGZvciAodmFyIGkgPSAwLCBsID0gdHlwZXMubGVuZ3RoOyBpIDwgbDsgaSsrKSB7XG4gICAgdHlwZSA9IHR5cGVzW2ldXG4gICAgdmFyIGlzID0gc3RbdHlwZV0gfHwgc3RbJ2lzJyArIHR5cGVdXG4gICAgaWYgKHR5cGVvZiBpcyA9PT0gJ2Z1bmN0aW9uJykgaXMgPSBpcy5jYWxsKHN0KVxuICAgIGlmIChpcykge1xuICAgICAgc3RbdHlwZV0gPSB0cnVlXG4gICAgICBzdC50eXBlID0gdHlwZVxuICAgICAgcmV0dXJuIHR5cGVcbiAgICB9XG4gIH1cblxuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/get-type.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/link-reader.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-reader.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Basically just a wrapper around an fs.readlink\n//\n// XXX: Enhance this to support the Link type, by keeping\n// a lookup table of {<dev+inode>:<path>}, so that hardlinks\n// can be preserved in tarballs.\n\nmodule.exports = LinkReader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\n\ninherits(LinkReader, Reader)\n\nfunction LinkReader (props) {\n  var self = this\n  if (!(self instanceof LinkReader)) {\n    throw new Error('LinkReader must be called as constructor.')\n  }\n\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\n// When piping a LinkReader into a LinkWriter, we have to\n// already have the linkpath property set, so that has to\n// happen *before* the \"ready\" event, which means we need to\n// override the _stat method.\nLinkReader.prototype._stat = function (currentStat) {\n  var self = this\n  fs.readlink(self._path, function (er, linkpath) {\n    if (er) return self.error(er)\n    self.linkpath = self.props.linkpath = linkpath\n    self.emit('linkpath', linkpath)\n    Reader.prototype._stat.call(self, currentStat)\n  })\n}\n\nLinkReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we need\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/link-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/link-writer.js":
/*!*************************************************!*\
  !*** ./node_modules/fstream/lib/link-writer.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = LinkWriter\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"rimraf\")\n\ninherits(LinkWriter, Writer)\n\nfunction LinkWriter (props) {\n  var self = this\n  if (!(self instanceof LinkWriter)) {\n    throw new Error('LinkWriter must be called as constructor.')\n  }\n\n  // should already be established as a Link type\n  if (!((props.type === 'Link' && props.Link) ||\n    (props.type === 'SymbolicLink' && props.SymbolicLink))) {\n    throw new Error('Non-link type ' + props.type)\n  }\n\n  if (props.linkpath === '') props.linkpath = '.'\n  if (!props.linkpath) {\n    self.error('Need linkpath property to create ' + props.type)\n  }\n\n  Writer.call(this, props)\n}\n\nLinkWriter.prototype._create = function () {\n  // console.error(\" LW _create\")\n  var self = this\n  var hard = self.type === 'Link' || process.platform === 'win32'\n  var link = hard ? 'link' : 'symlink'\n  var lp = hard ? path.resolve(self.dirname, self.linkpath) : self.linkpath\n\n  // can only change the link path by clobbering\n  // For hard links, let's just assume that's always the case, since\n  // there's no good way to read them if we don't already know.\n  if (hard) return clobber(self, lp, link)\n\n  fs.readlink(self._path, function (er, p) {\n    // only skip creation if it's exactly the same link\n    if (p && p === lp) return finish(self)\n    clobber(self, lp, link)\n  })\n}\n\nfunction clobber (self, lp, link) {\n  rimraf(self._path, function (er) {\n    if (er) return self.error(er)\n    create(self, lp, link)\n  })\n}\n\nfunction create (self, lp, link) {\n  fs[link](lp, self._path, function (er) {\n    // if this is a hard link, and we're in the process of writing out a\n    // directory, it's very possible that the thing we're linking to\n    // doesn't exist yet (especially if it was intended as a symlink),\n    // so swallow ENOENT errors here and just soldier in.\n    // Additionally, an EPERM or EACCES can happen on win32 if it's trying\n    // to make a link to a directory.  Again, just skip it.\n    // A better solution would be to have fs.symlink be supported on\n    // windows in some nice fashion.\n    if (er) {\n      if ((er.code === 'ENOENT' ||\n        er.code === 'EACCES' ||\n        er.code === 'EPERM') && process.platform === 'win32') {\n        self.ready = true\n        self.emit('ready')\n        self.emit('end')\n        self.emit('close')\n        self.end = self._finish = function () {}\n      } else return self.error(er)\n    }\n    finish(self)\n  })\n}\n\nfunction finish (self) {\n  self.ready = true\n  self.emit('ready')\n  if (self._ended && !self._finished) self._finish()\n}\n\nLinkWriter.prototype.end = function () {\n  // console.error(\"LW finish in end\")\n  this._ended = true\n  if (this.ready) {\n    this._finished = true\n    this._finish()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/link-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/proxy-reader.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-reader.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A reader for when we don't yet know what kind of thing\n// the thing is.\n\nmodule.exports = ProxyReader\n\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\n\ninherits(ProxyReader, Reader)\n\nfunction ProxyReader (props) {\n  var self = this\n  if (!(self instanceof ProxyReader)) {\n    throw new Error('ProxyReader must be called as constructor.')\n  }\n\n  self.props = props\n  self._buffer = []\n  self.ready = false\n\n  Reader.call(self, props)\n}\n\nProxyReader.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Reader(props, current))\n  })\n}\n\nProxyReader.prototype._addProxy = function (proxy) {\n  var self = this\n  if (self._proxyTarget) {\n    return self.error('proxy already set')\n  }\n\n  self._proxyTarget = proxy\n  proxy._proxy = self\n\n  ;[\n    'error',\n    'data',\n    'end',\n    'close',\n    'linkpath',\n    'entry',\n    'entryEnd',\n    'child',\n    'childEnd',\n    'warn',\n    'stat'\n  ].forEach(function (ev) {\n    // console.error('~~ proxy event', ev, self.path)\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  proxy.on('ready', function () {\n    // console.error(\"~~ proxy is ready!\", self.path)\n    self.ready = true\n    self.emit('ready')\n  })\n\n  var calls = self._buffer\n  self._buffer.length = 0\n  calls.forEach(function (c) {\n    proxy[c[0]].apply(proxy, c[1])\n  })\n}\n\nProxyReader.prototype.pause = function () {\n  return this._proxyTarget ? this._proxyTarget.pause() : false\n}\n\nProxyReader.prototype.resume = function () {\n  return this._proxyTarget ? this._proxyTarget.resume() : false\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/proxy-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/proxy-writer.js":
/*!**************************************************!*\
  !*** ./node_modules/fstream/lib/proxy-writer.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// A writer for when we don't know what kind of thing\n// the thing is.  That is, it's not explicitly set,\n// so we're going to make it whatever the thing already\n// is, or \"File\"\n//\n// Until then, collect all events.\n\nmodule.exports = ProxyWriter\n\nvar Writer = __webpack_require__(/*! ./writer.js */ \"(ssr)/./node_modules/fstream/lib/writer.js\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar collect = __webpack_require__(/*! ./collect.js */ \"(ssr)/./node_modules/fstream/lib/collect.js\")\nvar fs = __webpack_require__(/*! fs */ \"fs\")\n\ninherits(ProxyWriter, Writer)\n\nfunction ProxyWriter (props) {\n  var self = this\n  if (!(self instanceof ProxyWriter)) {\n    throw new Error('ProxyWriter must be called as constructor.')\n  }\n\n  self.props = props\n  self._needDrain = false\n\n  Writer.call(self, props)\n}\n\nProxyWriter.prototype._stat = function () {\n  var self = this\n  var props = self.props\n  // stat the thing to see what the proxy should be.\n  var stat = props.follow ? 'stat' : 'lstat'\n\n  fs[stat](props.path, function (er, current) {\n    var type\n    if (er || !current) {\n      type = 'File'\n    } else {\n      type = getType(current)\n    }\n\n    props[type] = true\n    props.type = self.type = type\n\n    self._old = current\n    self._addProxy(Writer(props, current))\n  })\n}\n\nProxyWriter.prototype._addProxy = function (proxy) {\n  // console.error(\"~~ set proxy\", this.path)\n  var self = this\n  if (self._proxy) {\n    return self.error('proxy already set')\n  }\n\n  self._proxy = proxy\n  ;[\n    'ready',\n    'error',\n    'close',\n    'pipe',\n    'drain',\n    'warn'\n  ].forEach(function (ev) {\n    proxy.on(ev, self.emit.bind(self, ev))\n  })\n\n  self.emit('proxy', proxy)\n\n  var calls = self._buffer\n  calls.forEach(function (c) {\n    // console.error(\"~~ ~~ proxy buffered call\", c[0], c[1])\n    proxy[c[0]].apply(proxy, c[1])\n  })\n  self._buffer.length = 0\n  if (self._needsDrain) self.emit('drain')\n}\n\nProxyWriter.prototype.add = function (entry) {\n  // console.error(\"~~ proxy add\")\n  collect(entry)\n\n  if (!this._proxy) {\n    this._buffer.push(['add', [entry]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.add(entry)\n}\n\nProxyWriter.prototype.write = function (c) {\n  // console.error('~~ proxy write')\n  if (!this._proxy) {\n    this._buffer.push(['write', [c]])\n    this._needDrain = true\n    return false\n  }\n  return this._proxy.write(c)\n}\n\nProxyWriter.prototype.end = function (c) {\n  // console.error('~~ proxy end')\n  if (!this._proxy) {\n    this._buffer.push(['end', [c]])\n    return false\n  }\n  return this._proxy.end(c)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/proxy-writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/reader.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/reader.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Reader\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar Stream = (__webpack_require__(/*! stream */ \"stream\").Stream)\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar hardLinks = Reader.hardLinks = {}\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Reader, Abstract)\n\nvar LinkReader = __webpack_require__(/*! ./link-reader.js */ \"(ssr)/./node_modules/fstream/lib/link-reader.js\")\n\nfunction Reader (props, currentStat) {\n  var self = this\n  if (!(self instanceof Reader)) return new Reader(props, currentStat)\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Reader(dir) to get a DirReader object, etc.\n  // Note that, unlike in the Writer case, ProxyReader is going\n  // to be the *normal* state of affairs, since we rarely know\n  // the type of a file prior to reading it.\n\n  var type\n  var ClassType\n\n  if (props.type && typeof props.type === 'function') {\n    type = props.type\n    ClassType = type\n  } else {\n    type = getType(props)\n    ClassType = Reader\n  }\n\n  if (currentStat && !type) {\n    type = getType(currentStat)\n    props[type] = true\n    props.type = type\n  }\n\n  switch (type) {\n    case 'Directory':\n      ClassType = __webpack_require__(/*! ./dir-reader.js */ \"(ssr)/./node_modules/fstream/lib/dir-reader.js\")\n      break\n\n    case 'Link':\n    // XXX hard links are just files.\n    // However, it would be good to keep track of files' dev+inode\n    // and nlink values, and create a HardLinkReader that emits\n    // a linkpath value of the original copy, so that the tar\n    // writer can preserve them.\n    // ClassType = HardLinkReader\n    // break\n\n    case 'File':\n      ClassType = __webpack_require__(/*! ./file-reader.js */ \"(ssr)/./node_modules/fstream/lib/file-reader.js\")\n      break\n\n    case 'SymbolicLink':\n      ClassType = LinkReader\n      break\n\n    case 'Socket':\n      ClassType = __webpack_require__(/*! ./socket-reader.js */ \"(ssr)/./node_modules/fstream/lib/socket-reader.js\")\n      break\n\n    case null:\n      ClassType = __webpack_require__(/*! ./proxy-reader.js */ \"(ssr)/./node_modules/fstream/lib/proxy-reader.js\")\n      break\n  }\n\n  if (!(self instanceof ClassType)) {\n    return new ClassType(props)\n  }\n\n  Abstract.call(self)\n\n  if (!props.path) {\n    self.error('Must provide a path', null, true)\n  }\n\n  self.readable = true\n  self.writable = false\n\n  self.type = type\n  self.props = props\n  self.depth = props.depth = props.depth || 0\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      // how DOES one create files on the moon?\n      // if the path has spaces in it, then UNC will fail.\n      self._swallowErrors = true\n      // if (self._path.indexOf(\" \") === -1) {\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    // }\n    }\n  }\n  self.basename = props.basename = path.basename(self.path)\n  self.dirname = props.dirname = path.dirname(self.path)\n\n  // these have served their purpose, and are now just noisy clutter\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n  if (props.sort === 'alpha') props.sort = alphasort\n\n  // start the ball rolling.\n  // this will stat the thing, and then call self._read()\n  // to start reading whatever it is.\n  // console.error(\"calling stat\", props.path, currentStat)\n  self._stat(currentStat)\n}\n\nfunction alphasort (a, b) {\n  return a === b ? 0\n    : a.toLowerCase() > b.toLowerCase() ? 1\n      : a.toLowerCase() < b.toLowerCase() ? -1\n        : a > b ? 1\n          : -1\n}\n\nReader.prototype._stat = function (currentStat) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  // console.error(\"Reader._stat\", self._path, currentStat)\n  if (currentStat) process.nextTick(statCb.bind(null, null, currentStat))\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, props_) {\n    // console.error(\"Reader._stat, statCb\", self._path, props_, props_.nlink)\n    if (er) return self.error(er)\n\n    Object.keys(props_).forEach(function (k) {\n      props[k] = props_[k]\n    })\n\n    // if it's not the expected size, then abort here.\n    if (undefined !== self.size && props.size !== self.size) {\n      return self.error('incorrect size')\n    }\n    self.size = props.size\n\n    var type = getType(props)\n    var handleHardlinks = props.hardlinks !== false\n\n    // special little thing for handling hardlinks.\n    if (handleHardlinks && type !== 'Directory' && props.nlink && props.nlink > 1) {\n      var k = props.dev + ':' + props.ino\n      // console.error(\"Reader has nlink\", self._path, k)\n      if (hardLinks[k] === self._path || !hardLinks[k]) {\n        hardLinks[k] = self._path\n      } else {\n        // switch into hardlink mode.\n        type = self.type = self.props.type = 'Link'\n        self.Link = self.props.Link = true\n        self.linkpath = self.props.linkpath = hardLinks[k]\n        // console.error(\"Hardlink detected, switching mode\", self._path, self.linkpath)\n        // Setting __proto__ would arguably be the \"correct\"\n        // approach here, but that just seems too wrong.\n        self._stat = self._read = LinkReader.prototype._read\n      }\n    }\n\n    if (self.type && self.type !== type) {\n      self.error('Unexpected type: ' + type)\n    }\n\n    // if the filter doesn't pass, then just skip over this one.\n    // still have to emit end so that dir-walking can move on.\n    if (self.filter) {\n      var who = self._proxy || self\n      // special handling for ProxyReaders\n      if (!self.filter.call(who, who, props)) {\n        if (!self._disowned) {\n          self.abort()\n          self.emit('end')\n          self.emit('close')\n        }\n        return\n      }\n    }\n\n    // last chance to abort or disown before the flow starts!\n    var events = ['_stat', 'stat', 'ready']\n    var e = 0\n    ;(function go () {\n      if (self._aborted) {\n        self.emit('end')\n        self.emit('close')\n        return\n      }\n\n      if (self._paused && self.type !== 'Directory') {\n        self.once('resume', go)\n        return\n      }\n\n      var ev = events[e++]\n      if (!ev) {\n        return self._read()\n      }\n      self.emit(ev, props)\n      go()\n    })()\n  }\n}\n\nReader.prototype.pipe = function (dest) {\n  var self = this\n  if (typeof dest.add === 'function') {\n    // piping to a multi-compatible, and we've got directory entries.\n    self.on('entry', function (entry) {\n      var ret = dest.add(entry)\n      if (ret === false) {\n        self.pause()\n      }\n    })\n  }\n\n  // console.error(\"R Pipe apply Stream Pipe\")\n  return Stream.prototype.pipe.apply(this, arguments)\n}\n\nReader.prototype.pause = function (who) {\n  this._paused = true\n  who = who || this\n  this.emit('pause', who)\n  if (this._stream) this._stream.pause(who)\n}\n\nReader.prototype.resume = function (who) {\n  this._paused = false\n  who = who || this\n  this.emit('resume', who)\n  if (this._stream) this._stream.resume(who)\n  this._read()\n}\n\nReader.prototype._read = function () {\n  this.error('Cannot read unknown type: ' + this.type)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/socket-reader.js":
/*!***************************************************!*\
  !*** ./node_modules/fstream/lib/socket-reader.js ***!
  \***************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Just get the stats, and then don't do anything.\n// You can't really \"read\" from a socket.  You \"connect\" to it.\n// Mostly, this is here so that reading a dir with a socket in it\n// doesn't blow up.\n\nmodule.exports = SocketReader\n\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar Reader = __webpack_require__(/*! ./reader.js */ \"(ssr)/./node_modules/fstream/lib/reader.js\")\n\ninherits(SocketReader, Reader)\n\nfunction SocketReader (props) {\n  var self = this\n  if (!(self instanceof SocketReader)) {\n    throw new Error('SocketReader must be called as constructor.')\n  }\n\n  if (!(props.type === 'Socket' && props.Socket)) {\n    throw new Error('Non-socket type ' + props.type)\n  }\n\n  Reader.call(self, props)\n}\n\nSocketReader.prototype._read = function () {\n  var self = this\n  if (self._paused) return\n  // basically just a no-op, since we got all the info we have\n  // from the _stat method\n  if (!self._ended) {\n    self.emit('end')\n    self.emit('close')\n    self._ended = true\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZnN0cmVhbS9saWIvc29ja2V0LXJlYWRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxlQUFlLG1CQUFPLENBQUMsMkRBQVU7QUFDakMsYUFBYSxtQkFBTyxDQUFDLCtEQUFhOztBQUVsQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxmc3RyZWFtXFxsaWJcXHNvY2tldC1yZWFkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gSnVzdCBnZXQgdGhlIHN0YXRzLCBhbmQgdGhlbiBkb24ndCBkbyBhbnl0aGluZy5cbi8vIFlvdSBjYW4ndCByZWFsbHkgXCJyZWFkXCIgZnJvbSBhIHNvY2tldC4gIFlvdSBcImNvbm5lY3RcIiB0byBpdC5cbi8vIE1vc3RseSwgdGhpcyBpcyBoZXJlIHNvIHRoYXQgcmVhZGluZyBhIGRpciB3aXRoIGEgc29ja2V0IGluIGl0XG4vLyBkb2Vzbid0IGJsb3cgdXAuXG5cbm1vZHVsZS5leHBvcnRzID0gU29ja2V0UmVhZGVyXG5cbnZhciBpbmhlcml0cyA9IHJlcXVpcmUoJ2luaGVyaXRzJylcbnZhciBSZWFkZXIgPSByZXF1aXJlKCcuL3JlYWRlci5qcycpXG5cbmluaGVyaXRzKFNvY2tldFJlYWRlciwgUmVhZGVyKVxuXG5mdW5jdGlvbiBTb2NrZXRSZWFkZXIgKHByb3BzKSB7XG4gIHZhciBzZWxmID0gdGhpc1xuICBpZiAoIShzZWxmIGluc3RhbmNlb2YgU29ja2V0UmVhZGVyKSkge1xuICAgIHRocm93IG5ldyBFcnJvcignU29ja2V0UmVhZGVyIG11c3QgYmUgY2FsbGVkIGFzIGNvbnN0cnVjdG9yLicpXG4gIH1cblxuICBpZiAoIShwcm9wcy50eXBlID09PSAnU29ja2V0JyAmJiBwcm9wcy5Tb2NrZXQpKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdOb24tc29ja2V0IHR5cGUgJyArIHByb3BzLnR5cGUpXG4gIH1cblxuICBSZWFkZXIuY2FsbChzZWxmLCBwcm9wcylcbn1cblxuU29ja2V0UmVhZGVyLnByb3RvdHlwZS5fcmVhZCA9IGZ1bmN0aW9uICgpIHtcbiAgdmFyIHNlbGYgPSB0aGlzXG4gIGlmIChzZWxmLl9wYXVzZWQpIHJldHVyblxuICAvLyBiYXNpY2FsbHkganVzdCBhIG5vLW9wLCBzaW5jZSB3ZSBnb3QgYWxsIHRoZSBpbmZvIHdlIGhhdmVcbiAgLy8gZnJvbSB0aGUgX3N0YXQgbWV0aG9kXG4gIGlmICghc2VsZi5fZW5kZWQpIHtcbiAgICBzZWxmLmVtaXQoJ2VuZCcpXG4gICAgc2VsZi5lbWl0KCdjbG9zZScpXG4gICAgc2VsZi5fZW5kZWQgPSB0cnVlXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/socket-reader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/lib/writer.js":
/*!********************************************!*\
  !*** ./node_modules/fstream/lib/writer.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = Writer\n\nvar fs = __webpack_require__(/*! graceful-fs */ \"(ssr)/./node_modules/graceful-fs/graceful-fs.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\")\nvar rimraf = __webpack_require__(/*! rimraf */ \"rimraf\")\nvar mkdir = __webpack_require__(/*! mkdirp */ \"(ssr)/./node_modules/fstream/node_modules/mkdirp/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar umask = process.platform === 'win32' ? 0 : process.umask()\nvar getType = __webpack_require__(/*! ./get-type.js */ \"(ssr)/./node_modules/fstream/lib/get-type.js\")\nvar Abstract = __webpack_require__(/*! ./abstract.js */ \"(ssr)/./node_modules/fstream/lib/abstract.js\")\n\n// Must do this *before* loading the child classes\ninherits(Writer, Abstract)\n\nWriter.dirmode = parseInt('0777', 8) & (~umask)\nWriter.filemode = parseInt('0666', 8) & (~umask)\n\nvar DirWriter = __webpack_require__(/*! ./dir-writer.js */ \"(ssr)/./node_modules/fstream/lib/dir-writer.js\")\nvar LinkWriter = __webpack_require__(/*! ./link-writer.js */ \"(ssr)/./node_modules/fstream/lib/link-writer.js\")\nvar FileWriter = __webpack_require__(/*! ./file-writer.js */ \"(ssr)/./node_modules/fstream/lib/file-writer.js\")\nvar ProxyWriter = __webpack_require__(/*! ./proxy-writer.js */ \"(ssr)/./node_modules/fstream/lib/proxy-writer.js\")\n\n// props is the desired state.  current is optionally the current stat,\n// provided here so that subclasses can avoid statting the target\n// more than necessary.\nfunction Writer (props, current) {\n  var self = this\n\n  if (typeof props === 'string') {\n    props = { path: props }\n  }\n\n  // polymorphism.\n  // call fstream.Writer(dir) to get a DirWriter object, etc.\n  var type = getType(props)\n  var ClassType = Writer\n\n  switch (type) {\n    case 'Directory':\n      ClassType = DirWriter\n      break\n    case 'File':\n      ClassType = FileWriter\n      break\n    case 'Link':\n    case 'SymbolicLink':\n      ClassType = LinkWriter\n      break\n    case null:\n    default:\n      // Don't know yet what type to create, so we wrap in a proxy.\n      ClassType = ProxyWriter\n      break\n  }\n\n  if (!(self instanceof ClassType)) return new ClassType(props)\n\n  // now get down to business.\n\n  Abstract.call(self)\n\n  if (!props.path) self.error('Must provide a path', null, true)\n\n  // props is what we want to set.\n  // set some convenience properties as well.\n  self.type = props.type\n  self.props = props\n  self.depth = props.depth || 0\n  self.clobber = props.clobber === false ? props.clobber : true\n  self.parent = props.parent || null\n  self.root = props.root || (props.parent && props.parent.root) || self\n\n  self._path = self.path = path.resolve(props.path)\n  if (process.platform === 'win32') {\n    self.path = self._path = self.path.replace(/\\?/g, '_')\n    if (self._path.length >= 260) {\n      self._swallowErrors = true\n      self._path = '\\\\\\\\?\\\\' + self.path.replace(/\\//g, '\\\\')\n    }\n  }\n  self.basename = path.basename(props.path)\n  self.dirname = path.dirname(props.path)\n  self.linkpath = props.linkpath || null\n\n  props.parent = props.root = null\n\n  // console.error(\"\\n\\n\\n%s setting size to\", props.path, props.size)\n  self.size = props.size\n\n  if (typeof props.mode === 'string') {\n    props.mode = parseInt(props.mode, 8)\n  }\n\n  self.readable = false\n  self.writable = true\n\n  // buffer until ready, or while handling another entry\n  self._buffer = []\n  self.ready = false\n\n  self.filter = typeof props.filter === 'function' ? props.filter : null\n\n  // start the ball rolling.\n  // this checks what's there already, and then calls\n  // self._create() to call the impl-specific creation stuff.\n  self._stat(current)\n}\n\n// Calling this means that it's something we can't create.\n// Just assert that it's already there, otherwise raise a warning.\nWriter.prototype._create = function () {\n  var self = this\n  fs[self.props.follow ? 'stat' : 'lstat'](self._path, function (er) {\n    if (er) {\n      return self.warn('Cannot create ' + self._path + '\\n' +\n        'Unsupported type: ' + self.type, 'ENOTSUP')\n    }\n    self._finish()\n  })\n}\n\nWriter.prototype._stat = function (current) {\n  var self = this\n  var props = self.props\n  var stat = props.follow ? 'stat' : 'lstat'\n  var who = self._proxy || self\n\n  if (current) statCb(null, current)\n  else fs[stat](self._path, statCb)\n\n  function statCb (er, current) {\n    if (self.filter && !self.filter.call(who, who, current)) {\n      self._aborted = true\n      self.emit('end')\n      self.emit('close')\n      return\n    }\n\n    // if it's not there, great.  We'll just create it.\n    // if it is there, then we'll need to change whatever differs\n    if (er || !current) {\n      return create(self)\n    }\n\n    self._old = current\n    var currentType = getType(current)\n\n    // if it's a type change, then we need to clobber or error.\n    // if it's not a type change, then let the impl take care of it.\n    if (currentType !== self.type || self.type === 'File' && current.nlink > 1) {\n      return rimraf(self._path, function (er) {\n        if (er) return self.error(er)\n        self._old = null\n        create(self)\n      })\n    }\n\n    // otherwise, just handle in the app-specific way\n    // this creates a fs.WriteStream, or mkdir's, or whatever\n    create(self)\n  }\n}\n\nfunction create (self) {\n  // console.error(\"W create\", self._path, Writer.dirmode)\n\n  // XXX Need to clobber non-dirs that are in the way,\n  // unless { clobber: false } in the props.\n  mkdir(path.dirname(self._path), Writer.dirmode, function (er, made) {\n    // console.error(\"W created\", path.dirname(self._path), er)\n    if (er) return self.error(er)\n\n    // later on, we have to set the mode and owner for these\n    self._madeDir = made\n    return self._create()\n  })\n}\n\nfunction endChmod (self, want, current, path, cb) {\n  var wantMode = want.mode\n  var chmod = want.follow || self.type !== 'SymbolicLink'\n    ? 'chmod' : 'lchmod'\n\n  if (!fs[chmod]) return cb()\n  if (typeof wantMode !== 'number') return cb()\n\n  var curMode = current.mode & parseInt('0777', 8)\n  wantMode = wantMode & parseInt('0777', 8)\n  if (wantMode === curMode) return cb()\n\n  fs[chmod](path, wantMode, cb)\n}\n\nfunction endChown (self, want, current, path, cb) {\n  // Don't even try it unless root.  Too easy to EPERM.\n  if (process.platform === 'win32') return cb()\n  if (!process.getuid || process.getuid() !== 0) return cb()\n  if (typeof want.uid !== 'number' &&\n    typeof want.gid !== 'number') return cb()\n\n  if (current.uid === want.uid &&\n    current.gid === want.gid) return cb()\n\n  var chown = (self.props.follow || self.type !== 'SymbolicLink')\n    ? 'chown' : 'lchown'\n  if (!fs[chown]) return cb()\n\n  if (typeof want.uid !== 'number') want.uid = current.uid\n  if (typeof want.gid !== 'number') want.gid = current.gid\n\n  fs[chown](path, want.uid, want.gid, cb)\n}\n\nfunction endUtimes (self, want, current, path, cb) {\n  if (!fs.utimes || process.platform === 'win32') return cb()\n\n  var utimes = (want.follow || self.type !== 'SymbolicLink')\n    ? 'utimes' : 'lutimes'\n\n  if (utimes === 'lutimes' && !fs[utimes]) {\n    utimes = 'utimes'\n  }\n\n  if (!fs[utimes]) return cb()\n\n  var curA = current.atime\n  var curM = current.mtime\n  var meA = want.atime\n  var meM = want.mtime\n\n  if (meA === undefined) meA = curA\n  if (meM === undefined) meM = curM\n\n  if (!isDate(meA)) meA = new Date(meA)\n  if (!isDate(meM)) meA = new Date(meM)\n\n  if (meA.getTime() === curA.getTime() &&\n    meM.getTime() === curM.getTime()) return cb()\n\n  fs[utimes](path, meA, meM, cb)\n}\n\n// XXX This function is beastly.  Break it up!\nWriter.prototype._finish = function () {\n  var self = this\n\n  if (self._finishing) return\n  self._finishing = true\n\n  // console.error(\" W Finish\", self._path, self.size)\n\n  // set up all the things.\n  // At this point, we're already done writing whatever we've gotta write,\n  // adding files to the dir, etc.\n  var todo = 0\n  var errState = null\n  var done = false\n\n  if (self._old) {\n    // the times will almost *certainly* have changed.\n    // adds the utimes syscall, but remove another stat.\n    self._old.atime = new Date(0)\n    self._old.mtime = new Date(0)\n    // console.error(\" W Finish Stale Stat\", self._path, self.size)\n    setProps(self._old)\n  } else {\n    var stat = self.props.follow ? 'stat' : 'lstat'\n    // console.error(\" W Finish Stating\", self._path, self.size)\n    fs[stat](self._path, function (er, current) {\n      // console.error(\" W Finish Stated\", self._path, self.size, current)\n      if (er) {\n        // if we're in the process of writing out a\n        // directory, it's very possible that the thing we're linking to\n        // doesn't exist yet (especially if it was intended as a symlink),\n        // so swallow ENOENT errors here and just soldier on.\n        if (er.code === 'ENOENT' &&\n          (self.type === 'Link' || self.type === 'SymbolicLink') &&\n          process.platform === 'win32') {\n          self.ready = true\n          self.emit('ready')\n          self.emit('end')\n          self.emit('close')\n          self.end = self._finish = function () {}\n          return\n        } else return self.error(er)\n      }\n      setProps(self._old = current)\n    })\n  }\n\n  return\n\n  function setProps (current) {\n    todo += 3\n    endChmod(self, self.props, current, self._path, next('chmod'))\n    endChown(self, self.props, current, self._path, next('chown'))\n    endUtimes(self, self.props, current, self._path, next('utimes'))\n  }\n\n  function next (what) {\n    return function (er) {\n      // console.error(\"   W Finish\", what, todo)\n      if (errState) return\n      if (er) {\n        er.fstream_finish_call = what\n        return self.error(errState = er)\n      }\n      if (--todo > 0) return\n      if (done) return\n      done = true\n\n      // we may still need to set the mode/etc. on some parent dirs\n      // that were created previously.  delay end/close until then.\n      if (!self._madeDir) return end()\n      else endMadeDir(self, self._path, end)\n\n      function end (er) {\n        if (er) {\n          er.fstream_finish_call = 'setupMadeDir'\n          return self.error(er)\n        }\n        // all the props have been set, so we're completely done.\n        self.emit('end')\n        self.emit('close')\n      }\n    }\n  }\n}\n\nfunction endMadeDir (self, p, cb) {\n  var made = self._madeDir\n  // everything *between* made and path.dirname(self._path)\n  // needs to be set up.  Note that this may just be one dir.\n  var d = path.dirname(p)\n\n  endMadeDir_(self, d, function (er) {\n    if (er) return cb(er)\n    if (d === made) {\n      return cb()\n    }\n    endMadeDir(self, d, cb)\n  })\n}\n\nfunction endMadeDir_ (self, p, cb) {\n  var dirProps = {}\n  Object.keys(self.props).forEach(function (k) {\n    dirProps[k] = self.props[k]\n\n    // only make non-readable dirs if explicitly requested.\n    if (k === 'mode' && self.type !== 'Directory') {\n      dirProps[k] = dirProps[k] | parseInt('0111', 8)\n    }\n  })\n\n  var todo = 3\n  var errState = null\n  fs.stat(p, function (er, current) {\n    if (er) return cb(errState = er)\n    endChmod(self, dirProps, current, p, next)\n    endChown(self, dirProps, current, p, next)\n    endUtimes(self, dirProps, current, p, next)\n  })\n\n  function next (er) {\n    if (errState) return\n    if (er) return cb(errState = er)\n    if (--todo === 0) return cb()\n  }\n}\n\nWriter.prototype.pipe = function () {\n  this.error(\"Can't pipe from writable stream\")\n}\n\nWriter.prototype.add = function () {\n  this.error(\"Can't add to non-Directory type\")\n}\n\nWriter.prototype.write = function () {\n  return true\n}\n\nfunction objectToString (d) {\n  return Object.prototype.toString.call(d)\n}\n\nfunction isDate (d) {\n  return typeof d === 'object' && objectToString(d) === '[object Date]'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/lib/writer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/fstream/node_modules/mkdirp/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/fstream/node_modules/mkdirp/index.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var path = __webpack_require__(/*! path */ \"path\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar _0777 = parseInt('0777', 8);\n\nmodule.exports = mkdirP.mkdirp = mkdirP.mkdirP = mkdirP;\n\nfunction mkdirP (p, opts, f, made) {\n    if (typeof opts === 'function') {\n        f = opts;\n        opts = {};\n    }\n    else if (!opts || typeof opts !== 'object') {\n        opts = { mode: opts };\n    }\n    \n    var mode = opts.mode;\n    var xfs = opts.fs || fs;\n    \n    if (mode === undefined) {\n        mode = _0777\n    }\n    if (!made) made = null;\n    \n    var cb = f || /* istanbul ignore next */ function () {};\n    p = path.resolve(p);\n    \n    xfs.mkdir(p, mode, function (er) {\n        if (!er) {\n            made = made || p;\n            return cb(null, made);\n        }\n        switch (er.code) {\n            case 'ENOENT':\n                /* istanbul ignore if */\n                if (path.dirname(p) === p) return cb(er);\n                mkdirP(path.dirname(p), opts, function (er, made) {\n                    /* istanbul ignore if */\n                    if (er) cb(er, made);\n                    else mkdirP(p, opts, cb, made);\n                });\n                break;\n\n            // In the case of any other error, just see if there's a dir\n            // there already.  If so, then hooray!  If not, then something\n            // is borked.\n            default:\n                xfs.stat(p, function (er2, stat) {\n                    // if the stat fails, then that's super weird.\n                    // let the original error be the failure reason.\n                    if (er2 || !stat.isDirectory()) cb(er, made)\n                    else cb(null, made);\n                });\n                break;\n        }\n    });\n}\n\nmkdirP.sync = function sync (p, opts, made) {\n    if (!opts || typeof opts !== 'object') {\n        opts = { mode: opts };\n    }\n    \n    var mode = opts.mode;\n    var xfs = opts.fs || fs;\n    \n    if (mode === undefined) {\n        mode = _0777\n    }\n    if (!made) made = null;\n\n    p = path.resolve(p);\n\n    try {\n        xfs.mkdirSync(p, mode);\n        made = made || p;\n    }\n    catch (err0) {\n        switch (err0.code) {\n            case 'ENOENT' :\n                made = sync(path.dirname(p), opts, made);\n                sync(p, opts, made);\n                break;\n\n            // In the case of any other error, just see if there's a dir\n            // there already.  If so, then hooray!  If not, then something\n            // is borked.\n            default:\n                var stat;\n                try {\n                    stat = xfs.statSync(p);\n                }\n                catch (err1) /* istanbul ignore next */ {\n                    throw err0;\n                }\n                /* istanbul ignore if */\n                if (!stat.isDirectory()) throw err0;\n                break;\n        }\n    }\n\n    return made;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fstream/node_modules/mkdirp/index.js\n");

/***/ })

};
;