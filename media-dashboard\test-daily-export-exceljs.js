const ExcelJS = require('exceljs');
const fs = require('fs');

async function createDailyScheduleExport() {
  try {
    // قراءة البيانات المحفوظة
    const savedData = JSON.parse(fs.readFileSync('./saved-schedules/daily-schedule-2025-06-20.json', 'utf8'));
    const scheduleRows = savedData.scheduleRows || [];

    console.log('📊 عدد الصفوف:', scheduleRows.length);

    // إنشاء مصنف جديد
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('جدول إذاعي 2025-06-20', {
      properties: {
        rightToLeft: true
      },
      views: [{
        rightToLeft: true,
        zoomScale: 70
      }]
    });

    // تنسيق الوقت
    function formatTimeWithSeconds(time) {
      if (time && time.includes(':')) {
        const parts = time.split(':');
        if (parts.length === 2) {
          return `${parts[0]}:${parts[1]}:00`;
        }
        return time;
      }
      return '00:00:00';
    }

    // إضافة تاريخ اليوم أعلى الجدول
    worksheet.mergeCells('A1:I1');
    const dateCell = worksheet.getCell('A1');
    dateCell.value = 'تاريخ الإذاعة: 2025-06-20';
    dateCell.font = { name: 'Arial', size: 12, bold: true };
    dateCell.alignment = { horizontal: 'right', vertical: 'middle' };
    dateCell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };

    // صف فارغ
    worksheet.addRow(['', '', '', '', '', '', '', '', '']);

    // إضافة رأس الجدول (من اليمين لليسار - معكوس)
    const headerRow = worksheet.addRow([
      'ID CODE',
      'وقت الإذاعة',
      'TYPE',
      'TITLE',
      'DESCRIPTION',
      'رقم الهارد',
      'IN',
      'OUT',
      'DURATION'
    ]);

    // تنسيق رأس الجدول
    headerRow.eachCell((cell) => {
      cell.font = { name: 'Arial', size: 10, bold: true, color: { argb: 'FFFFFFFF' } };
      cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF4CAF50' } };
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // معالجة كل صف في الجدول
    let exportedRows = 0;
    scheduleRows.forEach((row, index) => {
      if (row.type === 'segment' && row.mediaItemId) {
        // حساب الأوقات
        const broadcastTime = formatTimeWithSeconds(row.time || '00:00:00');
        const duration = row.duration || '00:00:00';
        
        // حساب وقت النهاية
        const [hours, minutes, seconds] = broadcastTime.split(':').map(Number);
        const [durHours, durMinutes, durSeconds] = duration.split(':').map(Number);
        const totalSeconds = (hours * 3600 + minutes * 60 + seconds) + (durHours * 3600 + durMinutes * 60 + durSeconds);
        const endHours = Math.floor(totalSeconds / 3600) % 24;
        const endMinutes = Math.floor((totalSeconds % 3600) / 60);
        const endSecs = totalSeconds % 60;
        const endTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}:${endSecs.toString().padStart(2, '0')}`;
        
        // تحديد النوع
        let type = 'Program';
        if (row.content.includes('STING')) type = 'Sting';
        else if (row.content.includes('PROMO')) type = 'Promo';
        else if (row.content.includes('FILL_IN')) type = 'Fill IN';
        else if (row.content.includes('FILLER')) type = 'Filler';
        else if (row.content.includes('إعادة')) type = 'Rerun';
        
        // تحديد العنوان والوصف
        let title = row.content || 'غير محدد';
        let description = `2025-06-20 ${broadcastTime} ${title}`;
        
        // كود المادة
        const idCode = row.segmentCode || `DPR${String(exportedRows + 1).padStart(5, '0')}`;
        
        // إضافة الصف (معكوس من اليمين لليسار)
        const dataRow = worksheet.addRow([
          idCode,             // ID CODE
          broadcastTime,      // وقت الإذاعة
          type,               // TYPE
          title,              // TITLE
          description,        // DESCRIPTION
          'SERVER',           // رقم الهارد
          broadcastTime,      // IN
          endTime,            // OUT
          duration            // DURATION
        ]);

        // تنسيق الصف حسب النوع
        let backgroundColor = 'FFFFFFFF'; // أبيض افتراضي
        
        switch (type) {
          case 'Program':
          case 'Rerun':
            backgroundColor = 'FF90EE90'; // أخضر فاتح للمحتوى الأساسي
            break;
          case 'Promo':
            backgroundColor = 'FFFFE4B5'; // بيج للبرومو
            break;
          case 'Sting':
            backgroundColor = 'FFFF6B6B'; // أحمر فاتح للستينغ
            break;
          case 'Filler':
            backgroundColor = 'FFFFA500'; // برتقالي للفيلر
            break;
          case 'Fill IN':
            backgroundColor = 'FF87CEEB'; // أزرق فاتح للـ Fill IN
            break;
        }

        dataRow.eachCell((cell) => {
          cell.font = { name: 'Arial', size: 10, color: { argb: 'FF000000' } };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: backgroundColor } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });

        exportedRows++;
      } else if (row.type === 'filler' && row.content) {
        // صف فيلر
        const broadcastTime = formatTimeWithSeconds(row.time || '00:00:00');
        const duration = row.duration || '00:11:07';
        
        // حساب وقت النهاية
        const [hours, minutes, seconds] = broadcastTime.split(':').map(Number);
        const [durHours, durMinutes, durSeconds] = duration.split(':').map(Number);
        const totalSeconds = (hours * 3600 + minutes * 60 + seconds) + (durHours * 3600 + durMinutes * 60 + durSeconds);
        const endHours = Math.floor(totalSeconds / 3600) % 24;
        const endMinutes = Math.floor((totalSeconds % 3600) / 60);
        const endSecs = totalSeconds % 60;
        const endTime = `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}:${endSecs.toString().padStart(2, '0')}`;
        
        // تحديد النوع
        let type = 'Filler';
        if (row.content.includes('STING')) type = 'Sting';
        else if (row.content.includes('PROMO')) type = 'Promo';
        else if (row.content.includes('FILL_IN')) type = 'Fill IN';

        const dataRow = worksheet.addRow([
          row.segmentCode || `DPR${String(exportedRows + 1).padStart(5, '0')}`,  // ID CODE
          broadcastTime,      // وقت الإذاعة
          type,               // TYPE
          row.content,        // TITLE
          `2025-06-20 ${broadcastTime} ${row.content}`,  // DESCRIPTION
          'SERVER',           // رقم الهارد
          broadcastTime,      // IN
          endTime,            // OUT
          duration            // DURATION
        ]);

        // تنسيق الصف حسب النوع
        let backgroundColor = 'FFFFFFFF';
        
        switch (type) {
          case 'Promo':
            backgroundColor = 'FFFFE4B5'; // بيج للبرومو
            break;
          case 'Sting':
            backgroundColor = 'FFFF6B6B'; // أحمر فاتح للستينغ
            break;
          case 'Filler':
            backgroundColor = 'FFFFA500'; // برتقالي للفيلر
            break;
          case 'Fill IN':
            backgroundColor = 'FF87CEEB'; // أزرق فاتح للـ Fill IN
            break;
        }

        dataRow.eachCell((cell) => {
          cell.font = { name: 'Arial', size: 10, color: { argb: 'FF000000' } };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: backgroundColor } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });

        exportedRows++;
      }
    });

    console.log('📋 تم تحضير البيانات للتصدير');
    console.log('📊 عدد الصفوف المصدرة:', exportedRows);

    // تحديد عرض الأعمدة (معكوس من اليمين لليسار)
    worksheet.columns = [
      { width: 15 }, // ID CODE
      { width: 15 }, // وقت الإذاعة
      { width: 12 }, // TYPE
      { width: 25 }, // TITLE
      { width: 35 }, // DESCRIPTION
      { width: 12 }, // رقم الهارد
      { width: 10 }, // IN
      { width: 10 }, // OUT
      { width: 12 }  // DURATION
    ];

    // حفظ الملف
    await workbook.xlsx.writeFile('Daily_Schedule_2025-06-20_ExcelJS_RTL_Colored.xlsx');
    console.log('✅ تم إنشاء الملف بنجاح: Daily_Schedule_2025-06-20_ExcelJS_RTL_Colored.xlsx');

  } catch (error) {
    console.error('❌ خطأ في إنشاء الملف:', error);
  }
}

// تشغيل الدالة
createDailyScheduleExport();
