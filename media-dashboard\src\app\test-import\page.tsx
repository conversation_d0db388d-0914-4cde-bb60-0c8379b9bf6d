'use client';

import { useState } from 'react';

export default function TestImportPage() {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleImport = async () => {
    if (!file) {
      alert('يرجى اختيار ملف');
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      console.log('🚀 بدء رفع الملف:', file.name, 'حجم:', file.size);

      const formData = new FormData();
      formData.append('file', file);

      console.log('📤 إرسال الطلب إلى API...');

      const response = await fetch('/api/import-unified', {
        method: 'POST',
        body: formData,
        // إضافة timeout أطول للملفات الكبيرة
        signal: AbortSignal.timeout(300000) // 5 دقائق
      });

      console.log('📥 تم استلام الرد، الحالة:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ البيانات المستلمة:', data);
      setResult(data);

      if (data.success) {
        const summary = data.data?.summary || '';
        alert(`تم الاستيراد بنجاح!\n\n${data.message}\n\n${summary}`);
      } else {
        if (data.duplicates) {
          alert(`خطأ - أكواد مكررة:\n${data.message}`);
        } else {
          alert(`خطأ: ${data.error}`);
        }
      }
    } catch (error) {
      console.error('❌ خطأ في الاستيراد:', error);

      let errorMessage = 'حدث خطأ في الاستيراد';

      if (error instanceof Error) {
        if (error.name === 'TimeoutError') {
          errorMessage = 'انتهت مهلة الاستيراد. الملف قد يكون كبيراً جداً.';
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'فشل في الاتصال بالخادم. تأكد من أن الخادم يعمل.';
        } else {
          errorMessage = `خطأ: ${error.message}`;
        }
      }

      alert(errorMessage);
      setResult({ success: false, error: errorMessage });
    } finally {
      setLoading(false);
    }
  };

  const clearData = async () => {
    try {
      const response = await fetch('/api/clear-imported-data', {
        method: 'POST',
      });
      const data = await response.json();
      alert(`تم حذف ${data.deletedCount} مادة`);
    } catch (error) {
      alert('خطأ في الحذف');
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">🧪 اختبار استيراد Excel</h1>

        <div className="bg-blue-900 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-300 mb-2">📋 تعليمات مهمة</h3>
          <div className="text-blue-100 text-sm space-y-1">
            <p>🔴 <strong>أحمر</strong>: مواد مرفوضة رقابياً/منتهية → لن تظهر في الجداول</p>
            <p>⚫ <strong>أسود</strong>: مواد مرفوضة هندسياً → لن تظهر في الجداول</p>
            <p>🟢 <strong>أخضر</strong>: مواد صالحة → ستظهر في الجداول</p>
            <p>⚪ <strong>بدون لون</strong>: مواد صالحة افتراضياً → ستظهر في الجداول</p>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">📥 رفع ملف Excel</h2>
          
          <div className="space-y-4">
            <input
              type="file"
              accept=".xlsx,.xls"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-300 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700"
            />
            
            {file && (
              <div className="text-green-400">
                <p>✅ تم اختيار الملف: {file.name}</p>
                <p className="text-sm text-gray-400">حجم الملف: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
              </div>
            )}
            
            <div className="flex gap-4">
              <button
                onClick={handleImport}
                disabled={!file || loading}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 px-6 py-2 rounded-lg font-semibold"
              >
                {loading ? '⏳ جاري الاستيراد...' : '📥 استيراد'}
              </button>
              
              <button
                onClick={clearData}
                className="bg-red-600 hover:bg-red-700 px-6 py-2 rounded-lg font-semibold"
              >
                🗑️ حذف البيانات
              </button>
            </div>
          </div>
        </div>

        {result && (
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">
              {result.success ? '✅ نتيجة الاستيراد' : '❌ خطأ في الاستيراد'}
            </h2>

            {result.success ? (
              <div className="space-y-4">
                <div className="bg-green-900 p-4 rounded-lg">
                  <h3 className="font-semibold text-green-300 mb-2">📊 ملخص الاستيراد</h3>
                  <p className="text-green-100">{result.message}</p>
                  {result.data?.summary && (
                    <p className="text-green-200 mt-2">{result.data.summary}</p>
                  )}
                </div>

                {result.data?.statusBreakdown && (
                  <div className="bg-blue-900 p-4 rounded-lg">
                    <h3 className="font-semibold text-blue-300 mb-2">📈 إحصائيات الحالات</h3>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="text-green-300">✅ صالحة: {result.data.statusBreakdown.valid}</div>
                      <div className="text-red-300">🔴 مرفوضة رقابياً: {result.data.statusBreakdown.rejectedCensorship}</div>
                      <div className="text-gray-300">⚫ مرفوضة هندسياً: {result.data.statusBreakdown.rejectedTechnical}</div>
                      <div className="text-orange-300">⏰ منتهية الصلاحية: {result.data.statusBreakdown.expired}</div>
                      <div className="text-yellow-300">🟡 Hold: {result.data.statusBreakdown.hold}</div>
                    </div>
                  </div>
                )}

                {result.data?.txBreakdown && (
                  <div className="bg-purple-900 p-4 rounded-lg">
                    <h3 className="font-semibold text-purple-300 mb-2">📺 حالة TX (الظهور في الجداول)</h3>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div className="text-green-300">🟢 مفعلة: {result.data.txBreakdown.enabled}</div>
                      <div className="text-red-300">🔴 معطلة: {result.data.txBreakdown.disabled}</div>
                    </div>
                    <p className="text-purple-200 text-xs mt-2">
                      * المواد المعطلة لن تظهر في الخريطة البرامجية أو جدول الإذاعة
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="bg-red-900 p-4 rounded-lg">
                <p className="text-red-100">{result.error}</p>
                {result.message && (
                  <pre className="text-red-200 text-sm mt-2 whitespace-pre-wrap">{result.message}</pre>
                )}
              </div>
            )}

            <details className="mt-4">
              <summary className="cursor-pointer text-gray-400 hover:text-white">
                🔍 عرض البيانات الخام
              </summary>
              <pre className="bg-gray-900 p-4 rounded text-sm overflow-auto mt-2">
                {JSON.stringify(result, null, 2)}
              </pre>
            </details>
          </div>
        )}
        
        <div className="mt-8 text-center">
          <a 
            href="/media-list" 
            className="bg-blue-600 hover:bg-blue-700 px-6 py-2 rounded-lg font-semibold inline-block"
          >
            📋 عرض قائمة المواد
          </a>
        </div>
      </div>
    </div>
  );
}
