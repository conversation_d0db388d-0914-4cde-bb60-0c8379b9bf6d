'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/components/Toast';
import Logo from '@/components/Logo';
import { useTranslation } from 'react-i18next';

export default function LoginPage() {
  const router = useRouter();
  const { showToast, ToastContainer } = useToast();
  const { t, i18n } = useTranslation('common');

  // Get current language and direction
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';
  
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  
  const [isLoading, setIsLoading] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.username.trim() || !formData.password.trim()) {
      showToast(t('auth.pleaseLogin'), 'error');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        showToast(t('auth.welcomeBack'), 'success');
        
        // حفظ بيانات المستخدم في localStorage
        localStorage.setItem('user', JSON.stringify(result.user));
        localStorage.setItem('token', result.token);
        
        // توجيه جميع المستخدمين للوحة التحكم الجديدة
        setTimeout(() => {
          router.push('/dashboard');
        }, 1500);
      } else {
        showToast(t('auth.invalidCredentials'), 'error');
      }
    } catch (error) {
      console.error('Login error:', error);
      showToast(t('auth.loginError'), 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: '#1a1d29',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Cairo, Arial, sans-serif',
      direction: isRTL ? 'rtl' : 'ltr',
      padding: '20px'
    }}>
      <div style={{
        background: '#2d3748',
        borderRadius: '24px',
        padding: '60px 50px',
        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.3)',
        border: '1px solid #4a5568',
        width: '100%',
        maxWidth: '450px',
        textAlign: 'center'
      }}>
        {/* منطقة اللوجو */}
        <div style={{
          marginBottom: '40px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center'
        }}>
          {/* لوجو Prime X الاحترافي */}
          <div style={{
            marginBottom: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '20px'
          }}>
            <Logo
              size="large"
              style={{
                fontSize: '3.5rem',
                textShadow: '0 4px 8px rgba(0,0,0,0.3)',
                filter: 'drop-shadow(0 0 10px rgba(255, 215, 0, 0.3))'
              }}
            />
          </div>
          
          <h1 style={{
            fontSize: '2.2rem',
            fontWeight: 'bold',
            color: '#f3f4f6',
            margin: '0 0 10px 0'
          }}>
            {t('home.subtitle')}
          </h1>

          <p style={{
            color: '#a0aec0',
            fontSize: '1rem',
            margin: 0
          }}>
            {t('auth.pleaseLogin')}
          </p>
        </div>

        {/* نموذج تسجيل الدخول */}
        <form onSubmit={handleSubmit} style={{ width: '100%' }}>
          <div style={{ marginBottom: '25px' }}>
            <input
              type="text"
              placeholder={t('auth.username')}
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              style={{
                width: '100%',
                padding: '16px 20px',
                border: '2px solid #e9ecef',
                borderRadius: '12px',
                fontSize: '1rem',
                fontFamily: 'Cairo, Arial, sans-serif',
                direction: isRTL ? 'rtl' : 'ltr',
                outline: 'none',
                transition: 'all 0.3s ease',
                background: 'rgba(255, 255, 255, 0.8)',
                color: '#000',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#667eea';
                e.target.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e9ecef';
                e.target.style.boxShadow = 'none';
              }}
            />
          </div>

          <div style={{ marginBottom: '30px' }}>
            <input
              type="password"
              placeholder={t('auth.password')}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              style={{
                width: '100%',
                padding: '16px 20px',
                border: '2px solid #e9ecef',
                borderRadius: '12px',
                fontSize: '1rem',
                fontFamily: 'Cairo, Arial, sans-serif',
                direction: isRTL ? 'rtl' : 'ltr',
                outline: 'none',
                transition: 'all 0.3s ease',
                background: 'rgba(255, 255, 255, 0.8)',
                color: '#000',
                boxSizing: 'border-box'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = '#667eea';
                e.target.style.boxShadow = '0 0 0 3px rgba(102, 126, 234, 0.1)';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#e9ecef';
                e.target.style.boxShadow = 'none';
              }}
            />
          </div>

          <button
            type="submit"
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '16px',
              background: isLoading 
                ? 'linear-gradient(45deg, #adb5bd, #6c757d)' 
                : 'linear-gradient(45deg, #667eea, #764ba2)',
              color: 'white',
              border: 'none',
              borderRadius: '12px',
              fontSize: '1.1rem',
              fontWeight: 'bold',
              cursor: isLoading ? 'not-allowed' : 'pointer',
              transition: 'all 0.3s ease',
              boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3)',
              fontFamily: 'Cairo, Arial, sans-serif'
            }}
            onMouseEnter={(e) => {
              if (!isLoading) {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 12px 25px rgba(102, 126, 234, 0.4)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading) {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 20px rgba(102, 126, 234, 0.3)';
              }
            }}
          >
            {isLoading ? `⏳ ${t('common.loading')}` : `🔐 ${t('auth.loginButton')}`}
          </button>
        </form>

        {/* معلومات إضافية */}
        <div style={{
          marginTop: '30px',
          padding: '20px',
          background: 'rgba(102, 126, 234, 0.1)',
          borderRadius: '12px',
          border: '1px solid rgba(102, 126, 234, 0.2)'
        }}>
          <h3 style={{
            color: '#667eea',
            fontSize: '1rem',
            margin: '0 0 10px 0'
          }}>
            🔑 {t('auth.userRoles')}
          </h3>
          <div style={{
            fontSize: '0.85rem',
            color: '#6c757d',
            textAlign: isRTL ? 'right' : 'left'
          }}>
            <div>👑 <strong>{t('roles.ADMIN')}</strong>: {t('auth.adminDesc')}</div>
            <div>📝 <strong>{t('roles.CONTENT_MANAGER')}</strong>: {t('auth.contentManagerDesc')}</div>
            <div>📅 <strong>{t('roles.SCHEDULER')}</strong>: {t('auth.schedulerDesc')}</div>
            <div>👁️ <strong>{t('roles.VIEWER')}</strong>: {t('auth.viewerDesc')}</div>
          </div>
        </div>
      </div>

      <ToastContainer />

      {/* النص السفلي */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        color: '#6c757d',
        fontSize: '0.75rem',
        fontFamily: 'Arial, sans-serif',
        direction: 'ltr'
      }}>
        Powered By Mahmoud Ismail
      </div>
    </div>
  );
}
