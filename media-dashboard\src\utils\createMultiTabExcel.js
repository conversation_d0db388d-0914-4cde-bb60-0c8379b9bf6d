const ExcelJS = require('exceljs');
const fs = require('fs');
const path = require('path');

async function createMultiTabExcel() {
  const workbook = new ExcelJS.Workbook();
  
  // تاب الأفلام
  const moviesSheet = workbook.addWorksheet('الأفلام');
  moviesSheet.addRow(['Film ID', 'نوع المادة', 'اسم المادة', 'TC In', 'TC Out', 'Dur']);
  moviesSheet.addRow(['DDC000055-P1-3', 'MOVIE', 'الريحاني ( الجزء الأول)', '0:00:00', '0:14:42', '0:14:42']);
  moviesSheet.addRow(['DDC000055-P2-3', 'MOVIE', 'الريحاني ( الجزء الأول)', '0:00:00', '0:14:47', '0:14:47']);
  moviesSheet.addRow(['DDC000055-P3-3', 'MOVIE', 'الريحاني ( الجزء الأول)', '0:00:00', '0:12:23', '0:12:23']);
  moviesSheet.addRow(['DDC000072-P1-2', 'MOVIE', 'الريحاني / الجزء الثاني', '0:00:00', '0:17:02', '0:17:02']);
  moviesSheet.addRow(['DDC000072-P2-2', 'MOVIE', 'الريحاني / الجزء الثاني', '0:00:00', '0:16:58', '0:16:58']);
  
  // تاب البرامج
  const programsSheet = workbook.addWorksheet('البرامج');
  programsSheet.addRow(['Film ID', 'نوع المادة', 'اسم المادة', 'TC In', 'TC Out', 'Dur']);
  programsSheet.addRow(['PRG001-P1-2', 'PROGRAM', 'برنامج الصباح', '0:00:00', '0:25:30', '0:25:30']);
  programsSheet.addRow(['PRG001-P2-2', 'PROGRAM', 'برنامج الصباح', '0:00:00', '0:24:15', '0:24:15']);
  programsSheet.addRow(['PRG002-P1-3', 'PROGRAM', 'برنامج المساء', '0:00:00', '0:22:45', '0:22:45']);
  programsSheet.addRow(['PRG002-P2-3', 'PROGRAM', 'برنامج المساء', '0:00:00', '0:21:30', '0:21:30']);
  programsSheet.addRow(['PRG002-P3-3', 'PROGRAM', 'برنامج المساء', '0:00:00', '0:20:15', '0:20:15']);
  
  // تاب المسلسلات
  const seriesSheet = workbook.addWorksheet('المسلسلات');
  seriesSheet.addRow(['Film ID', 'نوع المادة', 'اسم المادة', 'TC In', 'TC Out', 'Dur']);
  seriesSheet.addRow(['SER001-P1-3', 'SERIES', 'مسلسل الدراما', '0:00:00', '0:22:45', '0:22:45']);
  seriesSheet.addRow(['SER001-P2-3', 'SERIES', 'مسلسل الدراما', '0:00:00', '0:21:30', '0:21:30']);
  seriesSheet.addRow(['SER001-P3-3', 'SERIES', 'مسلسل الدراما', '0:00:00', '0:20:15', '0:20:15']);
  seriesSheet.addRow(['SER002-P1-2', 'SERIES', 'مسلسل الكوميديا', '0:00:00', '0:28:30', '0:28:30']);
  seriesSheet.addRow(['SER002-P2-2', 'SERIES', 'مسلسل الكوميديا', '0:00:00', '0:27:15', '0:27:15']);
  
  // تاب الفيلر والبرومو
  const fillersSheet = workbook.addWorksheet('الفيلر والبرومو');
  fillersSheet.addRow(['Film ID', 'نوع المادة', 'اسم المادة', 'TC In', 'TC Out', 'Dur']);
  fillersSheet.addRow(['FILL001-P1-1', 'FILLER', 'فاصل إعلاني', '0:00:00', '0:02:30', '0:02:30']);
  fillersSheet.addRow(['PROMO001-P1-1', 'PROMO', 'إعلان القناة', '0:00:00', '0:01:15', '0:01:15']);
  fillersSheet.addRow(['STING001-P1-1', 'STING', 'فاصل أخبار', '0:00:00', '0:00:30', '0:00:30']);

  // تاب الأنواع الخاصة
  const specialSheet = workbook.addWorksheet('الأنواع الخاصة');
  specialSheet.addRow(['Film ID', 'نوع المادة', 'اسم المادة', 'TC In', 'TC Out', 'Dur']);
  specialSheet.addRow(['NEXT001-P1-1', 'NEXT', 'التالي', '0:00:00', '0:00:15', '0:00:15']);
  specialSheet.addRow(['NOW001-P1-1', 'NOW', 'الآن', '0:00:00', '0:00:10', '0:00:10']);
  specialSheet.addRow(['BACK001-P1-1', 'سنعود', 'سنعود', '0:00:00', '0:00:20', '0:00:20']);
  specialSheet.addRow(['RETURN001-P1-1', 'عدنا', 'عدنا', '0:00:00', '0:00:15', '0:00:15']);
  
  // تنسيق العناوين لجميع التابات
  [moviesSheet, programsSheet, seriesSheet, fillersSheet, specialSheet].forEach(sheet => {
    const headerRow = sheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '366092' }
    };
    
    // تنسيق عرض الأعمدة
    sheet.columns.forEach(column => {
      column.width = 20;
    });
  });
  
  // حفظ الملف
  const filePath = path.join(__dirname, '../../public/media-import-multi-tabs.xlsx');
  await workbook.xlsx.writeFile(filePath);
  console.log('✅ تم إنشاء ملف Excel متعدد التابات:', filePath);
}

createMultiTabExcel().catch(console.error);
