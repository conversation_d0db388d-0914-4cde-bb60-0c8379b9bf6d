"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/daily-schedule/page",{

/***/ "(app-pages-browser)/./src/app/daily-schedule/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/daily-schedule/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _daily_schedule_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./daily-schedule.css */ \"(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DailySchedulePage() {\n    _s();\n    const { user, isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [gridRows, setGridRows] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('ALL');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [showWeeklySchedule, setShowWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // تهيئة التاريخ الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            setSelectedDate(today.toISOString().split('T')[0]);\n        }\n    }[\"DailySchedulePage.useEffect\"], []);\n    // جلب البيانات عند تغيير التاريخ\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (selectedDate) {\n                fetchScheduleData();\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        selectedDate\n    ]);\n    // جلب بيانات الجدول الإذاعي\n    const fetchScheduleData = async ()=>{\n        setLoading(true);\n        try {\n            console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);\n            console.log('🌐 URL المطلوب:', \"/api/daily-schedule?date=\".concat(selectedDate));\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة:', data);\n            if (data.success) {\n                var _data_data_scheduleItems, _data_data_availableMedia, _data_data_scheduleRows, _data_data_availableMedia1, _data_data_availableMedia2;\n                console.log('✅ استجابة ناجحة من API:', {\n                    scheduleItemsCount: ((_data_data_scheduleItems = data.data.scheduleItems) === null || _data_data_scheduleItems === void 0 ? void 0 : _data_data_scheduleItems.length) || 0,\n                    availableMediaCount: ((_data_data_availableMedia = data.data.availableMedia) === null || _data_data_availableMedia === void 0 ? void 0 : _data_data_availableMedia.length) || 0,\n                    scheduleRowsCount: ((_data_data_scheduleRows = data.data.scheduleRows) === null || _data_data_scheduleRows === void 0 ? void 0 : _data_data_scheduleRows.length) || 0,\n                    date: data.data.date,\n                    dayOfWeek: data.data.dayOfWeek\n                });\n                setScheduleItems(data.data.scheduleItems);\n                setAvailableMedia(data.data.availableMedia || []);\n                console.log(\"\\uD83D\\uDCDA تم تحديد \".concat(((_data_data_availableMedia1 = data.data.availableMedia) === null || _data_data_availableMedia1 === void 0 ? void 0 : _data_data_availableMedia1.length) || 0, \" مادة متاحة في القائمة الجانبية\"));\n                // التحقق من وجود صفوف في الجدول\n                if (data.data.scheduleRows && data.data.scheduleRows.length > 0) {\n                    // تطبيق الحماية على المواد الأساسية المحفوظة\n                    const protectedRows = data.data.scheduleRows.map((row)=>{\n                        if (row.type === 'segment' && !row.isTemporary) {\n                            return {\n                                ...row,\n                                canDelete: false\n                            }; // حماية المواد الأساسية\n                        }\n                        return row;\n                    });\n                    setGridRows(protectedRows);\n                    console.log('📝 تم تحميل', protectedRows.length, 'صف من الجدول المحفوظ مع تطبيق الحماية');\n                } else {\n                    // إذا لم تكن هناك صفوف، نحاول بناء الجدول من الخريطة البرامجية\n                    console.log('⚠️ لم يتم العثور على جدول محفوظ، سيتم محاولة بناء الجدول من الخريطة البرامجية');\n                    await buildScheduleFromWeekly();\n                }\n                if (data.fromSavedFile) {\n                    console.log('📂 تم تحميل جدول محفوظ مسبقاً');\n                    console.log('💾 تاريخ الحفظ:', data.savedAt);\n                    console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);\n                } else {\n                    console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');\n                    console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');\n                }\n                console.log('📦 المواد المتاحة:', ((_data_data_availableMedia2 = data.data.availableMedia) === null || _data_data_availableMedia2 === void 0 ? void 0 : _data_data_availableMedia2.length) || 0);\n                // عرض عينة من المواد المتاحة\n                if (data.data.availableMedia && data.data.availableMedia.length > 0) {\n                    console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));\n                } else {\n                    console.log('⚠️ لا توجد مواد متاحة في القائمة الجانبية');\n                }\n            } else {\n                console.log('❌ فشل في جلب البيانات:', data.error || 'خطأ غير محدد');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n            // جلب الجدول الأسبوعي للمراجعة\n            await fetchWeeklySchedule();\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        } finally{\n            setLoading(false);\n            console.log('✅ انتهى تحميل البيانات');\n        }\n    };\n    // البحث عن الاستنجات المطابقة لمادة معينة\n    const findMatchingStings = (mediaName, stingType)=>{\n        return mediaItems.filter((item)=>item.name === mediaName && item.type === stingType && item.status === 'VALID' && item.tx === true);\n    };\n    // إضافة استنجات تلقائية بين السيجمنت\n    const addAutomaticStings = (scheduleRows, mediaName)=>{\n        console.log(\"\\uD83D\\uDD0D البحث عن استنجات للمادة: \".concat(mediaName));\n        // البحث عن استنجات \"سنعود\" و \"عدنا\" لهذه المادة\n        const snawodStings = findMatchingStings(mediaName, 'سنعود');\n        const odnaStings = findMatchingStings(mediaName, 'عدنا');\n        console.log(\"\\uD83D\\uDCE6 وجدت \".concat(snawodStings.length, ' استنج \"سنعود\" و ').concat(odnaStings.length, ' استنج \"عدنا\" للمادة: ').concat(mediaName));\n        if (snawodStings.length === 0 && odnaStings.length === 0) {\n            console.log(\"⚠️ لا توجد استنجات للمادة: \".concat(mediaName));\n            return scheduleRows; // لا توجد استنجات متطابقة\n        }\n        // البحث عن المادة الأساسية في الجدول\n        const mediaRows = scheduleRows.map((row, index)=>({\n                ...row,\n                originalIndex: index\n            })).filter((row)=>row.type === 'segment' && row.content && row.content.includes(mediaName) && !row.isAutoGenerated // تجنب المواد المولدة تلقائياً\n        );\n        console.log(\"\\uD83C\\uDFAC وجدت \".concat(mediaRows.length, \" صف للمادة: \").concat(mediaName));\n        if (mediaRows.length <= 1) {\n            console.log(\"⚠️ المادة \".concat(mediaName, \" لها سيجمنت واحد فقط - لا حاجة للاستنجات\"));\n            return scheduleRows; // لا حاجة للاستنجات إذا كان سيجمنت واحد فقط\n        }\n        const newRows = [\n            ...scheduleRows\n        ];\n        const insertions = [];\n        // إضافة استنجات بين كل سيجمنت والتالي\n        for(let i = 0; i < mediaRows.length - 1; i++){\n            const currentRow = mediaRows[i];\n            // إضافة \"سنعود\" بعد السيجمنت الحالي\n            if (snawodStings.length > 0) {\n                const snawodSting = snawodStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex,\n                    sting: {\n                        id: \"auto_snawod_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(snawodSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(snawodSting),\n                        mediaType: 'سنعود',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n            // إضافة \"عدنا\" قبل السيجمنت التالي (بعد \"سنعود\")\n            if (odnaStings.length > 0) {\n                const odnaSting = odnaStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex + (snawodStings.length > 0 ? 1 : 0),\n                    sting: {\n                        id: \"auto_odna_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(odnaSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(odnaSting),\n                        mediaType: 'عدنا',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n        }\n        // إدراج الاستنجات من النهاية للبداية لتجنب تغيير الفهارس\n        insertions.sort((a, b)=>b.afterIndex - a.afterIndex);\n        insertions.forEach((insertion)=>{\n            newRows.splice(insertion.afterIndex + 1, 0, insertion.sting);\n        });\n        console.log(\"✨ تم إضافة \".concat(insertions.length, \" استنج تلقائي للمادة: \").concat(mediaName));\n        return newRows;\n    };\n    // بناء الجدول من الخريطة البرامجية\n    const buildScheduleFromWeekly = async ()=>{\n        try {\n            console.log('🔄 محاولة بناء الجدول من الخريطة البرامجية للتاريخ:', selectedDate);\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            console.log('📅 تفاصيل التاريخ:');\n            console.log('  - التاريخ الأصلي:', selectedDate);\n            console.log('  - التاريخ المحول:', date.toISOString().split('T')[0]);\n            console.log('  - يوم الأسبوع:', date.getDay(), '(0=أحد, 1=اثنين, 2=ثلاثاء, 3=أربعاء, 4=خميس, 5=جمعة, 6=سبت)');\n            console.log('  - بداية الأسبوع:', weekStartStr);\n            console.log('  - نهاية الأسبوع:', new Date(new Date(weekStartStr).getTime() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);\n            console.log('🌐 طلب البيانات من:', \"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status);\n                return;\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة من weekly-schedule:', data);\n            if (data.success && data.data && data.data.scheduleItems) {\n                const dayOfWeek = date.getDay();\n                console.log('📅 اليوم المطلوب:', dayOfWeek);\n                console.log('📦 إجمالي المواد في الخريطة:', data.data.scheduleItems.length);\n                // عرض جميع المواد للتشخيص\n                console.log('🔍 جميع المواد في الخريطة:');\n                data.data.scheduleItems.forEach((item, index)=>{\n                    var _item_mediaItem;\n                    console.log(\"  \".concat(index + 1, \". \").concat(((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime, \" - إعادة: \").concat(item.isRerun));\n                });\n                // فلترة المواد الخاصة بهذا اليوم\n                const dayItems = data.data.scheduleItems.filter((item)=>{\n                    const matches = item.dayOfWeek === dayOfWeek && item.mediaItem;\n                    if (matches) {\n                        var _item_mediaItem;\n                        console.log(\"✅ مادة متطابقة: \".concat((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime));\n                    } else {\n                        var _item_mediaItem1;\n                        console.log(\"❌ مادة غير متطابقة: \".concat(((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" (مطلوب: \").concat(dayOfWeek, \") - mediaItem: \").concat(!!item.mediaItem));\n                    }\n                    return matches;\n                });\n                console.log('📋 عدد المواد المطابقة لهذا اليوم:', dayItems.length);\n                if (dayItems.length > 0) {\n                    // ترتيب المواد حسب وقت البداية\n                    dayItems.sort((a, b)=>a.startTime.localeCompare(b.startTime));\n                    // بناء صفوف الجدول\n                    const newRows = [];\n                    let currentTime = '08:00:00';\n                    // إضافة صف فارغ في البداية\n                    newRows.push({\n                        id: \"empty_start_\".concat(Date.now()),\n                        type: 'empty',\n                        time: currentTime,\n                        canDelete: true\n                    });\n                    // إضافة المواد\n                    for (const item of dayItems){\n                        // إضافة المادة\n                        const mediaItem = item.mediaItem;\n                        const itemName = mediaItem.name;\n                        const itemType = mediaItem.type;\n                        const itemId = mediaItem.id;\n                        // إضافة تفاصيل المادة\n                        const details = [];\n                        if (mediaItem.episodeNumber) details.push(\"ح\".concat(mediaItem.episodeNumber));\n                        if (mediaItem.seasonNumber && mediaItem.seasonNumber > 0) details.push(\"م\".concat(mediaItem.seasonNumber));\n                        if (mediaItem.partNumber) details.push(\"ج\".concat(mediaItem.partNumber));\n                        const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n                        // تحديد نوع المادة\n                        let rowType = 'segment';\n                        let itemContent = \"\".concat(itemName).concat(detailsText);\n                        if ([\n                            'PROMO',\n                            'STING',\n                            'FILLER',\n                            'FILL_IN'\n                        ].includes(itemType)) {\n                            rowType = 'filler';\n                            itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                        }\n                        // حساب المدة\n                        let itemDuration = '00:01:00';\n                        if (mediaItem.duration) {\n                            itemDuration = mediaItem.duration;\n                        } else if (mediaItem.segments && mediaItem.segments.length > 0) {\n                            let totalSeconds = 0;\n                            mediaItem.segments.forEach((segment)=>{\n                                if (segment.duration) {\n                                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                                }\n                            });\n                            if (totalSeconds > 0) {\n                                const hours = Math.floor(totalSeconds / 3600);\n                                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                                const secs = totalSeconds % 60;\n                                itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                            }\n                        }\n                        // إضافة الصف\n                        const newRow = {\n                            id: \"\".concat(rowType, \"_\").concat(Date.now(), \"_\").concat(itemId),\n                            type: rowType,\n                            time: item.startTime,\n                            content: itemContent,\n                            mediaItemId: itemId,\n                            duration: itemDuration,\n                            canDelete: false,\n                            isRerun: item.isRerun,\n                            isTemporary: false,\n                            originalStartTime: item.startTime\n                        };\n                        console.log('🔒 إضافة مادة أساسية محمية:', newRow.content, 'canDelete:', newRow.canDelete);\n                        newRows.push(newRow);\n                        // تحديث الوقت الحالي\n                        currentTime = calculateNextTime(item.startTime, itemDuration);\n                    }\n                    // إضافة الاستنجات التلقائية لكل مادة\n                    let finalRows = [\n                        ...newRows\n                    ];\n                    const processedMediaNames = new Set();\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem;\n                        const mediaName = (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name;\n                        if (mediaName && !processedMediaNames.has(mediaName)) {\n                            processedMediaNames.add(mediaName);\n                            finalRows = addAutomaticStings(finalRows, mediaName);\n                        }\n                    });\n                    // إضافة صفوف فارغة في النهاية\n                    for(let i = 0; i < 8; i++){\n                        finalRows.push({\n                            id: \"empty_end_\".concat(Date.now(), \"_\").concat(i),\n                            type: 'empty',\n                            canDelete: true\n                        });\n                    }\n                    // تحديث الجدول\n                    setGridRows(finalRows);\n                    console.log('✅ تم بناء الجدول من الخريطة البرامجية:', finalRows.length, 'صف');\n                    // إعادة حساب الأوقات\n                    recalculateTimes(finalRows);\n                } else {\n                    console.log('⚠️ لا توجد مواد لهذا اليوم في الخريطة البرامجية');\n                    console.log('💡 تحقق من:');\n                    console.log('  - وجود مواد في الخريطة البرامجية لهذا اليوم');\n                    console.log('  - صحة يوم الأسبوع المحسوب');\n                    console.log('  - وجود mediaItem في كل مادة');\n                }\n            } else {\n                console.log('❌ فشل في جلب بيانات الخريطة البرامجية');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في بناء الجدول من الخريطة البرامجية:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        }\n    };\n    // جلب الجدول الأسبوعي للمراجعة\n    const fetchWeeklySchedule = async ()=>{\n        try {\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const data = await response.json();\n            console.log('📊 استجابة API للجدول الأسبوعي:', data);\n            if (data.success && data.data) {\n                // استخراج مواد اليوم المحدد من scheduleItems\n                const dayOfWeek = new Date(selectedDate).getDay();\n                const daySchedule = [];\n                console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);\n                console.log('📦 البيانات المتاحة:', data.data);\n                // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)\n                if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {\n                    console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);\n                    const dayItems = data.data.scheduleItems.filter((item)=>{\n                        var _item_mediaItem;\n                        console.log('🔍 فحص المادة:', (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);\n                        return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء\n                    }).sort((a, b)=>{\n                        // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59\n                        const timeA = a.startTime;\n                        const timeB = b.startTime;\n                        const getTimeOrder = (time)=>{\n                            const hour = parseInt(time.split(':')[0]);\n                            if (hour >= 8 && hour < 18) return 1; // صباح ومساء\n                            if (hour >= 18) return 2; // برايم تايم\n                            return 3; // منتصف الليل والفجر\n                        };\n                        const orderA = getTimeOrder(timeA);\n                        const orderB = getTimeOrder(timeB);\n                        if (orderA !== orderB) return orderA - orderB;\n                        return timeA.localeCompare(timeB);\n                    });\n                    console.log('✅ مواد اليوم المفلترة:', dayItems.length);\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3;\n                        const scheduleItem = {\n                            time: item.startTime,\n                            name: ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'مادة غير محددة',\n                            episodeNumber: item.episodeNumber || ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.episodeNumber),\n                            partNumber: item.partNumber || ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.partNumber),\n                            seasonNumber: item.seasonNumber || ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.seasonNumber),\n                            isRerun: item.isRerun || false\n                        };\n                        daySchedule.push(scheduleItem);\n                        console.log('📺 إضافة مادة للخريطة:', scheduleItem);\n                    });\n                } else {\n                    console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');\n                }\n                setWeeklySchedule(daySchedule);\n                console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');\n            } else {\n                console.log('❌ فشل في جلب البيانات أو البيانات فارغة');\n                setWeeklySchedule([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);\n            setWeeklySchedule([]);\n        }\n    };\n    // فلترة المواد المتاحة\n    const filteredMedia = availableMedia.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesType = filterType === 'ALL' || item.type === filterType;\n        return matchesSearch && matchesType;\n    });\n    // أنواع المواد للفلترة\n    const mediaTypes = [\n        'ALL',\n        'PROGRAM',\n        'SERIES',\n        'FILM',\n        'SONG',\n        'PROMO',\n        'STING',\n        'FILLER',\n        'NEXT',\n        'NOW',\n        'سنعود',\n        'عدنا',\n        'MINI',\n        'CROSS'\n    ];\n    // دالة ترجمة أنواع المواد\n    const getTypeLabel = (type)=>{\n        const typeLabels = {\n            'ALL': t('schedule.allTypes'),\n            'PROGRAM': t('schedule.types.program'),\n            'SERIES': t('schedule.types.series'),\n            'FILM': t('schedule.types.film'),\n            'MOVIE': t('schedule.types.film'),\n            'SONG': t('schedule.types.song'),\n            'STING': t('schedule.types.sting'),\n            'FILL_IN': t('schedule.types.fillIn'),\n            'FILLER': t('schedule.types.filler'),\n            'PROMO': t('schedule.types.promo'),\n            'NEXT': t('schedule.types.next'),\n            'NOW': t('schedule.types.now'),\n            'سنعود': t('mediaTypes.سنعود'),\n            'عدنا': t('mediaTypes.عدنا'),\n            'MINI': t('mediaTypes.MINI'),\n            'CROSS': t('mediaTypes.CROSS')\n        };\n        return typeLabels[type] || type;\n    };\n    // إضافة صف فارغ\n    const addEmptyRow = (afterIndex)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        const newRow = {\n            id: \"empty_\".concat(Date.now()),\n            type: 'empty',\n            canDelete: true\n        };\n        newRows.splice(afterIndex + 1, 0, newRow);\n        setGridRows(newRows);\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // إضافة صفوف فارغة متعددة\n    const addMultipleEmptyRows = function(afterIndex) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 8;\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        for(let i = 0; i < count; i++){\n            const newRow = {\n                id: \"empty_\".concat(Date.now(), \"_\").concat(i),\n                type: 'empty',\n                canDelete: true\n            };\n            newRows.splice(afterIndex + 1 + i, 0, newRow);\n        }\n        setGridRows(newRows);\n        console.log(\"✅ تم إضافة \".concat(count, \" صف فارغ\"));\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // التحقق من الحاجة لإضافة صفوف فارغة\n    const checkAndAddEmptyRows = (currentIndex)=>{\n        const currentRows = gridRows;\n        const nextEmptyIndex = currentRows.findIndex((row, index)=>index > currentIndex && row.type === 'empty');\n        // إذا لم توجد صفوف فارغة بعد الفاصل الحالي\n        if (nextEmptyIndex === -1) {\n            console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 8 صفوف');\n            addMultipleEmptyRows(currentIndex, 8);\n        } else {\n            console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);\n        }\n    };\n    // حذف صف فارغ\n    const deleteRow = (rowId)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        // استعادة موضع التمرير بعد الحذف\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 100);\n    };\n    // حذف سيجمنت مع حماية المواد الأساسية\n    const deleteSegment = (rowId)=>{\n        const row = gridRows.find((r)=>r.id === rowId);\n        // حماية مضاعفة للمواد الأساسية\n        if (row && row.type === 'segment' && (!row.isTemporary || !row.canDelete)) {\n            alert('⚠️ لا يمكن حذف المواد الأساسية من هنا!\\n\\nهذه المادة مستوردة من الخريطة البرامجية.\\n\\nلتغيير هذه المادة:\\n1. اذهب للخريطة البرامجية\\n2. قم بتعديل المادة هناك\\n3. ستتحدث تلقائياً في الجدول اليومي');\n            return;\n        }\n        // التأكد من أن المادة قابلة للحذف\n        if (row && !row.canDelete) {\n            alert('⚠️ هذه المادة محمية من الحذف!');\n            return;\n        }\n        if (confirm(t('schedule.confirmDeleteSegment'))) {\n            const newRows = gridRows.filter((row)=>row.id !== rowId);\n            recalculateTimes(newRows);\n            console.log('🗑️ تم حذف السيجمنت');\n        }\n    };\n    // حذف فاصل بدون تأكيد\n    const deleteFiller = (rowId)=>{\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        console.log('🗑️ تم حذف الفاصل');\n    };\n    // تحريك صف لأعلى\n    const moveRowUp = (index)=>{\n        if (index <= 0) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index - 1], newRows[index]] = [\n            newRows[index],\n            newRows[index - 1]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬆️ تم تحريك الصف لأعلى');\n    };\n    // تحريك صف لأسفل\n    const moveRowDown = (index)=>{\n        if (index >= gridRows.length - 1) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index], newRows[index + 1]] = [\n            newRows[index + 1],\n            newRows[index]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬇️ تم تحريك الصف لأسفل');\n    };\n    // معالجة سحب الصفوف داخل الجدول\n    const handleRowDragStart = (e, index)=>{\n        e.dataTransfer.setData('text/plain', index.toString());\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    // معالجة إسقاط الصفوف داخل الجدول\n    const handleRowDrop = (e, targetIndex)=>{\n        e.preventDefault();\n        const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));\n        if (sourceIndex === targetIndex) return;\n        const newRows = [\n            ...gridRows\n        ];\n        const [movedRow] = newRows.splice(sourceIndex, 1);\n        newRows.splice(targetIndex, 0, movedRow);\n        recalculateTimes(newRows);\n        console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);\n        // تثبيت الجدول بعد السحب\n        setTimeout(()=>{\n            const gridElement = document.querySelector('.schedule-grid');\n            if (gridElement) {\n                gridElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'start'\n                });\n            }\n        }, 100);\n    };\n    // معالجة إسقاط المواد\n    const handleDrop = (e, rowIndex)=>{\n        e.preventDefault();\n        // حفظ موضع التمرير الحالي قبل أي تغيير\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const rowElement = e.currentTarget.closest('.grid-row');\n        const rowOffsetTop = (rowElement === null || rowElement === void 0 ? void 0 : rowElement.getBoundingClientRect().top) || 0;\n        try {\n            var _mediaData_segments, _mediaData_segments1;\n            // محاولة الحصول على البيانات بطرق مختلفة\n            let mediaData;\n            const jsonData = e.dataTransfer.getData('application/json');\n            const textData = e.dataTransfer.getData('text/plain');\n            console.log('📥 البيانات المسحوبة:', {\n                jsonData,\n                textData\n            });\n            // التحقق من أن هذا ليس سحب صف داخلي\n            if (textData && !jsonData && /^\\d+$/.test(textData)) {\n                console.log('🔄 هذا سحب صف داخلي، تجاهل');\n                return;\n            }\n            if (jsonData) {\n                mediaData = JSON.parse(jsonData);\n            } else if (textData) {\n                try {\n                    mediaData = JSON.parse(textData);\n                } catch (e) {\n                    console.error('❌ لا يمكن تحليل البيانات المسحوبة');\n                    return;\n                }\n            } else {\n                console.error('❌ لا توجد بيانات مسحوبة');\n                return;\n            }\n            // التحقق من أن الصف فارغ\n            const targetRow = gridRows[rowIndex];\n            if (targetRow.type !== 'empty') {\n                alert('يمكن إسقاط المواد في الصفوف الفارغة فقط');\n                return;\n            }\n            console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);\n            // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر\n            if (!mediaData || typeof mediaData !== 'object' || typeof mediaData === 'string' || Array.isArray(mediaData)) {\n                console.error('❌ بيانات المادة غير صحيحة:', mediaData);\n                console.error('❌ نوع البيانات:', typeof mediaData);\n                console.error('❌ هل هو مصفوفة؟', Array.isArray(mediaData));\n                return;\n            }\n            // التأكد من وجود الاسم\n            const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';\n            const itemType = mediaData.type || 'UNKNOWN';\n            const itemId = mediaData.id || Date.now().toString();\n            console.log('📥 معلومات المادة:', {\n                name: itemName,\n                type: itemType,\n                id: itemId,\n                segments: ((_mediaData_segments = mediaData.segments) === null || _mediaData_segments === void 0 ? void 0 : _mediaData_segments.length) || 0\n            });\n            // تحديد نوع المادة المسحوبة\n            let dragItemType = 'filler';\n            let itemContent = itemName;\n            // إضافة تفاصيل المادة\n            const details = [];\n            if (mediaData.episodeNumber) details.push(\"ح\".concat(mediaData.episodeNumber));\n            if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(\"م\".concat(mediaData.seasonNumber));\n            if (mediaData.partNumber) details.push(\"ج\".concat(mediaData.partNumber));\n            const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n            // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات\n            if ([\n                'PROMO',\n                'STING',\n                'FILLER',\n                'FILL_IN'\n            ].includes(itemType)) {\n                dragItemType = 'filler';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                // تخزين نوع المادة لاستخدامه في تمييز الألوان\n                mediaData.mediaType = itemType;\n            } else {\n                dragItemType = 'segment';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" (مادة إضافية)\");\n            }\n            // حساب المدة الحقيقية للمادة\n            let itemDuration = '00:01:00'; // مدة افتراضية\n            console.log('🔍 تحليل مدة المادة:', {\n                name: itemName,\n                hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),\n                segmentsCount: ((_mediaData_segments1 = mediaData.segments) === null || _mediaData_segments1 === void 0 ? void 0 : _mediaData_segments1.length) || 0,\n                hasDuration: !!mediaData.duration,\n                directDuration: mediaData.duration\n            });\n            if (mediaData.segments && mediaData.segments.length > 0) {\n                // حساب إجمالي مدة جميع السيجمنتات\n                let totalSeconds = 0;\n                mediaData.segments.forEach((segment, index)=>{\n                    if (segment.duration) {\n                        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                        const segmentSeconds = hours * 3600 + minutes * 60 + seconds;\n                        totalSeconds += segmentSeconds;\n                        console.log(\"  \\uD83D\\uDCFA سيجمنت \".concat(index + 1, \": \").concat(segment.duration, \" (\").concat(segmentSeconds, \" ثانية)\"));\n                    }\n                });\n                if (totalSeconds > 0) {\n                    const hours = Math.floor(totalSeconds / 3600);\n                    const minutes = Math.floor(totalSeconds % 3600 / 60);\n                    const secs = totalSeconds % 60;\n                    itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                }\n                console.log('📊 حساب مدة المادة من السيجمنتات:', {\n                    name: itemName,\n                    segments: mediaData.segments.length,\n                    totalSeconds,\n                    finalDuration: itemDuration\n                });\n            } else if (mediaData.duration) {\n                // استخدام المدة المباشرة إذا كانت موجودة\n                itemDuration = mediaData.duration;\n                console.log('📊 استخدام مدة مباشرة:', itemDuration);\n            } else {\n                console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);\n            }\n            // إنشاء كود للمادة المسحوبة\n            let itemCode = '';\n            if (mediaData.segmentCode) {\n                itemCode = mediaData.segmentCode;\n            } else if (mediaData.id) {\n                itemCode = \"\".concat(itemType, \"_\").concat(mediaData.id);\n            } else {\n                itemCode = \"\".concat(itemType, \"_\").concat(Date.now().toString().slice(-6));\n            }\n            // إنشاء صف جديد مع المدة الحقيقية\n            const newRow = {\n                id: \"dropped_\".concat(Date.now()),\n                type: dragItemType,\n                content: itemContent,\n                mediaItemId: itemId,\n                segmentCode: itemCode,\n                duration: itemDuration,\n                canDelete: true\n            };\n            // إضافة نوع المادة للفواصل لتمييزها بألوان مختلفة\n            if (dragItemType === 'filler' && [\n                'PROMO',\n                'STING',\n                'FILL_IN',\n                'FILLER'\n            ].includes(itemType)) {\n                newRow.mediaType = itemType;\n                console.log(\"\\uD83C\\uDFA8 تم تعيين نوع المادة: \".concat(itemType, \" للصف الجديد\"));\n            }\n            // التأكد من أن الصف المستهدف فارغ\n            if (gridRows[rowIndex].type !== 'empty') {\n                console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);\n                return;\n            }\n            // استبدال الصف الفارغ مباشرة\n            const newRows = [\n                ...gridRows\n            ];\n            newRows[rowIndex] = newRow;\n            console.log('✅ تم إضافة المادة:', {\n                name: itemName,\n                type: dragItemType,\n                duration: itemDuration,\n                position: rowIndex,\n                content: itemContent,\n                beforeType: gridRows[rowIndex].type,\n                afterType: newRow.type\n            });\n            // تحديث الصفوف مباشرة\n            setGridRows(newRows);\n            // إعادة حساب الأوقات بعد تأخير قصير\n            setTimeout(()=>{\n                recalculateTimes(newRows);\n                // التحقق من الحاجة لإضافة صفوف فارغة\n                checkAndAddEmptyRows(rowIndex);\n                // تثبيت الجدول بعد إسقاط المادة\n                setTimeout(()=>{\n                    const gridElement = document.querySelector('.schedule-grid');\n                    if (gridElement) {\n                        gridElement.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'start'\n                        });\n                        console.log('📍 تم تثبيت الجدول بعد إسقاط المادة');\n                    }\n                }, 100);\n            }, 50);\n        } catch (error) {\n            console.error('❌ خطأ في إسقاط المادة:', error);\n            // في حالة حدوث خطأ، نستعيد موضع التمرير على أي حال\n            setTimeout(()=>{\n                if (gridBody) {\n                    gridBody.scrollTop = currentScrollTop;\n                }\n            }, 100);\n        }\n    };\n    // إعادة حساب الأوقات مثل Excel\n    const recalculateTimes = (rows)=>{\n        const newRows = [\n            ...rows\n        ];\n        let currentTime = '08:00:00'; // نقطة البداية بالثواني\n        let hasFillers = false; // هل تم إضافة فواصل؟\n        // التحقق من وجود فواصل\n        hasFillers = rows.some((row)=>row.type === 'filler');\n        console.log('🔄 بدء إعادة حساب الأوقات من 08:00:00', hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');\n        for(let i = 0; i < newRows.length; i++){\n            const row = newRows[i];\n            if (row.type === 'segment' || row.type === 'filler') {\n                // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل\n                if (i === 0 || hasFillers) {\n                    newRows[i] = {\n                        ...row,\n                        time: currentTime\n                    };\n                } else {\n                    newRows[i] = {\n                        ...row,\n                        time: undefined\n                    };\n                }\n                if (row.duration) {\n                    // حساب الوقت التالي بناءً على المدة\n                    const nextTime = calculateNextTime(currentTime, row.duration);\n                    console.log(\"⏰ \".concat(row.type, ': \"').concat(row.content, '\" - من ').concat(currentTime, \" إلى \").concat(nextTime, \" (مدة: \").concat(row.duration, \")\"));\n                    currentTime = nextTime;\n                }\n            } else if (row.type === 'empty') {\n                // الصفوف الفارغة لا تؤثر على الوقت\n                newRows[i] = {\n                    ...row,\n                    time: undefined\n                };\n            }\n            // التحقق من الوصول لوقت مادة أساسية\n            if (row.originalStartTime && hasFillers) {\n                const targetMinutes = timeToMinutes(row.originalStartTime);\n                const currentMinutes = timeToMinutes(currentTime);\n                const difference = targetMinutes - currentMinutes;\n                if (Math.abs(difference) > 5) {\n                    console.log('⚠️ انحراف زمني: المادة \"'.concat(row.content, '\" مجدولة في ').concat(row.originalStartTime, \" لكن ستدخل في \").concat(currentTime, \" (فرق: \").concat(difference, \" دقيقة)\"));\n                } else {\n                    console.log('✅ توقيت صحيح: المادة \"'.concat(row.content, '\" ستدخل في ').concat(currentTime, \" (مجدولة: \").concat(row.originalStartTime, \")\"));\n                }\n            }\n        }\n        console.log(\"\\uD83C\\uDFC1 انتهاء الحساب - الوقت النهائي: \".concat(currentTime));\n        // حفظ موضع التمرير قبل التحديث\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        // تحديث الصفوف دائماً لضمان التحديث الصحيح\n        setGridRows(newRows);\n        // استعادة موضع التمرير بعد التحديث\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // تحويل الوقت إلى دقائق\n    const timeToMinutes = (time)=>{\n        const [hours, minutes] = time.split(':').map(Number);\n        return hours * 60 + minutes;\n    };\n    // حساب المدة الإجمالية للمادة بدقة\n    const calculateTotalDuration = (item)=>{\n        if (item.segments && item.segments.length > 0) {\n            let totalSeconds = 0;\n            item.segments.forEach((segment)=>{\n                if (segment.duration) {\n                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                }\n            });\n            if (totalSeconds > 0) {\n                const hours = Math.floor(totalSeconds / 3600);\n                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                const secs = totalSeconds % 60;\n                return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n            }\n        }\n        return item.duration || '00:01:00';\n    };\n    // حفظ تعديلات الجدول\n    const saveScheduleChanges = async ()=>{\n        try {\n            console.log('💾 بدء حفظ التعديلات...');\n            console.log('📅 التاريخ:', selectedDate);\n            console.log('📝 عدد الصفوف:', gridRows.length);\n            const response = await fetch('/api/daily-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    date: selectedDate,\n                    scheduleRows: gridRows\n                })\n            });\n            const result = await response.json();\n            if (response.ok && result.success) {\n                console.log('✅ تم حفظ تعديلات الجدول الإذاعي بنجاح');\n                alert('✅ تم حفظ التعديلات بنجاح!');\n            } else {\n                console.error('❌ فشل في حفظ التعديلات:', result.error);\n                alert('❌ فشل في حفظ التعديلات: ' + result.error);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ التعديلات:', error);\n            alert('❌ خطأ في الاتصال بالخادم');\n        }\n    };\n    // تصدير الجدول الإذاعي إلى Excel\n    const exportDailySchedule = async ()=>{\n        try {\n            console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');\n            if (!selectedDate) {\n                alert('يرجى تحديد التاريخ أولاً');\n                return;\n            }\n            // حفظ البيانات الحالية أولاً\n            console.log('💾 حفظ البيانات الحالية...');\n            await saveScheduleChanges();\n            // إرسال البيانات الحالية مع طلب التصدير (تجاهل المواد المؤقتة)\n            const currentData = {\n                date: selectedDate,\n                scheduleRows: gridRows.filter((row)=>(row.type === 'segment' || row.type === 'filler' && row.content) && !row.isTemporary)\n            };\n            const response = await fetch(\"/api/export-daily-schedule-new?date=\".concat(selectedDate), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(currentData)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"Daily_Schedule_\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('✅ تم تصدير الجدول الإذاعي بنجاح');\n            alert('✅ تم تصدير الجدول بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في تصدير الجدول:', error);\n            alert('❌ فشل في تصدير الجدول: ' + error.message);\n        }\n    };\n    // حساب الوقت التالي بناءً على المدة (بدقة الثواني)\n    const calculateNextTime = (startTime, duration)=>{\n        // تحليل وقت البداية\n        const startParts = startTime.split(':');\n        const startHours = parseInt(startParts[0]);\n        const startMins = parseInt(startParts[1]);\n        const startSecs = parseInt(startParts[2] || '0');\n        // تحليل المدة\n        const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n        // حساب إجمالي الثواني\n        let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;\n        totalSeconds += durHours * 3600 + durMins * 60 + durSecs;\n        // تحويل إلى ساعات ودقائق وثواني\n        const hours = Math.floor(totalSeconds / 3600) % 24;\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            title: t('schedule.daily'),\n            subtitle: t('schedule.scheduledMedia'),\n            icon: \"\\uD83D\\uDCCA\",\n            fullWidth: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"1b542d50d800f28e\",\n                    children: \".grid-row.empty.jsx-1b542d50d800f28e{background:#b8dce8!important;background-color:#b8dce8!important;min-height:50px!important;height:50px!important;border-color:#a0c4d4!important}.grid-row.empty.jsx-1b542d50d800f28e .action-btn.jsx-1b542d50d800f28e{font-size:.75rem!important;padding:5px 8px!important;min-width:30px!important;height:30px!important;line-height:1.2!important;-webkit-border-radius:4px!important;-moz-border-radius:4px!important;border-radius:4px!important;margin:2px!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important;overflow:hidden!important;white-space:nowrap!important}.grid-row.empty.jsx-1b542d50d800f28e .actions-cell.jsx-1b542d50d800f28e{padding:8px!important;gap:5px!important;display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important;-webkit-box-align:center!important;-webkit-align-items:center!important;-moz-box-align:center!important;-ms-flex-align:center!important;align-items:center!important;-webkit-box-pack:center!important;-webkit-justify-content:center!important;-moz-box-pack:center!important;-ms-flex-pack:center!important;justify-content:center!important;height:50px!important;max-height:50px!important;overflow:hidden!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important}\"\n                }, void 0, false, void 0, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#b8dce8',\n                        minHeight: '100vh',\n                        margin: '-2rem',\n                        padding: '2rem'\n                    },\n                    className: \"jsx-1b542d50d800f28e\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-controls\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"date-selector\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"schedule-date\",\n                                            className: \"jsx-1b542d50d800f28e\",\n                                            children: [\n                                                t('common.selectDate'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"schedule-date\",\n                                            type: \"date\",\n                                            value: selectedDate,\n                                            onChange: (e)=>setSelectedDate(e.target.value),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1182,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"header-buttons\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: saveScheduleChanges,\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button primary\",\n                                            children: [\n                                                \"\\uD83D\\uDCBE \",\n                                                t('common.save')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1194,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: exportDailySchedule,\n                                            style: {\n                                                background: 'linear-gradient(45deg, #17a2b8, #138496)',\n                                                color: 'white'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button export\",\n                                            children: [\n                                                \"\\uD83D\\uDCCA \",\n                                                t('reports.exportExcel')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1201,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.href = '/daily-schedule/import',\n                                            style: {\n                                                background: 'linear-gradient(45deg, #8e44ad, #9b59b6)',\n                                                color: 'white'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button\",\n                                            children: \"\\uD83D\\uDCE5 Import List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1212,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowWeeklySchedule(!showWeeklySchedule),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button\",\n                                            children: showWeeklySchedule ? \"\\uD83D\\uDCCB \".concat(t('schedule.hideSchedule')) : \"\\uD83D\\uDCC5 \".concat(t('schedule.showSchedule'))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1223,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 1181,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-content\",\n                            children: [\n                                showWeeklySchedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: t('schedule.weeklySchedule')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1238,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-schedule-list\",\n                                            children: Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? weeklySchedule.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-time\",\n                                                            children: item.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1243,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-name\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ح\",\n                                                                        item.episodeNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ج\",\n                                                                        item.partNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1244,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-status\",\n                                                            children: item.isRerun ? '🔄' : '🎯'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1242,\n                                                    columnNumber: 19\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"no-data\",\n                                                children: t('schedule.noWeeklyData')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1237,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: \"المواد المتاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1267,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-controls\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filterType,\n                                                    onChange: (e)=>setFilterType(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"filter-select\",\n                                                    children: mediaTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type,\n                                                            className: \"jsx-1b542d50d800f28e\",\n                                                            children: getTypeLabel(type)\n                                                        }, type, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1277,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1271,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث في المواد...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"search-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1283,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-header\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                            children: \"الاسم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1296,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                            children: \"المدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                            children: \"الكود\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1299,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1295,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-body\",\n                                                    children: filteredMedia.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: (e)=>{\n                                                                e.dataTransfer.setData('application/json', JSON.stringify(item));\n                                                                // إضافة class للتصغير أثناء السحب\n                                                                e.currentTarget.classList.add('dragging');\n                                                            },\n                                                            onDragEnd: (e)=>{\n                                                                // إزالة class بعد انتهاء السحب\n                                                                e.currentTarget.classList.remove('dragging');\n                                                            },\n                                                            \"data-type\": item.type,\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-row \".concat(item.type.toLowerCase()),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    title: \"\".concat(item.name, \" - \").concat(getTypeLabel(item.type)),\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-name-text\",\n                                                                            children: item.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1321,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-tags\",\n                                                                            children: [\n                                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"episode-tag\",\n                                                                                    children: [\n                                                                                        \"ح\",\n                                                                                        item.episodeNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1323,\n                                                                                    columnNumber: 46\n                                                                                }, this),\n                                                                                item.seasonNumber && item.seasonNumber > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"season-tag\",\n                                                                                    children: [\n                                                                                        \"م\",\n                                                                                        item.seasonNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1324,\n                                                                                    columnNumber: 70\n                                                                                }, this),\n                                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"part-tag\",\n                                                                                    children: [\n                                                                                        \"ج\",\n                                                                                        item.partNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1325,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1322,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1320,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                                    children: getTypeLabel(item.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1328,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                                    children: calculateTotalDuration(item)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1329,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                                    children: item.segmentCode || item.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1330,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, item.id, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1305,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1303,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1293,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-grid\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-header\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-column\",\n                                                    children: \"الكود\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1340,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-column\",\n                                                    children: \"الوقت\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1341,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-column\",\n                                                    children: \"المحتوى\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1342,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-column\",\n                                                    children: \"المدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1343,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-column\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1344,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-column\",\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1345,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1339,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-body\",\n                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"loading\",\n                                                children: t('common.loading')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 1350,\n                                                columnNumber: 15\n                                            }, this) : gridRows.map((row, index)=>{\n                                                var _row_content, _row_content1, _row_content2, _row_content3;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    draggable: row.type === 'filler' || row.type === 'empty' || row.type === 'segment' && row.canDelete,\n                                                    onDragStart: (e)=>handleRowDragStart(e, index),\n                                                    onDrop: (e)=>{\n                                                        handleRowDrop(e, index);\n                                                        // إزالة تأثير drag-over\n                                                        e.currentTarget.classList.remove('drag-over');\n                                                    },\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        // إضافة تأثير بصري عند السحب فوق الصف الفارغ\n                                                        if (row.type === 'empty') {\n                                                            e.currentTarget.classList.add('drag-over');\n                                                        }\n                                                    },\n                                                    onDragLeave: (e)=>{\n                                                        // إزالة تأثير drag-over عند مغادرة الصف\n                                                        e.currentTarget.classList.remove('drag-over');\n                                                    },\n                                                    \"data-type\": // استخدام الحقل الإضافي mediaType إن وجد\n                                                    row.mediaType ? row.mediaType : ((_row_content = row.content) === null || _row_content === void 0 ? void 0 : _row_content.includes('PROMO')) ? 'PROMO' : ((_row_content1 = row.content) === null || _row_content1 === void 0 ? void 0 : _row_content1.includes('STING')) ? 'STING' : ((_row_content2 = row.content) === null || _row_content2 === void 0 ? void 0 : _row_content2.includes('FILL_IN')) ? 'FILL_IN' : ((_row_content3 = row.content) === null || _row_content3 === void 0 ? void 0 : _row_content3.includes('FILLER')) ? 'FILLER' : '',\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-row \".concat(row.type, \" \").concat(row.type === 'segment' ? row.isRerun ? 'rerun-content' : row.isTemporary ? 'temp-content' : 'primary-content' : row.type === 'filler' ? row.mediaType === 'PROMO' ? 'promo-content' : row.mediaType === 'STING' ? 'sting-content' : row.mediaType === 'FILLER' ? 'filler-content' : row.mediaType === 'FILL_IN' ? 'filler-content' : 'break-content' : 'primary-content'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-cell\",\n                                                            children: row.type === 'segment' || row.type === 'filler' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-code\",\n                                                                children: row.segmentCode || \"\".concat(row.type.toUpperCase(), \"_\").concat(row.id.slice(-6))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1399,\n                                                                columnNumber: 23\n                                                            }, this) : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1397,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-cell\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-time\",\n                                                                children: row.time || ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onDrop: (e)=>handleDrop(e, index),\n                                                            onDragOver: (e)=>e.preventDefault(),\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-cell\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-text\",\n                                                                children: row.content || ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1409,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-cell\",\n                                                            children: row.duration || ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1418,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-cell\",\n                                                            children: [\n                                                                row.type === 'segment' && row.isTemporary && '🟣 مؤقت',\n                                                                row.type === 'segment' && !row.isRerun && !row.isTemporary && (row.originalStartTime ? Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ? '⚠️ انحراف' : '✅ دقيق' : '🎯 أساسي'),\n                                                                row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة',\n                                                                row.type === 'filler' && '📺 فاصل',\n                                                                row.type === 'empty' && '⚪ فارغ'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-cell\",\n                                                            children: [\n                                                                row.type === 'empty' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"إضافة صف\",\n                                                                            onClick: ()=>addEmptyRow(index),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-row\",\n                                                                            children: \"➕\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1436,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"إضافة 8 صفوف\",\n                                                                            onClick: ()=>addMultipleEmptyRows(index, 8),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-multiple-rows\",\n                                                                            children: \"➕➕\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1443,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        row.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"حذف صف\",\n                                                                            onClick: ()=>deleteRow(row.id),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                            children: \"➖\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                row.type === 'filler' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"تحريك لأعلى\",\n                                                                            onClick: ()=>moveRowUp(index),\n                                                                            disabled: index === 0,\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-up\",\n                                                                            children: \"⬆️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1463,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"تحريك لأسفل\",\n                                                                            onClick: ()=>moveRowDown(index),\n                                                                            disabled: index === gridRows.length - 1,\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-down\",\n                                                                            children: \"⬇️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1471,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"حذف فاصل\",\n                                                                            onClick: ()=>{\n                                                                                // تحويل الفاصل إلى صف فارغ\n                                                                                const newRows = [\n                                                                                    ...gridRows\n                                                                                ];\n                                                                                newRows[index] = {\n                                                                                    id: \"empty_\".concat(Date.now()),\n                                                                                    type: 'empty',\n                                                                                    canDelete: true\n                                                                                };\n                                                                                // إعادة حساب الأوقات\n                                                                                recalculateTimes(newRows);\n                                                                            },\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                            children: \"\\uD83D\\uDDD1️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1479,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                row.type === 'segment' && row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    title: \"استبدال بمادة حقيقية\",\n                                                                    onClick: ()=>{\n                                                                        alert('💡 لاستبدال المادة المؤقتة:\\n\\n1. أضف المادة الحقيقية لقاعدة البيانات\\n2. احذف المادة المؤقتة\\n3. اسحب المادة الجديدة من القائمة الجانبية');\n                                                                    },\n                                                                    style: {\n                                                                        color: '#9c27b0'\n                                                                    },\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn replace-temp\",\n                                                                    children: \"\\uD83D\\uDD04\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1500,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                row.type === 'segment' && row.canDelete && !row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    title: \"حذف سيجمنت\",\n                                                                    onClick: ()=>deleteSegment(row.id),\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                    children: \"❌\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1512,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1433,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, row.id, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1353,\n                                                    columnNumber: 17\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1338,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 1178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n            lineNumber: 1145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n        lineNumber: 1144,\n        columnNumber: 5\n    }, this);\n}\n_s(DailySchedulePage, \"OrBF0na36HW7NPf2T+tiAqx8I7A=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = DailySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"DailySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/page.tsx\n"));

/***/ })

});