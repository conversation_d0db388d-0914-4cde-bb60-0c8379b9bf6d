@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: #1a1d29;
  color: white;
  font-family: 'Cairo', Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
  direction: rtl;
}

/* منع الوميض عند التحميل */
.page-transition {
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Mobile Responsive Design - Global */
@media (max-width: 768px) {
  body {
    font-size: 14px;
  }

  /* Dashboard Layout Mobile */
  .dashboard-layout {
    padding: 10px !important;
  }

  /* Cards Mobile */
  .card, .dashboard-card {
    margin-bottom: 15px !important;
    padding: 15px !important;
    border-radius: 10px !important;
  }

  /* Grid layouts */
  .grid-container {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  /* Tables */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  table {
    min-width: 600px;
    font-size: 0.8rem;
  }

  th, td {
    padding: 8px 4px !important;
    white-space: nowrap;
  }

  /* Buttons */
  .btn, button {
    padding: 10px 15px !important;
    font-size: 0.9rem !important;
    min-height: 44px; /* Touch target size */
  }

  /* Forms */
  .form-group {
    margin-bottom: 15px;
  }

  input, select, textarea {
    width: 100% !important;
    padding: 12px !important;
    font-size: 16px !important; /* Prevent zoom on iOS */
    border-radius: 8px !important;
  }

  /* Navigation */
  .nav-item {
    padding: 12px !important;
    font-size: 0.9rem !important;
  }

  /* Sidebar adjustments */
  .sidebar {
    width: 100% !important;
    max-width: none !important;
  }

  /* Modal adjustments */
  .modal {
    margin: 10px !important;
    max-width: calc(100vw - 20px) !important;
  }

  /* Text adjustments */
  h1 { font-size: 1.5rem !important; }
  h2 { font-size: 1.3rem !important; }
  h3 { font-size: 1.1rem !important; }
  h4 { font-size: 1rem !important; }

  /* Flex containers */
  .flex-container {
    flex-direction: column !important;
    gap: 10px !important;
  }

  /* Hide non-essential elements on mobile */
  .desktop-only {
    display: none !important;
  }

  .mobile-only {
    display: block !important;
  }
}

@media (max-width: 480px) {
  body {
    font-size: 13px;
  }

  .dashboard-layout {
    padding: 5px !important;
  }

  .card, .dashboard-card {
    padding: 10px !important;
    margin-bottom: 10px !important;
  }

  table {
    font-size: 0.7rem;
  }

  th, td {
    padding: 6px 2px !important;
  }

  .btn, button {
    padding: 8px 12px !important;
    font-size: 0.8rem !important;
  }

  h1 { font-size: 1.2rem !important; }
  h2 { font-size: 1.1rem !important; }
  h3 { font-size: 1rem !important; }
  h4 { font-size: 0.9rem !important; }
}

/* Desktop only elements */
@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }

  .desktop-only {
    display: block !important;
  }
}
