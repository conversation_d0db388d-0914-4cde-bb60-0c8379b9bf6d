/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/traverse";
exports.ids = ["vendor-chunks/traverse"];
exports.modules = {

/***/ "(ssr)/./node_modules/traverse/index.js":
/*!****************************************!*\
  !*** ./node_modules/traverse/index.js ***!
  \****************************************/
/***/ ((module) => {

eval("module.exports = Traverse;\nfunction Traverse (obj) {\n    if (!(this instanceof Traverse)) return new Traverse(obj);\n    this.value = obj;\n}\n\nTraverse.prototype.get = function (ps) {\n    var node = this.value;\n    for (var i = 0; i < ps.length; i ++) {\n        var key = ps[i];\n        if (!Object.hasOwnProperty.call(node, key)) {\n            node = undefined;\n            break;\n        }\n        node = node[key];\n    }\n    return node;\n};\n\nTraverse.prototype.set = function (ps, value) {\n    var node = this.value;\n    for (var i = 0; i < ps.length - 1; i ++) {\n        var key = ps[i];\n        if (!Object.hasOwnProperty.call(node, key)) node[key] = {};\n        node = node[key];\n    }\n    node[ps[i]] = value;\n    return value;\n};\n\nTraverse.prototype.map = function (cb) {\n    return walk(this.value, cb, true);\n};\n\nTraverse.prototype.forEach = function (cb) {\n    this.value = walk(this.value, cb, false);\n    return this.value;\n};\n\nTraverse.prototype.reduce = function (cb, init) {\n    var skip = arguments.length === 1;\n    var acc = skip ? this.value : init;\n    this.forEach(function (x) {\n        if (!this.isRoot || !skip) {\n            acc = cb.call(this, acc, x);\n        }\n    });\n    return acc;\n};\n\nTraverse.prototype.deepEqual = function (obj) {\n    if (arguments.length !== 1) {\n        throw new Error(\n            'deepEqual requires exactly one object to compare against'\n        );\n    }\n    \n    var equal = true;\n    var node = obj;\n    \n    this.forEach(function (y) {\n        var notEqual = (function () {\n            equal = false;\n            //this.stop();\n            return undefined;\n        }).bind(this);\n        \n        //if (node === undefined || node === null) return notEqual();\n        \n        if (!this.isRoot) {\n        /*\n            if (!Object.hasOwnProperty.call(node, this.key)) {\n                return notEqual();\n            }\n        */\n            if (typeof node !== 'object') return notEqual();\n            node = node[this.key];\n        }\n        \n        var x = node;\n        \n        this.post(function () {\n            node = x;\n        });\n        \n        var toS = function (o) {\n            return Object.prototype.toString.call(o);\n        };\n        \n        if (this.circular) {\n            if (Traverse(obj).get(this.circular.path) !== x) notEqual();\n        }\n        else if (typeof x !== typeof y) {\n            notEqual();\n        }\n        else if (x === null || y === null || x === undefined || y === undefined) {\n            if (x !== y) notEqual();\n        }\n        else if (x.__proto__ !== y.__proto__) {\n            notEqual();\n        }\n        else if (x === y) {\n            // nop\n        }\n        else if (typeof x === 'function') {\n            if (x instanceof RegExp) {\n                // both regexps on account of the __proto__ check\n                if (x.toString() != y.toString()) notEqual();\n            }\n            else if (x !== y) notEqual();\n        }\n        else if (typeof x === 'object') {\n            if (toS(y) === '[object Arguments]'\n            || toS(x) === '[object Arguments]') {\n                if (toS(x) !== toS(y)) {\n                    notEqual();\n                }\n            }\n            else if (x instanceof Date || y instanceof Date) {\n                if (!(x instanceof Date) || !(y instanceof Date)\n                || x.getTime() !== y.getTime()) {\n                    notEqual();\n                }\n            }\n            else {\n                var kx = Object.keys(x);\n                var ky = Object.keys(y);\n                if (kx.length !== ky.length) return notEqual();\n                for (var i = 0; i < kx.length; i++) {\n                    var k = kx[i];\n                    if (!Object.hasOwnProperty.call(y, k)) {\n                        notEqual();\n                    }\n                }\n            }\n        }\n    });\n    \n    return equal;\n};\n\nTraverse.prototype.paths = function () {\n    var acc = [];\n    this.forEach(function (x) {\n        acc.push(this.path); \n    });\n    return acc;\n};\n\nTraverse.prototype.nodes = function () {\n    var acc = [];\n    this.forEach(function (x) {\n        acc.push(this.node);\n    });\n    return acc;\n};\n\nTraverse.prototype.clone = function () {\n    var parents = [], nodes = [];\n    \n    return (function clone (src) {\n        for (var i = 0; i < parents.length; i++) {\n            if (parents[i] === src) {\n                return nodes[i];\n            }\n        }\n        \n        if (typeof src === 'object' && src !== null) {\n            var dst = copy(src);\n            \n            parents.push(src);\n            nodes.push(dst);\n            \n            Object.keys(src).forEach(function (key) {\n                dst[key] = clone(src[key]);\n            });\n            \n            parents.pop();\n            nodes.pop();\n            return dst;\n        }\n        else {\n            return src;\n        }\n    })(this.value);\n};\n\nfunction walk (root, cb, immutable) {\n    var path = [];\n    var parents = [];\n    var alive = true;\n    \n    return (function walker (node_) {\n        var node = immutable ? copy(node_) : node_;\n        var modifiers = {};\n        \n        var state = {\n            node : node,\n            node_ : node_,\n            path : [].concat(path),\n            parent : parents.slice(-1)[0],\n            key : path.slice(-1)[0],\n            isRoot : path.length === 0,\n            level : path.length,\n            circular : null,\n            update : function (x) {\n                if (!state.isRoot) {\n                    state.parent.node[state.key] = x;\n                }\n                state.node = x;\n            },\n            'delete' : function () {\n                delete state.parent.node[state.key];\n            },\n            remove : function () {\n                if (Array.isArray(state.parent.node)) {\n                    state.parent.node.splice(state.key, 1);\n                }\n                else {\n                    delete state.parent.node[state.key];\n                }\n            },\n            before : function (f) { modifiers.before = f },\n            after : function (f) { modifiers.after = f },\n            pre : function (f) { modifiers.pre = f },\n            post : function (f) { modifiers.post = f },\n            stop : function () { alive = false }\n        };\n        \n        if (!alive) return state;\n        \n        if (typeof node === 'object' && node !== null) {\n            state.isLeaf = Object.keys(node).length == 0;\n            \n            for (var i = 0; i < parents.length; i++) {\n                if (parents[i].node_ === node_) {\n                    state.circular = parents[i];\n                    break;\n                }\n            }\n        }\n        else {\n            state.isLeaf = true;\n        }\n        \n        state.notLeaf = !state.isLeaf;\n        state.notRoot = !state.isRoot;\n        \n        // use return values to update if defined\n        var ret = cb.call(state, state.node);\n        if (ret !== undefined && state.update) state.update(ret);\n        if (modifiers.before) modifiers.before.call(state, state.node);\n        \n        if (typeof state.node == 'object'\n        && state.node !== null && !state.circular) {\n            parents.push(state);\n            \n            var keys = Object.keys(state.node);\n            keys.forEach(function (key, i) {\n                path.push(key);\n                \n                if (modifiers.pre) modifiers.pre.call(state, state.node[key], key);\n                \n                var child = walker(state.node[key]);\n                if (immutable && Object.hasOwnProperty.call(state.node, key)) {\n                    state.node[key] = child.node;\n                }\n                \n                child.isLast = i == keys.length - 1;\n                child.isFirst = i == 0;\n                \n                if (modifiers.post) modifiers.post.call(state, child);\n                \n                path.pop();\n            });\n            parents.pop();\n        }\n        \n        if (modifiers.after) modifiers.after.call(state, state.node);\n        \n        return state;\n    })(root).node;\n}\n\nObject.keys(Traverse.prototype).forEach(function (key) {\n    Traverse[key] = function (obj) {\n        var args = [].slice.call(arguments, 1);\n        var t = Traverse(obj);\n        return t[key].apply(t, args);\n    };\n});\n\nfunction copy (src) {\n    if (typeof src === 'object' && src !== null) {\n        var dst;\n        \n        if (Array.isArray(src)) {\n            dst = [];\n        }\n        else if (src instanceof Date) {\n            dst = new Date(src);\n        }\n        else if (src instanceof Boolean) {\n            dst = new Boolean(src);\n        }\n        else if (src instanceof Number) {\n            dst = new Number(src);\n        }\n        else if (src instanceof String) {\n            dst = new String(src);\n        }\n        else {\n            dst = Object.create(Object.getPrototypeOf(src));\n        }\n        \n        Object.keys(src).forEach(function (key) {\n            dst[key] = src[key];\n        });\n        return dst;\n    }\n    else return src;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/traverse/index.js\n");

/***/ })

};
;