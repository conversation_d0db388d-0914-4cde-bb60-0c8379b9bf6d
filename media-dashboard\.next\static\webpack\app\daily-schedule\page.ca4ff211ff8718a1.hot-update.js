"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/daily-schedule/page",{

/***/ "(app-pages-browser)/./src/app/daily-schedule/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/daily-schedule/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DailySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _daily_schedule_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./daily-schedule.css */ \"(app-pages-browser)/./src/app/daily-schedule/daily-schedule.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DailySchedulePage() {\n    _s();\n    const { user, isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [gridRows, setGridRows] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('ALL');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [weeklySchedule, setWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [showWeeklySchedule, setShowWeeklySchedule] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // تهيئة التاريخ الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            const today = new Date();\n            setSelectedDate(today.toISOString().split('T')[0]);\n        }\n    }[\"DailySchedulePage.useEffect\"], []);\n    // جلب البيانات عند تغيير التاريخ\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DailySchedulePage.useEffect\": ()=>{\n            if (selectedDate) {\n                fetchScheduleData();\n            }\n        }\n    }[\"DailySchedulePage.useEffect\"], [\n        selectedDate\n    ]);\n    // جلب بيانات الجدول الإذاعي\n    const fetchScheduleData = async ()=>{\n        setLoading(true);\n        try {\n            console.log('🔄 جلب بيانات الجدول اليومي للتاريخ:', selectedDate);\n            console.log('🌐 URL المطلوب:', \"/api/daily-schedule?date=\".concat(selectedDate));\n            const response = await fetch(\"/api/daily-schedule?date=\".concat(selectedDate));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة:', data);\n            if (data.success) {\n                var _data_data_scheduleItems, _data_data_availableMedia, _data_data_scheduleRows, _data_data_availableMedia1, _data_data_availableMedia2;\n                console.log('✅ استجابة ناجحة من API:', {\n                    scheduleItemsCount: ((_data_data_scheduleItems = data.data.scheduleItems) === null || _data_data_scheduleItems === void 0 ? void 0 : _data_data_scheduleItems.length) || 0,\n                    availableMediaCount: ((_data_data_availableMedia = data.data.availableMedia) === null || _data_data_availableMedia === void 0 ? void 0 : _data_data_availableMedia.length) || 0,\n                    scheduleRowsCount: ((_data_data_scheduleRows = data.data.scheduleRows) === null || _data_data_scheduleRows === void 0 ? void 0 : _data_data_scheduleRows.length) || 0,\n                    date: data.data.date,\n                    dayOfWeek: data.data.dayOfWeek\n                });\n                setScheduleItems(data.data.scheduleItems);\n                setAvailableMedia(data.data.availableMedia || []);\n                console.log(\"\\uD83D\\uDCDA تم تحديد \".concat(((_data_data_availableMedia1 = data.data.availableMedia) === null || _data_data_availableMedia1 === void 0 ? void 0 : _data_data_availableMedia1.length) || 0, \" مادة متاحة في القائمة الجانبية\"));\n                // التحقق من وجود صفوف في الجدول\n                if (data.data.scheduleRows && data.data.scheduleRows.length > 0) {\n                    // تطبيق الحماية على المواد الأساسية المحفوظة\n                    const protectedRows = data.data.scheduleRows.map((row)=>{\n                        if (row.type === 'segment' && !row.isTemporary) {\n                            return {\n                                ...row,\n                                canDelete: false\n                            }; // حماية المواد الأساسية\n                        }\n                        return row;\n                    });\n                    setGridRows(protectedRows);\n                    console.log('📝 تم تحميل', protectedRows.length, 'صف من الجدول المحفوظ مع تطبيق الحماية');\n                } else {\n                    // إذا لم تكن هناك صفوف، نحاول بناء الجدول من الخريطة البرامجية\n                    console.log('⚠️ لم يتم العثور على جدول محفوظ، سيتم محاولة بناء الجدول من الخريطة البرامجية');\n                    await buildScheduleFromWeekly();\n                }\n                if (data.fromSavedFile) {\n                    console.log('📂 تم تحميل جدول محفوظ مسبقاً');\n                    console.log('💾 تاريخ الحفظ:', data.savedAt);\n                    console.log('📝 عدد الصفوف المحفوظة:', data.data.scheduleRows.length);\n                } else {\n                    console.log('✅ تم جلب', data.data.scheduleItems.length, 'مادة للجدول الإذاعي');\n                    console.log('📝 تم بناء', data.data.scheduleRows.length, 'صف في الجدول');\n                }\n                console.log('📦 المواد المتاحة:', ((_data_data_availableMedia2 = data.data.availableMedia) === null || _data_data_availableMedia2 === void 0 ? void 0 : _data_data_availableMedia2.length) || 0);\n                // عرض عينة من المواد المتاحة\n                if (data.data.availableMedia && data.data.availableMedia.length > 0) {\n                    console.log('📋 عينة من المواد:', data.data.availableMedia.slice(0, 3));\n                } else {\n                    console.log('⚠️ لا توجد مواد متاحة في القائمة الجانبية');\n                }\n            } else {\n                console.log('❌ فشل في جلب البيانات:', data.error || 'خطأ غير محدد');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n            // جلب الجدول الأسبوعي للمراجعة\n            await fetchWeeklySchedule();\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        } finally{\n            setLoading(false);\n            console.log('✅ انتهى تحميل البيانات');\n        }\n    };\n    // البحث عن الاستنجات المطابقة لمادة معينة\n    const findMatchingStings = (mediaName, stingType)=>{\n        return mediaItems.filter((item)=>item.name === mediaName && item.type === stingType && item.status === 'VALID' && item.tx === true);\n    };\n    // إضافة استنجات تلقائية بين السيجمنت\n    const addAutomaticStings = (scheduleRows, mediaName)=>{\n        console.log(\"\\uD83D\\uDD0D البحث عن استنجات للمادة: \".concat(mediaName));\n        // البحث عن استنجات \"سنعود\" و \"عدنا\" لهذه المادة\n        const snawodStings = findMatchingStings(mediaName, 'سنعود');\n        const odnaStings = findMatchingStings(mediaName, 'عدنا');\n        console.log(\"\\uD83D\\uDCE6 وجدت \".concat(snawodStings.length, ' استنج \"سنعود\" و ').concat(odnaStings.length, ' استنج \"عدنا\" للمادة: ').concat(mediaName));\n        if (snawodStings.length === 0 && odnaStings.length === 0) {\n            console.log(\"⚠️ لا توجد استنجات للمادة: \".concat(mediaName));\n            return scheduleRows; // لا توجد استنجات متطابقة\n        }\n        // البحث عن المادة الأساسية في الجدول\n        const mediaRows = scheduleRows.map((row, index)=>({\n                ...row,\n                originalIndex: index\n            })).filter((row)=>row.type === 'segment' && row.content && row.content.includes(mediaName) && !row.isAutoGenerated // تجنب المواد المولدة تلقائياً\n        );\n        console.log(\"\\uD83C\\uDFAC وجدت \".concat(mediaRows.length, \" صف للمادة: \").concat(mediaName));\n        if (mediaRows.length <= 1) {\n            console.log(\"⚠️ المادة \".concat(mediaName, \" لها سيجمنت واحد فقط - لا حاجة للاستنجات\"));\n            return scheduleRows; // لا حاجة للاستنجات إذا كان سيجمنت واحد فقط\n        }\n        const newRows = [\n            ...scheduleRows\n        ];\n        const insertions = [];\n        // إضافة استنجات بين كل سيجمنت والتالي\n        for(let i = 0; i < mediaRows.length - 1; i++){\n            const currentRow = mediaRows[i];\n            // إضافة \"سنعود\" بعد السيجمنت الحالي\n            if (snawodStings.length > 0) {\n                const snawodSting = snawodStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex,\n                    sting: {\n                        id: \"auto_snawod_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(snawodSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(snawodSting),\n                        mediaType: 'سنعود',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n            // إضافة \"عدنا\" قبل السيجمنت التالي (بعد \"سنعود\")\n            if (odnaStings.length > 0) {\n                const odnaSting = odnaStings[0];\n                insertions.push({\n                    afterIndex: currentRow.originalIndex + (snawodStings.length > 0 ? 1 : 0),\n                    sting: {\n                        id: \"auto_odna_\".concat(currentRow.originalIndex, \"_\").concat(Date.now(), \"_\").concat(Math.random()),\n                        type: 'filler',\n                        content: \"\".concat(odnaSting.name, \" (تلقائي)\"),\n                        duration: calculateTotalDuration(odnaSting),\n                        mediaType: 'عدنا',\n                        canDelete: true,\n                        isAutoGenerated: true\n                    }\n                });\n            }\n        }\n        // إدراج الاستنجات من النهاية للبداية لتجنب تغيير الفهارس\n        insertions.sort((a, b)=>b.afterIndex - a.afterIndex);\n        insertions.forEach((insertion)=>{\n            newRows.splice(insertion.afterIndex + 1, 0, insertion.sting);\n        });\n        console.log(\"✨ تم إضافة \".concat(insertions.length, \" استنج تلقائي للمادة: \").concat(mediaName));\n        return newRows;\n    };\n    // بناء الجدول من الخريطة البرامجية\n    const buildScheduleFromWeekly = async ()=>{\n        try {\n            console.log('🔄 محاولة بناء الجدول من الخريطة البرامجية للتاريخ:', selectedDate);\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            console.log('📅 تفاصيل التاريخ:');\n            console.log('  - التاريخ الأصلي:', selectedDate);\n            console.log('  - التاريخ المحول:', date.toISOString().split('T')[0]);\n            console.log('  - يوم الأسبوع:', date.getDay(), '(0=أحد, 1=اثنين, 2=ثلاثاء, 3=أربعاء, 4=خميس, 5=جمعة, 6=سبت)');\n            console.log('  - بداية الأسبوع:', weekStartStr);\n            console.log('  - نهاية الأسبوع:', new Date(new Date(weekStartStr).getTime() + 6 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]);\n            console.log('🌐 طلب البيانات من:', \"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            console.log('📡 حالة الاستجابة:', response.status, response.statusText);\n            if (!response.ok) {\n                console.error('❌ خطأ في الاستجابة:', response.status);\n                return;\n            }\n            const data = await response.json();\n            console.log('📦 البيانات المستلمة من weekly-schedule:', data);\n            if (data.success && data.data && data.data.scheduleItems) {\n                const dayOfWeek = date.getDay();\n                console.log('📅 اليوم المطلوب:', dayOfWeek);\n                console.log('📦 إجمالي المواد في الخريطة:', data.data.scheduleItems.length);\n                // عرض جميع المواد للتشخيص\n                console.log('🔍 جميع المواد في الخريطة:');\n                data.data.scheduleItems.forEach((item, index)=>{\n                    var _item_mediaItem;\n                    console.log(\"  \".concat(index + 1, \". \").concat(((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime, \" - إعادة: \").concat(item.isRerun));\n                });\n                // فلترة المواد الخاصة بهذا اليوم\n                const dayItems = data.data.scheduleItems.filter((item)=>{\n                    const matches = item.dayOfWeek === dayOfWeek && item.mediaItem;\n                    if (matches) {\n                        var _item_mediaItem;\n                        console.log(\"✅ مادة متطابقة: \".concat((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, \" - يوم \").concat(item.dayOfWeek, \" - \").concat(item.startTime));\n                    } else {\n                        var _item_mediaItem1;\n                        console.log(\"❌ مادة غير متطابقة: \".concat(((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name) || 'بدون اسم', \" - يوم \").concat(item.dayOfWeek, \" (مطلوب: \").concat(dayOfWeek, \") - mediaItem: \").concat(!!item.mediaItem));\n                    }\n                    return matches;\n                });\n                console.log('📋 عدد المواد المطابقة لهذا اليوم:', dayItems.length);\n                if (dayItems.length > 0) {\n                    // ترتيب المواد حسب وقت البداية\n                    dayItems.sort((a, b)=>a.startTime.localeCompare(b.startTime));\n                    // بناء صفوف الجدول\n                    const newRows = [];\n                    let currentTime = '08:00:00';\n                    // إضافة صف فارغ في البداية\n                    newRows.push({\n                        id: \"empty_start_\".concat(Date.now()),\n                        type: 'empty',\n                        time: currentTime,\n                        canDelete: true\n                    });\n                    // إضافة المواد\n                    for (const item of dayItems){\n                        // إضافة المادة\n                        const mediaItem = item.mediaItem;\n                        const itemName = mediaItem.name;\n                        const itemType = mediaItem.type;\n                        const itemId = mediaItem.id;\n                        // إضافة تفاصيل المادة\n                        const details = [];\n                        if (mediaItem.episodeNumber) details.push(\"ح\".concat(mediaItem.episodeNumber));\n                        if (mediaItem.seasonNumber && mediaItem.seasonNumber > 0) details.push(\"م\".concat(mediaItem.seasonNumber));\n                        if (mediaItem.partNumber) details.push(\"ج\".concat(mediaItem.partNumber));\n                        const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n                        // تحديد نوع المادة\n                        let rowType = 'segment';\n                        let itemContent = \"\".concat(itemName).concat(detailsText);\n                        if ([\n                            'PROMO',\n                            'STING',\n                            'FILLER',\n                            'FILL_IN'\n                        ].includes(itemType)) {\n                            rowType = 'filler';\n                            itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                        }\n                        // حساب المدة\n                        let itemDuration = '00:01:00';\n                        if (mediaItem.duration) {\n                            itemDuration = mediaItem.duration;\n                        } else if (mediaItem.segments && mediaItem.segments.length > 0) {\n                            let totalSeconds = 0;\n                            mediaItem.segments.forEach((segment)=>{\n                                if (segment.duration) {\n                                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                                }\n                            });\n                            if (totalSeconds > 0) {\n                                const hours = Math.floor(totalSeconds / 3600);\n                                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                                const secs = totalSeconds % 60;\n                                itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                            }\n                        }\n                        // إضافة الصف\n                        const newRow = {\n                            id: \"\".concat(rowType, \"_\").concat(Date.now(), \"_\").concat(itemId),\n                            type: rowType,\n                            time: item.startTime,\n                            content: itemContent,\n                            mediaItemId: itemId,\n                            duration: itemDuration,\n                            canDelete: false,\n                            isRerun: item.isRerun,\n                            isTemporary: false,\n                            originalStartTime: item.startTime\n                        };\n                        console.log('🔒 إضافة مادة أساسية محمية:', newRow.content, 'canDelete:', newRow.canDelete);\n                        newRows.push(newRow);\n                        // تحديث الوقت الحالي\n                        currentTime = calculateNextTime(item.startTime, itemDuration);\n                    }\n                    // إضافة الاستنجات التلقائية لكل مادة\n                    let finalRows = [\n                        ...newRows\n                    ];\n                    const processedMediaNames = new Set();\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem;\n                        const mediaName = (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name;\n                        if (mediaName && !processedMediaNames.has(mediaName)) {\n                            processedMediaNames.add(mediaName);\n                            finalRows = addAutomaticStings(finalRows, mediaName);\n                        }\n                    });\n                    // إضافة صفوف فارغة في النهاية\n                    for(let i = 0; i < 8; i++){\n                        finalRows.push({\n                            id: \"empty_end_\".concat(Date.now(), \"_\").concat(i),\n                            type: 'empty',\n                            canDelete: true\n                        });\n                    }\n                    // تحديث الجدول\n                    setGridRows(finalRows);\n                    console.log('✅ تم بناء الجدول من الخريطة البرامجية:', finalRows.length, 'صف');\n                    // إعادة حساب الأوقات\n                    recalculateTimes(finalRows);\n                } else {\n                    console.log('⚠️ لا توجد مواد لهذا اليوم في الخريطة البرامجية');\n                    console.log('💡 تحقق من:');\n                    console.log('  - وجود مواد في الخريطة البرامجية لهذا اليوم');\n                    console.log('  - صحة يوم الأسبوع المحسوب');\n                    console.log('  - وجود mediaItem في كل مادة');\n                }\n            } else {\n                console.log('❌ فشل في جلب بيانات الخريطة البرامجية');\n                console.log('📋 تفاصيل الاستجابة:', data);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في بناء الجدول من الخريطة البرامجية:', error);\n            console.error('📋 تفاصيل الخطأ:', error.message);\n        }\n    };\n    // جلب الجدول الأسبوعي للمراجعة\n    const fetchWeeklySchedule = async ()=>{\n        try {\n            const date = new Date(selectedDate + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n            const weekStart = new Date(date);\n            weekStart.setDate(date.getDate() - date.getDay());\n            const weekStartStr = weekStart.toISOString().split('T')[0];\n            const response = await fetch(\"/api/weekly-schedule?weekStart=\".concat(weekStartStr));\n            const data = await response.json();\n            console.log('📊 استجابة API للجدول الأسبوعي:', data);\n            if (data.success && data.data) {\n                // استخراج مواد اليوم المحدد من scheduleItems\n                const dayOfWeek = new Date(selectedDate).getDay();\n                const daySchedule = [];\n                console.log('📅 البحث عن مواد اليوم:', dayOfWeek, 'للتاريخ:', selectedDate);\n                console.log('📦 البيانات المتاحة:', data.data);\n                // البحث في scheduleItems عن جميع مواد هذا اليوم (أساسية + إعادات)\n                if (data.data.scheduleItems && Array.isArray(data.data.scheduleItems)) {\n                    console.log('📋 إجمالي المواد:', data.data.scheduleItems.length);\n                    const dayItems = data.data.scheduleItems.filter((item)=>{\n                        var _item_mediaItem;\n                        console.log('🔍 فحص المادة:', (_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name, 'يوم:', item.dayOfWeek, 'إعادة:', item.isRerun, 'وقت:', item.startTime);\n                        return item.dayOfWeek === dayOfWeek; // إزالة فلتر !item.isRerun لعرض كل شيء\n                    }).sort((a, b)=>{\n                        // ترتيب خاص: 08:00-17:59 أولاً، ثم 18:00+، ثم 00:00-07:59\n                        const timeA = a.startTime;\n                        const timeB = b.startTime;\n                        const getTimeOrder = (time)=>{\n                            const hour = parseInt(time.split(':')[0]);\n                            if (hour >= 8 && hour < 18) return 1; // صباح ومساء\n                            if (hour >= 18) return 2; // برايم تايم\n                            return 3; // منتصف الليل والفجر\n                        };\n                        const orderA = getTimeOrder(timeA);\n                        const orderB = getTimeOrder(timeB);\n                        if (orderA !== orderB) return orderA - orderB;\n                        return timeA.localeCompare(timeB);\n                    });\n                    console.log('✅ مواد اليوم المفلترة:', dayItems.length);\n                    dayItems.forEach((item)=>{\n                        var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3;\n                        const scheduleItem = {\n                            time: item.startTime,\n                            name: ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || 'مادة غير محددة',\n                            episodeNumber: item.episodeNumber || ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.episodeNumber),\n                            partNumber: item.partNumber || ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.partNumber),\n                            seasonNumber: item.seasonNumber || ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.seasonNumber),\n                            isRerun: item.isRerun || false\n                        };\n                        daySchedule.push(scheduleItem);\n                        console.log('📺 إضافة مادة للخريطة:', scheduleItem);\n                    });\n                } else {\n                    console.log('❌ لا توجد scheduleItems أو ليست مصفوفة');\n                }\n                setWeeklySchedule(daySchedule);\n                console.log('📅 تم تحديث الخريطة الجانبية:', daySchedule.length, 'مادة');\n            } else {\n                console.log('❌ فشل في جلب البيانات أو البيانات فارغة');\n                setWeeklySchedule([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب الجدول الأسبوعي:', error);\n            setWeeklySchedule([]);\n        }\n    };\n    // فلترة المواد المتاحة\n    const filteredMedia = availableMedia.filter((item)=>{\n        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesType = filterType === 'ALL' || item.type === filterType;\n        return matchesSearch && matchesType;\n    });\n    // أنواع المواد للفلترة\n    const mediaTypes = [\n        'ALL',\n        'PROGRAM',\n        'SERIES',\n        'FILM',\n        'SONG',\n        'PROMO',\n        'STING',\n        'FILLER',\n        'NEXT',\n        'NOW',\n        'سنعود',\n        'عدنا',\n        'MINI',\n        'CROSS'\n    ];\n    // دالة ترجمة أنواع المواد\n    const getTypeLabel = (type)=>{\n        const typeLabels = {\n            'ALL': t('schedule.allTypes'),\n            'PROGRAM': t('schedule.types.program'),\n            'SERIES': t('schedule.types.series'),\n            'FILM': t('schedule.types.film'),\n            'MOVIE': t('schedule.types.film'),\n            'SONG': t('schedule.types.song'),\n            'STING': t('schedule.types.sting'),\n            'FILL_IN': t('schedule.types.fillIn'),\n            'FILLER': t('schedule.types.filler'),\n            'PROMO': t('schedule.types.promo'),\n            'NEXT': t('schedule.types.next'),\n            'NOW': t('schedule.types.now'),\n            'سنعود': 'سنعود',\n            'عدنا': 'عدنا',\n            'MINI': 'Mini',\n            'CROSS': 'Cross'\n        };\n        return typeLabels[type] || type;\n    };\n    // إضافة صف فارغ\n    const addEmptyRow = (afterIndex)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        const newRow = {\n            id: \"empty_\".concat(Date.now()),\n            type: 'empty',\n            canDelete: true\n        };\n        newRows.splice(afterIndex + 1, 0, newRow);\n        setGridRows(newRows);\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // إضافة صفوف فارغة متعددة\n    const addMultipleEmptyRows = function(afterIndex) {\n        let count = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 8;\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = [\n            ...gridRows\n        ];\n        for(let i = 0; i < count; i++){\n            const newRow = {\n                id: \"empty_\".concat(Date.now(), \"_\").concat(i),\n                type: 'empty',\n                canDelete: true\n            };\n            newRows.splice(afterIndex + 1 + i, 0, newRow);\n        }\n        setGridRows(newRows);\n        console.log(\"✅ تم إضافة \".concat(count, \" صف فارغ\"));\n        // استعادة موضع التمرير\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // التحقق من الحاجة لإضافة صفوف فارغة\n    const checkAndAddEmptyRows = (currentIndex)=>{\n        const currentRows = gridRows;\n        const nextEmptyIndex = currentRows.findIndex((row, index)=>index > currentIndex && row.type === 'empty');\n        // إذا لم توجد صفوف فارغة بعد الفاصل الحالي\n        if (nextEmptyIndex === -1) {\n            console.log('🔍 لا توجد صفوف فارغة بعد الموضع', currentIndex, '- إضافة 8 صفوف');\n            addMultipleEmptyRows(currentIndex, 8);\n        } else {\n            console.log('✅ توجد صفوف فارغة بعد الموضع', currentIndex, 'في الموضع', nextEmptyIndex);\n        }\n    };\n    // حذف صف فارغ\n    const deleteRow = (rowId)=>{\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        // استعادة موضع التمرير بعد الحذف\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 100);\n    };\n    // حذف سيجمنت مع حماية المواد الأساسية\n    const deleteSegment = (rowId)=>{\n        const row = gridRows.find((r)=>r.id === rowId);\n        // حماية مضاعفة للمواد الأساسية\n        if (row && row.type === 'segment' && (!row.isTemporary || !row.canDelete)) {\n            alert('⚠️ لا يمكن حذف المواد الأساسية من هنا!\\n\\nهذه المادة مستوردة من الخريطة البرامجية.\\n\\nلتغيير هذه المادة:\\n1. اذهب للخريطة البرامجية\\n2. قم بتعديل المادة هناك\\n3. ستتحدث تلقائياً في الجدول اليومي');\n            return;\n        }\n        // التأكد من أن المادة قابلة للحذف\n        if (row && !row.canDelete) {\n            alert('⚠️ هذه المادة محمية من الحذف!');\n            return;\n        }\n        if (confirm(t('schedule.confirmDeleteSegment'))) {\n            const newRows = gridRows.filter((row)=>row.id !== rowId);\n            recalculateTimes(newRows);\n            console.log('🗑️ تم حذف السيجمنت');\n        }\n    };\n    // حذف فاصل بدون تأكيد\n    const deleteFiller = (rowId)=>{\n        const newRows = gridRows.filter((row)=>row.id !== rowId);\n        recalculateTimes(newRows);\n        console.log('🗑️ تم حذف الفاصل');\n    };\n    // تحريك صف لأعلى\n    const moveRowUp = (index)=>{\n        if (index <= 0) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index - 1], newRows[index]] = [\n            newRows[index],\n            newRows[index - 1]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬆️ تم تحريك الصف لأعلى');\n    };\n    // تحريك صف لأسفل\n    const moveRowDown = (index)=>{\n        if (index >= gridRows.length - 1) return;\n        const newRows = [\n            ...gridRows\n        ];\n        [newRows[index], newRows[index + 1]] = [\n            newRows[index + 1],\n            newRows[index]\n        ];\n        recalculateTimes(newRows);\n        console.log('⬇️ تم تحريك الصف لأسفل');\n    };\n    // معالجة سحب الصفوف داخل الجدول\n    const handleRowDragStart = (e, index)=>{\n        e.dataTransfer.setData('text/plain', index.toString());\n        e.dataTransfer.effectAllowed = 'move';\n    };\n    // معالجة إسقاط الصفوف داخل الجدول\n    const handleRowDrop = (e, targetIndex)=>{\n        e.preventDefault();\n        const sourceIndex = parseInt(e.dataTransfer.getData('text/plain'));\n        if (sourceIndex === targetIndex) return;\n        const newRows = [\n            ...gridRows\n        ];\n        const [movedRow] = newRows.splice(sourceIndex, 1);\n        newRows.splice(targetIndex, 0, movedRow);\n        recalculateTimes(newRows);\n        console.log('🔄 تم تحريك الصف من', sourceIndex, 'إلى', targetIndex);\n        // تثبيت الجدول بعد السحب\n        setTimeout(()=>{\n            const gridElement = document.querySelector('.schedule-grid');\n            if (gridElement) {\n                gridElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'start'\n                });\n            }\n        }, 100);\n    };\n    // معالجة إسقاط المواد\n    const handleDrop = (e, rowIndex)=>{\n        e.preventDefault();\n        // حفظ موضع التمرير الحالي قبل أي تغيير\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        const rowElement = e.currentTarget.closest('.grid-row');\n        const rowOffsetTop = (rowElement === null || rowElement === void 0 ? void 0 : rowElement.getBoundingClientRect().top) || 0;\n        try {\n            var _mediaData_segments, _mediaData_segments1;\n            // محاولة الحصول على البيانات بطرق مختلفة\n            let mediaData;\n            const jsonData = e.dataTransfer.getData('application/json');\n            const textData = e.dataTransfer.getData('text/plain');\n            console.log('📥 البيانات المسحوبة:', {\n                jsonData,\n                textData\n            });\n            // التحقق من أن هذا ليس سحب صف داخلي\n            if (textData && !jsonData && /^\\d+$/.test(textData)) {\n                console.log('🔄 هذا سحب صف داخلي، تجاهل');\n                return;\n            }\n            if (jsonData) {\n                mediaData = JSON.parse(jsonData);\n            } else if (textData) {\n                try {\n                    mediaData = JSON.parse(textData);\n                } catch (e) {\n                    console.error('❌ لا يمكن تحليل البيانات المسحوبة');\n                    return;\n                }\n            } else {\n                console.error('❌ لا توجد بيانات مسحوبة');\n                return;\n            }\n            // التحقق من أن الصف فارغ\n            const targetRow = gridRows[rowIndex];\n            if (targetRow.type !== 'empty') {\n                alert('يمكن إسقاط المواد في الصفوف الفارغة فقط');\n                return;\n            }\n            console.log('📥 إسقاط مادة - البيانات الخام:', mediaData);\n            // التحقق من صحة البيانات وإصلاح البنية إذا لزم الأمر\n            if (!mediaData || typeof mediaData !== 'object' || typeof mediaData === 'string' || Array.isArray(mediaData)) {\n                console.error('❌ بيانات المادة غير صحيحة:', mediaData);\n                console.error('❌ نوع البيانات:', typeof mediaData);\n                console.error('❌ هل هو مصفوفة؟', Array.isArray(mediaData));\n                return;\n            }\n            // التأكد من وجود الاسم\n            const itemName = mediaData.name || mediaData.title || 'مادة غير محددة';\n            const itemType = mediaData.type || 'UNKNOWN';\n            const itemId = mediaData.id || Date.now().toString();\n            console.log('📥 معلومات المادة:', {\n                name: itemName,\n                type: itemType,\n                id: itemId,\n                segments: ((_mediaData_segments = mediaData.segments) === null || _mediaData_segments === void 0 ? void 0 : _mediaData_segments.length) || 0\n            });\n            // تحديد نوع المادة المسحوبة\n            let dragItemType = 'filler';\n            let itemContent = itemName;\n            // إضافة تفاصيل المادة\n            const details = [];\n            if (mediaData.episodeNumber) details.push(\"ح\".concat(mediaData.episodeNumber));\n            if (mediaData.seasonNumber && mediaData.seasonNumber > 0) details.push(\"م\".concat(mediaData.seasonNumber));\n            if (mediaData.partNumber) details.push(\"ج\".concat(mediaData.partNumber));\n            const detailsText = details.length > 0 ? \" (\".concat(details.join(' - '), \")\") : '';\n            // المواد الصغيرة تعتبر فواصل، المواد الكبيرة تعتبر سيجمنتات\n            if ([\n                'PROMO',\n                'STING',\n                'FILLER',\n                'FILL_IN'\n            ].includes(itemType)) {\n                dragItemType = 'filler';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" - \").concat(itemType);\n                // تخزين نوع المادة لاستخدامه في تمييز الألوان\n                mediaData.mediaType = itemType;\n            } else {\n                dragItemType = 'segment';\n                itemContent = \"\".concat(itemName).concat(detailsText, \" (مادة إضافية)\");\n            }\n            // حساب المدة الحقيقية للمادة\n            let itemDuration = '00:01:00'; // مدة افتراضية\n            console.log('🔍 تحليل مدة المادة:', {\n                name: itemName,\n                hasSegments: !!(mediaData.segments && mediaData.segments.length > 0),\n                segmentsCount: ((_mediaData_segments1 = mediaData.segments) === null || _mediaData_segments1 === void 0 ? void 0 : _mediaData_segments1.length) || 0,\n                hasDuration: !!mediaData.duration,\n                directDuration: mediaData.duration\n            });\n            if (mediaData.segments && mediaData.segments.length > 0) {\n                // حساب إجمالي مدة جميع السيجمنتات\n                let totalSeconds = 0;\n                mediaData.segments.forEach((segment, index)=>{\n                    if (segment.duration) {\n                        const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                        const segmentSeconds = hours * 3600 + minutes * 60 + seconds;\n                        totalSeconds += segmentSeconds;\n                        console.log(\"  \\uD83D\\uDCFA سيجمنت \".concat(index + 1, \": \").concat(segment.duration, \" (\").concat(segmentSeconds, \" ثانية)\"));\n                    }\n                });\n                if (totalSeconds > 0) {\n                    const hours = Math.floor(totalSeconds / 3600);\n                    const minutes = Math.floor(totalSeconds % 3600 / 60);\n                    const secs = totalSeconds % 60;\n                    itemDuration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n                }\n                console.log('📊 حساب مدة المادة من السيجمنتات:', {\n                    name: itemName,\n                    segments: mediaData.segments.length,\n                    totalSeconds,\n                    finalDuration: itemDuration\n                });\n            } else if (mediaData.duration) {\n                // استخدام المدة المباشرة إذا كانت موجودة\n                itemDuration = mediaData.duration;\n                console.log('📊 استخدام مدة مباشرة:', itemDuration);\n            } else {\n                console.log('⚠️ لا توجد مدة للمادة، استخدام مدة افتراضية:', itemDuration);\n            }\n            // إنشاء كود للمادة المسحوبة\n            let itemCode = '';\n            if (mediaData.segmentCode) {\n                itemCode = mediaData.segmentCode;\n            } else if (mediaData.id) {\n                itemCode = \"\".concat(itemType, \"_\").concat(mediaData.id);\n            } else {\n                itemCode = \"\".concat(itemType, \"_\").concat(Date.now().toString().slice(-6));\n            }\n            // إنشاء صف جديد مع المدة الحقيقية\n            const newRow = {\n                id: \"dropped_\".concat(Date.now()),\n                type: dragItemType,\n                content: itemContent,\n                mediaItemId: itemId,\n                segmentCode: itemCode,\n                duration: itemDuration,\n                canDelete: true\n            };\n            // إضافة نوع المادة للفواصل لتمييزها بألوان مختلفة\n            if (dragItemType === 'filler' && [\n                'PROMO',\n                'STING',\n                'FILL_IN',\n                'FILLER'\n            ].includes(itemType)) {\n                newRow.mediaType = itemType;\n                console.log(\"\\uD83C\\uDFA8 تم تعيين نوع المادة: \".concat(itemType, \" للصف الجديد\"));\n            }\n            // التأكد من أن الصف المستهدف فارغ\n            if (gridRows[rowIndex].type !== 'empty') {\n                console.error('❌ الصف المستهدف ليس فارغاً:', gridRows[rowIndex]);\n                return;\n            }\n            // استبدال الصف الفارغ مباشرة\n            const newRows = [\n                ...gridRows\n            ];\n            newRows[rowIndex] = newRow;\n            console.log('✅ تم إضافة المادة:', {\n                name: itemName,\n                type: dragItemType,\n                duration: itemDuration,\n                position: rowIndex,\n                content: itemContent,\n                beforeType: gridRows[rowIndex].type,\n                afterType: newRow.type\n            });\n            // تحديث الصفوف مباشرة\n            setGridRows(newRows);\n            // إعادة حساب الأوقات بعد تأخير قصير\n            setTimeout(()=>{\n                recalculateTimes(newRows);\n                // التحقق من الحاجة لإضافة صفوف فارغة\n                checkAndAddEmptyRows(rowIndex);\n                // تثبيت الجدول بعد إسقاط المادة\n                setTimeout(()=>{\n                    const gridElement = document.querySelector('.schedule-grid');\n                    if (gridElement) {\n                        gridElement.scrollIntoView({\n                            behavior: 'smooth',\n                            block: 'start'\n                        });\n                        console.log('📍 تم تثبيت الجدول بعد إسقاط المادة');\n                    }\n                }, 100);\n            }, 50);\n        } catch (error) {\n            console.error('❌ خطأ في إسقاط المادة:', error);\n            // في حالة حدوث خطأ، نستعيد موضع التمرير على أي حال\n            setTimeout(()=>{\n                if (gridBody) {\n                    gridBody.scrollTop = currentScrollTop;\n                }\n            }, 100);\n        }\n    };\n    // إعادة حساب الأوقات مثل Excel\n    const recalculateTimes = (rows)=>{\n        const newRows = [\n            ...rows\n        ];\n        let currentTime = '08:00:00'; // نقطة البداية بالثواني\n        let hasFillers = false; // هل تم إضافة فواصل؟\n        // التحقق من وجود فواصل\n        hasFillers = rows.some((row)=>row.type === 'filler');\n        console.log('🔄 بدء إعادة حساب الأوقات من 08:00:00', hasFillers ? '(يوجد فواصل)' : '(لا يوجد فواصل)');\n        for(let i = 0; i < newRows.length; i++){\n            const row = newRows[i];\n            if (row.type === 'segment' || row.type === 'filler') {\n                // عرض الوقت فقط للسيجمنت الأول أو إذا كان هناك فواصل\n                if (i === 0 || hasFillers) {\n                    newRows[i] = {\n                        ...row,\n                        time: currentTime\n                    };\n                } else {\n                    newRows[i] = {\n                        ...row,\n                        time: undefined\n                    };\n                }\n                if (row.duration) {\n                    // حساب الوقت التالي بناءً على المدة\n                    const nextTime = calculateNextTime(currentTime, row.duration);\n                    console.log(\"⏰ \".concat(row.type, ': \"').concat(row.content, '\" - من ').concat(currentTime, \" إلى \").concat(nextTime, \" (مدة: \").concat(row.duration, \")\"));\n                    currentTime = nextTime;\n                }\n            } else if (row.type === 'empty') {\n                // الصفوف الفارغة لا تؤثر على الوقت\n                newRows[i] = {\n                    ...row,\n                    time: undefined\n                };\n            }\n            // التحقق من الوصول لوقت مادة أساسية\n            if (row.originalStartTime && hasFillers) {\n                const targetMinutes = timeToMinutes(row.originalStartTime);\n                const currentMinutes = timeToMinutes(currentTime);\n                const difference = targetMinutes - currentMinutes;\n                if (Math.abs(difference) > 5) {\n                    console.log('⚠️ انحراف زمني: المادة \"'.concat(row.content, '\" مجدولة في ').concat(row.originalStartTime, \" لكن ستدخل في \").concat(currentTime, \" (فرق: \").concat(difference, \" دقيقة)\"));\n                } else {\n                    console.log('✅ توقيت صحيح: المادة \"'.concat(row.content, '\" ستدخل في ').concat(currentTime, \" (مجدولة: \").concat(row.originalStartTime, \")\"));\n                }\n            }\n        }\n        console.log(\"\\uD83C\\uDFC1 انتهاء الحساب - الوقت النهائي: \".concat(currentTime));\n        // حفظ موضع التمرير قبل التحديث\n        const gridBody = document.querySelector('.grid-body');\n        const currentScrollTop = (gridBody === null || gridBody === void 0 ? void 0 : gridBody.scrollTop) || 0;\n        // تحديث الصفوف دائماً لضمان التحديث الصحيح\n        setGridRows(newRows);\n        // استعادة موضع التمرير بعد التحديث\n        setTimeout(()=>{\n            if (gridBody) {\n                gridBody.scrollTop = currentScrollTop;\n            }\n        }, 50);\n    };\n    // تحويل الوقت إلى دقائق\n    const timeToMinutes = (time)=>{\n        const [hours, minutes] = time.split(':').map(Number);\n        return hours * 60 + minutes;\n    };\n    // حساب المدة الإجمالية للمادة بدقة\n    const calculateTotalDuration = (item)=>{\n        if (item.segments && item.segments.length > 0) {\n            let totalSeconds = 0;\n            item.segments.forEach((segment)=>{\n                if (segment.duration) {\n                    const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n                    totalSeconds += hours * 3600 + minutes * 60 + seconds;\n                }\n            });\n            if (totalSeconds > 0) {\n                const hours = Math.floor(totalSeconds / 3600);\n                const minutes = Math.floor(totalSeconds % 3600 / 60);\n                const secs = totalSeconds % 60;\n                return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n            }\n        }\n        return item.duration || '00:01:00';\n    };\n    // حفظ تعديلات الجدول\n    const saveScheduleChanges = async ()=>{\n        try {\n            console.log('💾 بدء حفظ التعديلات...');\n            console.log('📅 التاريخ:', selectedDate);\n            console.log('📝 عدد الصفوف:', gridRows.length);\n            const response = await fetch('/api/daily-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    date: selectedDate,\n                    scheduleRows: gridRows\n                })\n            });\n            const result = await response.json();\n            if (response.ok && result.success) {\n                console.log('✅ تم حفظ تعديلات الجدول الإذاعي بنجاح');\n                alert('✅ تم حفظ التعديلات بنجاح!');\n            } else {\n                console.error('❌ فشل في حفظ التعديلات:', result.error);\n                alert('❌ فشل في حفظ التعديلات: ' + result.error);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ التعديلات:', error);\n            alert('❌ خطأ في الاتصال بالخادم');\n        }\n    };\n    // تصدير الجدول الإذاعي إلى Excel\n    const exportDailySchedule = async ()=>{\n        try {\n            console.log('📊 بدء تصدير الجدول الإذاعي اليومي...');\n            if (!selectedDate) {\n                alert('يرجى تحديد التاريخ أولاً');\n                return;\n            }\n            // حفظ البيانات الحالية أولاً\n            console.log('💾 حفظ البيانات الحالية...');\n            await saveScheduleChanges();\n            // إرسال البيانات الحالية مع طلب التصدير (تجاهل المواد المؤقتة)\n            const currentData = {\n                date: selectedDate,\n                scheduleRows: gridRows.filter((row)=>(row.type === 'segment' || row.type === 'filler' && row.content) && !row.isTemporary)\n            };\n            const response = await fetch(\"/api/export-daily-schedule-new?date=\".concat(selectedDate), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(currentData)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"Daily_Schedule_\".concat(selectedDate, \".xlsx\");\n            document.body.appendChild(a);\n            a.click();\n            window.URL.revokeObjectURL(url);\n            document.body.removeChild(a);\n            console.log('✅ تم تصدير الجدول الإذاعي بنجاح');\n            alert('✅ تم تصدير الجدول بنجاح!');\n        } catch (error) {\n            console.error('❌ خطأ في تصدير الجدول:', error);\n            alert('❌ فشل في تصدير الجدول: ' + error.message);\n        }\n    };\n    // حساب الوقت التالي بناءً على المدة (بدقة الثواني)\n    const calculateNextTime = (startTime, duration)=>{\n        // تحليل وقت البداية\n        const startParts = startTime.split(':');\n        const startHours = parseInt(startParts[0]);\n        const startMins = parseInt(startParts[1]);\n        const startSecs = parseInt(startParts[2] || '0');\n        // تحليل المدة\n        const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n        // حساب إجمالي الثواني\n        let totalSeconds = startHours * 3600 + startMins * 60 + startSecs;\n        totalSeconds += durHours * 3600 + durMins * 60 + durSecs;\n        // تحويل إلى ساعات ودقائق وثواني\n        const hours = Math.floor(totalSeconds / 3600) % 24;\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        return \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            title: t('schedule.daily'),\n            subtitle: t('schedule.scheduledMedia'),\n            icon: \"\\uD83D\\uDCCA\",\n            fullWidth: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    id: \"1b542d50d800f28e\",\n                    children: \".grid-row.empty.jsx-1b542d50d800f28e{background:#b8dce8!important;background-color:#b8dce8!important;min-height:50px!important;height:50px!important;border-color:#a0c4d4!important}.grid-row.empty.jsx-1b542d50d800f28e .action-btn.jsx-1b542d50d800f28e{font-size:.75rem!important;padding:5px 8px!important;min-width:30px!important;height:30px!important;line-height:1.2!important;-webkit-border-radius:4px!important;-moz-border-radius:4px!important;border-radius:4px!important;margin:2px!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important;overflow:hidden!important;white-space:nowrap!important}.grid-row.empty.jsx-1b542d50d800f28e .actions-cell.jsx-1b542d50d800f28e{padding:8px!important;gap:5px!important;display:-webkit-box!important;display:-webkit-flex!important;display:-moz-box!important;display:-ms-flexbox!important;display:flex!important;-webkit-box-align:center!important;-webkit-align-items:center!important;-moz-box-align:center!important;-ms-flex-align:center!important;align-items:center!important;-webkit-box-pack:center!important;-webkit-justify-content:center!important;-moz-box-pack:center!important;-ms-flex-pack:center!important;justify-content:center!important;height:50px!important;max-height:50px!important;overflow:hidden!important;-webkit-box-sizing:border-box!important;-moz-box-sizing:border-box!important;box-sizing:border-box!important}\"\n                }, void 0, false, void 0, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#b8dce8',\n                        minHeight: '100vh',\n                        margin: '-2rem',\n                        padding: '2rem'\n                    },\n                    className: \"jsx-1b542d50d800f28e\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-controls\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"date-selector\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"schedule-date\",\n                                            className: \"jsx-1b542d50d800f28e\",\n                                            children: [\n                                                t('common.selectDate'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1183,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            id: \"schedule-date\",\n                                            type: \"date\",\n                                            value: selectedDate,\n                                            onChange: (e)=>setSelectedDate(e.target.value),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-input\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1184,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1182,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"header-buttons\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: saveScheduleChanges,\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button primary\",\n                                            children: [\n                                                \"\\uD83D\\uDCBE \",\n                                                t('common.save')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1194,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: exportDailySchedule,\n                                            style: {\n                                                background: 'linear-gradient(45deg, #17a2b8, #138496)',\n                                                color: 'white'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button export\",\n                                            children: [\n                                                \"\\uD83D\\uDCCA \",\n                                                t('reports.exportExcel')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1201,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>window.location.href = '/daily-schedule/import',\n                                            style: {\n                                                background: 'linear-gradient(45deg, #8e44ad, #9b59b6)',\n                                                color: 'white'\n                                            },\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button\",\n                                            children: \"\\uD83D\\uDCE5 Import List\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1212,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowWeeklySchedule(!showWeeklySchedule),\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"glass-button\",\n                                            children: showWeeklySchedule ? \"\\uD83D\\uDCCB \".concat(t('schedule.hideSchedule')) : \"\\uD83D\\uDCC5 \".concat(t('schedule.showSchedule'))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1223,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 1181,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-content\",\n                            children: [\n                                showWeeklySchedule && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: t('schedule.weeklySchedule')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1238,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-schedule-list\",\n                                            children: Array.isArray(weeklySchedule) && weeklySchedule.length > 0 ? weeklySchedule.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-item\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-time\",\n                                                            children: item.time\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1243,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-content\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-name\",\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1245,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ح\",\n                                                                        item.episodeNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-details\",\n                                                                    children: [\n                                                                        \"ج\",\n                                                                        item.partNumber\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1250,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1244,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"weekly-status\",\n                                                            children: item.isRerun ? '🔄' : '🎯'\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1253,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1242,\n                                                    columnNumber: 19\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"no-data\",\n                                                children: t('schedule.noWeeklyData')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1237,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-sidebar\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-title\",\n                                            children: \"المواد المتاحة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1267,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"sidebar-controls\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: filterType,\n                                                    onChange: (e)=>setFilterType(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"filter-select\",\n                                                    children: mediaTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: type,\n                                                            className: \"jsx-1b542d50d800f28e\",\n                                                            children: getTypeLabel(type)\n                                                        }, type, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1277,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1271,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"البحث في المواد...\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value),\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"search-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1283,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-header\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                            children: \"الاسم\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1296,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                            children: \"النوع\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                            children: \"المدة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 15\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                            children: \"الكود\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1299,\n                                                            columnNumber: 15\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1295,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-table-body\",\n                                                    children: filteredMedia.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            draggable: true,\n                                                            onDragStart: (e)=>{\n                                                                e.dataTransfer.setData('application/json', JSON.stringify(item));\n                                                                // إضافة class للتصغير أثناء السحب\n                                                                e.currentTarget.classList.add('dragging');\n                                                            },\n                                                            onDragEnd: (e)=>{\n                                                                // إزالة class بعد انتهاء السحب\n                                                                e.currentTarget.classList.remove('dragging');\n                                                            },\n                                                            \"data-type\": item.type,\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-row \".concat(item.type.toLowerCase()),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    title: \"\".concat(item.name, \" - \").concat(getTypeLabel(item.type)),\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-name\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-name-text\",\n                                                                            children: item.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1321,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-tags\",\n                                                                            children: [\n                                                                                item.episodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"episode-tag\",\n                                                                                    children: [\n                                                                                        \"ح\",\n                                                                                        item.episodeNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1323,\n                                                                                    columnNumber: 46\n                                                                                }, this),\n                                                                                item.seasonNumber && item.seasonNumber > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"season-tag\",\n                                                                                    children: [\n                                                                                        \"م\",\n                                                                                        item.seasonNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1324,\n                                                                                    columnNumber: 70\n                                                                                }, this),\n                                                                                item.partNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"part-tag\",\n                                                                                    children: [\n                                                                                        \"ج\",\n                                                                                        item.partNumber\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                                    lineNumber: 1325,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1322,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1320,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-type\",\n                                                                    children: getTypeLabel(item.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1328,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-duration\",\n                                                                    children: calculateTotalDuration(item)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1329,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"media-col-code\",\n                                                                    children: item.segmentCode || item.id\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1330,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, item.id, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1305,\n                                                            columnNumber: 17\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1303,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1293,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"schedule-grid\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-header\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-column\",\n                                                    children: \"الكود\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1340,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-column\",\n                                                    children: \"الوقت\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1341,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-column\",\n                                                    children: \"المحتوى\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1342,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-column\",\n                                                    children: \"المدة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1343,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-column\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1344,\n                                                    columnNumber: 13\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-column\",\n                                                    children: \"الإجراءات\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1345,\n                                                    columnNumber: 13\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1339,\n                                            columnNumber: 11\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-body\",\n                                            children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"loading\",\n                                                children: t('common.loading')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                lineNumber: 1350,\n                                                columnNumber: 15\n                                            }, this) : gridRows.map((row, index)=>{\n                                                var _row_content, _row_content1, _row_content2, _row_content3;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    draggable: row.type === 'filler' || row.type === 'empty' || row.type === 'segment' && row.canDelete,\n                                                    onDragStart: (e)=>handleRowDragStart(e, index),\n                                                    onDrop: (e)=>{\n                                                        handleRowDrop(e, index);\n                                                        // إزالة تأثير drag-over\n                                                        e.currentTarget.classList.remove('drag-over');\n                                                    },\n                                                    onDragOver: (e)=>{\n                                                        e.preventDefault();\n                                                        // إضافة تأثير بصري عند السحب فوق الصف الفارغ\n                                                        if (row.type === 'empty') {\n                                                            e.currentTarget.classList.add('drag-over');\n                                                        }\n                                                    },\n                                                    onDragLeave: (e)=>{\n                                                        // إزالة تأثير drag-over عند مغادرة الصف\n                                                        e.currentTarget.classList.remove('drag-over');\n                                                    },\n                                                    \"data-type\": // استخدام الحقل الإضافي mediaType إن وجد\n                                                    row.mediaType ? row.mediaType : ((_row_content = row.content) === null || _row_content === void 0 ? void 0 : _row_content.includes('PROMO')) ? 'PROMO' : ((_row_content1 = row.content) === null || _row_content1 === void 0 ? void 0 : _row_content1.includes('STING')) ? 'STING' : ((_row_content2 = row.content) === null || _row_content2 === void 0 ? void 0 : _row_content2.includes('FILL_IN')) ? 'FILL_IN' : ((_row_content3 = row.content) === null || _row_content3 === void 0 ? void 0 : _row_content3.includes('FILLER')) ? 'FILLER' : '',\n                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"grid-row \".concat(row.type, \" \").concat(row.type === 'segment' ? row.isRerun ? 'rerun-content' : row.isTemporary ? 'temp-content' : 'primary-content' : row.type === 'filler' ? row.mediaType === 'PROMO' ? 'promo-content' : row.mediaType === 'STING' ? 'sting-content' : row.mediaType === 'FILLER' ? 'filler-content' : row.mediaType === 'FILL_IN' ? 'filler-content' : 'break-content' : 'primary-content'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"code-cell\",\n                                                            children: row.type === 'segment' || row.type === 'filler' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-code\",\n                                                                children: row.segmentCode || \"\".concat(row.type.toUpperCase(), \"_\").concat(row.id.slice(-6))\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1399,\n                                                                columnNumber: 23\n                                                            }, this) : ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1397,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"time-cell\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-time\",\n                                                                children: row.time || ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1405,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1404,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onDrop: (e)=>handleDrop(e, index),\n                                                            onDragOver: (e)=>e.preventDefault(),\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-cell\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-1b542d50d800f28e\" + \" \" + \"content-text\",\n                                                                children: row.content || ''\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                lineNumber: 1414,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1409,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"duration-cell\",\n                                                            children: row.duration || ''\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1418,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"status-cell\",\n                                                            children: [\n                                                                row.type === 'segment' && row.isTemporary && '🟣 مؤقت',\n                                                                row.type === 'segment' && !row.isRerun && !row.isTemporary && (row.originalStartTime ? Math.abs(timeToMinutes(row.originalStartTime) - timeToMinutes(row.time || '00:00:00')) > 5 ? '⚠️ انحراف' : '✅ دقيق' : '🎯 أساسي'),\n                                                                row.type === 'segment' && row.isRerun && !row.isTemporary && '🔄 إعادة',\n                                                                row.type === 'filler' && '📺 فاصل',\n                                                                row.type === 'empty' && '⚪ فارغ'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1421,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"actions-cell\",\n                                                            children: [\n                                                                row.type === 'empty' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"إضافة صف\",\n                                                                            onClick: ()=>addEmptyRow(index),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-row\",\n                                                                            children: \"➕\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1436,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"إضافة 8 صفوف\",\n                                                                            onClick: ()=>addMultipleEmptyRows(index, 8),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn add-multiple-rows\",\n                                                                            children: \"➕➕\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1443,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        row.canDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"حذف صف\",\n                                                                            onClick: ()=>deleteRow(row.id),\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                            children: \"➖\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                row.type === 'filler' && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"تحريك لأعلى\",\n                                                                            onClick: ()=>moveRowUp(index),\n                                                                            disabled: index === 0,\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-up\",\n                                                                            children: \"⬆️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1463,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"تحريك لأسفل\",\n                                                                            onClick: ()=>moveRowDown(index),\n                                                                            disabled: index === gridRows.length - 1,\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn move-down\",\n                                                                            children: \"⬇️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1471,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            title: \"حذف فاصل\",\n                                                                            onClick: ()=>{\n                                                                                // تحويل الفاصل إلى صف فارغ\n                                                                                const newRows = [\n                                                                                    ...gridRows\n                                                                                ];\n                                                                                newRows[index] = {\n                                                                                    id: \"empty_\".concat(Date.now()),\n                                                                                    type: 'empty',\n                                                                                    canDelete: true\n                                                                                };\n                                                                                // إعادة حساب الأوقات\n                                                                                recalculateTimes(newRows);\n                                                                            },\n                                                                            className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                            children: \"\\uD83D\\uDDD1️\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                            lineNumber: 1479,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                row.type === 'segment' && row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    title: \"استبدال بمادة حقيقية\",\n                                                                    onClick: ()=>{\n                                                                        alert('💡 لاستبدال المادة المؤقتة:\\n\\n1. أضف المادة الحقيقية لقاعدة البيانات\\n2. احذف المادة المؤقتة\\n3. اسحب المادة الجديدة من القائمة الجانبية');\n                                                                    },\n                                                                    style: {\n                                                                        color: '#9c27b0'\n                                                                    },\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn replace-temp\",\n                                                                    children: \"\\uD83D\\uDD04\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1500,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                row.type === 'segment' && row.canDelete && !row.isTemporary && !readOnlyMode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    title: \"حذف سيجمنت\",\n                                                                    onClick: ()=>deleteSegment(row.id),\n                                                                    className: \"jsx-1b542d50d800f28e\" + \" \" + \"action-btn delete-row\",\n                                                                    children: \"❌\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1512,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                            lineNumber: 1433,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, row.id, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                                    lineNumber: 1353,\n                                                    columnNumber: 17\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 11\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                                    lineNumber: 1338,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                            lineNumber: 1234,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n                    lineNumber: 1178,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n            lineNumber: 1145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\daily-schedule\\\\page.tsx\",\n        lineNumber: 1144,\n        columnNumber: 5\n    }, this);\n}\n_s(DailySchedulePage, \"OrBF0na36HW7NPf2T+tiAqx8I7A=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        react_i18next__WEBPACK_IMPORTED_MODULE_5__.useTranslation\n    ];\n});\n_c = DailySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"DailySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/daily-schedule/page.tsx\n"));

/***/ })

});