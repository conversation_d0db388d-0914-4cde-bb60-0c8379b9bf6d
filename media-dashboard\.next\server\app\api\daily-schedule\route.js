/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/daily-schedule/route";
exports.ids = ["app/api/daily-schedule/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdaily-schedule%2Froute&page=%2Fapi%2Fdaily-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdaily-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdaily-schedule%2Froute&page=%2Fapi%2Fdaily-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdaily-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_daily_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/daily-schedule/route.ts */ \"(rsc)/./src/app/api/daily-schedule/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/daily-schedule/route\",\n        pathname: \"/api/daily-schedule\",\n        filename: \"route\",\n        bundlePath: \"app/api/daily-schedule/route\"\n    },\n    resolvedPagePath: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\api\\\\daily-schedule\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_project_sport_media_dashboard_clean_media_dashboard_src_app_api_daily_schedule_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdaily-schedule%2Froute&page=%2Fapi%2Fdaily-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdaily-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/daily-schedule/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/daily-schedule/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _shared_data__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared-data */ \"(rsc)/./src/app/api/shared-data.ts\");\n\n\n\n// استيراد البيانات المشتركة\n\n// Helper functions\nfunction getWeekStart(date) {\n    const d = new Date(date);\n    const day = d.getDay();\n    const diff = d.getDate() - day;\n    return new Date(d.setDate(diff));\n}\nfunction formatDate(date) {\n    return date.toISOString().split('T')[0];\n}\nfunction timeToMinutes(time) {\n    const [hours, minutes] = time.split(':').map(Number);\n    return hours * 60 + minutes;\n}\nfunction addMinutesToTime(timeStr, minutes) {\n    const totalMinutes = timeToMinutes(timeStr) + minutes;\n    const hours = Math.floor(totalMinutes / 60) % 24;\n    const mins = totalMinutes % 60;\n    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;\n}\n// GET - جلب الجدول الإذاعي لتاريخ محدد\nasync function GET(request) {\n    try {\n        console.log('🚀 بدء معالجة طلب daily-schedule API');\n        const { searchParams } = new URL(request.url);\n        const dateParam = searchParams.get('date');\n        console.log('📅 التاريخ المطلوب:', dateParam);\n        if (!dateParam) {\n            console.log('❌ لم يتم تمرير تاريخ');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'التاريخ مطلوب'\n            }, {\n                status: 400\n            });\n        }\n        const date = new Date(dateParam + 'T12:00:00'); // إضافة وقت لتجنب مشاكل المنطقة الزمنية\n        const dayOfWeek = date.getDay();\n        // حساب بداية الأسبوع (الأحد) - الطريقة الصحيحة\n        const sunday = new Date(date);\n        sunday.setDate(date.getDate() - date.getDay());\n        const weekStart = sunday.toISOString().split('T')[0];\n        // أسماء الأيام للتوضيح\n        const dayNames = [\n            'الأحد',\n            'الاثنين',\n            'الثلاثاء',\n            'الأربعاء',\n            'الخميس',\n            'الجمعة',\n            'السبت'\n        ];\n        console.log('🔍 تفاصيل حساب الأسبوع:');\n        console.log('  📅 التاريخ المطلوب:', dateParam);\n        console.log('  📅 التاريخ المحول:', date.toISOString().split('T')[0]);\n        console.log('  📊 يوم الأسبوع (رقم):', dayOfWeek);\n        console.log('  📊 يوم الأسبوع (اسم):', dayNames[dayOfWeek]);\n        console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n        console.log('  📅 يوم الأحد:', sunday.toISOString().split('T')[0]);\n        // التحقق من وجود جدول محفوظ مسبقاً\n        const savedFileName = `daily-schedule-${dateParam}.json`;\n        const savedFilePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'saved-schedules', savedFileName);\n        if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(savedFilePath)) {\n            console.log('📂 تم العثور على جدول محفوظ:', savedFileName);\n            try {\n                const savedData = JSON.parse(fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(savedFilePath, 'utf8'));\n                console.log('✅ تم تحميل الجدول المحفوظ بنجاح');\n                console.log('📊 إحصائيات الجدول المحفوظ:', {\n                    totalRows: savedData.totalRows,\n                    segments: savedData.segments,\n                    fillers: savedData.fillers,\n                    emptyRows: savedData.emptyRows,\n                    savedAt: savedData.savedAt\n                });\n                // جلب المواد المتاحة للقائمة الجانبية مع فلترة المواد الصالحة فقط\n                const allMedia = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getAllMediaItems)().filter((item)=>{\n                    // 1. التحقق من خاصية TX\n                    const showInTX = item.showInTX === true;\n                    if (!showInTX) {\n                        console.log(`🚫 مادة غير مفعلة في TX: ${item.name}`);\n                        return false;\n                    }\n                    // 2. التحقق من الحالة\n                    const isValidStatus = item.status === 'VALID';\n                    if (!isValidStatus) {\n                        console.log(`🚫 مادة غير صالحة: ${item.name} (حالة: ${item.status})`);\n                        return false;\n                    }\n                    // 3. التحقق من تاريخ الانتهاء\n                    const isNotExpired = !item.endDate || new Date(item.endDate) >= new Date();\n                    if (!isNotExpired) {\n                        console.log(`🚫 مادة منتهية الصلاحية: ${item.name}`);\n                        return false;\n                    }\n                    console.log(`✅ مادة صالحة للقائمة: ${item.name} (نوع: ${item.type})`);\n                    return true;\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        date: dateParam,\n                        dayOfWeek,\n                        scheduleItems: [],\n                        scheduleRows: savedData.scheduleRows,\n                        availableMedia: allMedia\n                    },\n                    fromSavedFile: true,\n                    savedAt: savedData.savedAt\n                });\n            } catch (error) {\n                console.error('❌ خطأ في قراءة الجدول المحفوظ:', error);\n            // المتابعة لبناء جدول جديد\n            }\n        }\n        // جلب البيانات من الخريطة البرامجية\n        console.log(`🌐 طلب البيانات من: ${request.nextUrl.origin}/api/weekly-schedule?weekStart=${weekStart}`);\n        const scheduleResponse = await fetch(`${request.nextUrl.origin}/api/weekly-schedule?weekStart=${weekStart}`);\n        if (!scheduleResponse.ok) {\n            console.error(`❌ فشل في الاتصال بـ weekly-schedule API: ${scheduleResponse.status}`);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: `فشل في الاتصال بالخريطة البرامجية: ${scheduleResponse.status}`\n            }, {\n                status: 500\n            });\n        }\n        const scheduleData = await scheduleResponse.json();\n        console.log(`📦 استجابة weekly-schedule:`, {\n            success: scheduleData.success,\n            itemsCount: scheduleData.data?.scheduleItems?.length || 0,\n            weekStart: scheduleData.data?.weekStart\n        });\n        if (!scheduleData.success) {\n            console.error(`❌ خطأ في بيانات الخريطة البرامجية:`, scheduleData);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'فشل في جلب بيانات الخريطة البرامجية'\n            }, {\n                status: 500\n            });\n        }\n        // فلترة المواد لليوم المحدد فقط (المواد الأصلية + الإعادات)\n        console.log('📦 إجمالي المواد في الخريطة:', scheduleData.data.scheduleItems?.length || 0);\n        // عرض عينة من المواد للتحقق\n        if (scheduleData.data.scheduleItems && scheduleData.data.scheduleItems.length > 0) {\n            console.log('📋 عينة من المواد في الخريطة:');\n            scheduleData.data.scheduleItems.slice(0, 5).forEach((item, index)=>{\n                console.log(`  ${index + 1}. ${item.mediaItem?.name} - يوم ${item.dayOfWeek} - ${item.startTime} - إعادة: ${item.isRerun}`);\n            });\n        }\n        const dayItems = scheduleData.data.scheduleItems.filter((item)=>{\n            const matches = item.dayOfWeek === dayOfWeek;\n            if (matches) {\n                console.log(`✅ مادة متطابقة: ${item.mediaItem?.name} - يوم ${item.dayOfWeek} - ${item.startTime}`);\n            } else {\n                console.log(`❌ مادة غير متطابقة: ${item.mediaItem?.name} - يوم ${item.dayOfWeek} (مطلوب: ${dayOfWeek}) - ${item.startTime}`);\n            }\n            return matches;\n        });\n        console.log(`🔍 نتائج الفلترة:`);\n        console.log(`  📊 إجمالي المواد في الخريطة: ${scheduleData.data.scheduleItems?.length || 0}`);\n        console.log(`  📊 المواد المطابقة لليوم ${dayOfWeek}: ${dayItems.length}`);\n        console.log(`  📅 التاريخ المطلوب: ${dateParam}`);\n        console.log(`  📅 يوم الأسبوع المحسوب: ${dayOfWeek} (${dayNames[dayOfWeek]})`);\n        console.log(`  📅 weekStart المرسل للخريطة: ${weekStart}`);\n        console.log(`  📅 weekStart المستلم من الخريطة: ${scheduleData.data.weekStart}`);\n        if (dayItems.length === 0) {\n            console.log(`⚠️ لا توجد مواد لليوم ${dayOfWeek} في الخريطة البرامجية`);\n            console.log(`💡 تحقق من وجود مواد في الخريطة البرامجية لهذا اليوم`);\n            console.log(`🔍 جميع المواد في الخريطة:`);\n            if (scheduleData.data.scheduleItems && scheduleData.data.scheduleItems.length > 0) {\n                scheduleData.data.scheduleItems.forEach((item, index)=>{\n                    console.log(`  ${index + 1}. ${item.mediaItem?.name} - يوم ${item.dayOfWeek} - ${item.startTime}`);\n                });\n            } else {\n                console.log(`  📭 لا توجد مواد في الخريطة البرامجية على الإطلاق`);\n            }\n        }\n        // فصل المواد الأصلية عن الإعادات\n        const originalItems = dayItems.filter((item)=>!item.isRerun);\n        const rerunItems = dayItems.filter((item)=>item.isRerun);\n        // ترتيب المواد حسب الوقت\n        const sortedOriginalItems = originalItems.sort((a, b)=>a.startTime.localeCompare(b.startTime));\n        // بناء الجدول الإذاعي الاحترافي\n        const scheduleRows = [];\n        // دالة لحساب الوقت التالي\n        const calculateNextTime = (startTime, duration)=>{\n            const [startHours, startMins] = startTime.split(':').map(Number);\n            const [durHours, durMins, durSecs] = duration.split(':').map(Number);\n            let totalMinutes = startHours * 60 + startMins;\n            totalMinutes += durHours * 60 + durMins + Math.ceil(durSecs / 60);\n            const hours = Math.floor(totalMinutes / 60) % 24;\n            const minutes = totalMinutes % 60;\n            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n        };\n        // دالة لحساب الوقت المتبقي حتى الهدف\n        const calculateRemainingTime = (currentTime, targetTime)=>{\n            const currentMinutes = timeToMinutes(currentTime);\n            let targetMinutes = timeToMinutes(targetTime);\n            // إذا كان الهدف في اليوم التالي\n            if (targetMinutes <= currentMinutes) {\n                targetMinutes += 24 * 60;\n            }\n            return targetMinutes - currentMinutes;\n        };\n        // ترتيب المواد حسب الوقت الطبيعي (08:00 أولاً)\n        const allItems = [\n            ...dayItems\n        ].sort((a, b)=>{\n            const timeA = a.startTime;\n            const timeB = b.startTime;\n            // ترتيب طبيعي: 08:00, 09:00, ..., 23:00, 00:00, 01:00, ..., 07:00\n            const getTimeOrder = (time)=>{\n                const hour = parseInt(time.split(':')[0]);\n                // 08:00-23:59 = 0-15, 00:00-07:59 = 16-23\n                return hour >= 8 ? hour - 8 : hour + 16;\n            };\n            const orderA = getTimeOrder(timeA);\n            const orderB = getTimeOrder(timeB);\n            if (orderA !== orderB) return orderA - orderB;\n            return timeA.localeCompare(timeB);\n        });\n        console.log('🏗️ بناء جدول إذاعي لـ', allItems.length, 'مادة');\n        // بناء الجدول الإذاعي الاحترافي\n        let isFirstSegment = true;\n        allItems.forEach((item, itemIndex)=>{\n            // التعامل مع المواد المؤقتة\n            if (item.isTemporary) {\n                console.log(`🟣 إضافة مادة مؤقتة: ${item.mediaItem?.name} (3 سيجمنت × 13 دقيقة)`);\n                // إضافة 3 سيجمنت للمادة المؤقتة\n                for(let segIndex = 0; segIndex < 3; segIndex++){\n                    scheduleRows.push({\n                        id: `temp_segment_${item.id}_${segIndex}`,\n                        type: 'segment',\n                        time: isFirstSegment ? '08:00:00' : undefined,\n                        content: `${item.mediaItem?.name || 'مادة مؤقتة'} - سيجمنت ${segIndex + 1}${item.isRerun ? ' (إعادة)' : ''} [مؤقت]`,\n                        mediaItemId: item.mediaItemId,\n                        segmentId: `temp_seg_${segIndex}`,\n                        segmentCode: `TEMP_${item.mediaItemId}_${segIndex + 1}`,\n                        duration: '00:13:00',\n                        isRerun: item.isRerun || false,\n                        isTemporary: true,\n                        canDelete: true,\n                        originalStartTime: item.startTime\n                    });\n                    isFirstSegment = false;\n                    // إضافة صفوف فارغة بين السيجمنتات (3 صفوف فقط)\n                    if (segIndex < 2) {\n                        for(let i = 0; i < 3; i++){\n                            scheduleRows.push({\n                                id: `empty_temp_seg_${item.id}_${segIndex}_${i}`,\n                                type: 'empty',\n                                canDelete: true\n                            });\n                        }\n                    }\n                }\n            } else {\n                // المواد العادية\n                if (!item.mediaItem || !item.mediaItem.segments) return;\n                const mediaItem = item.mediaItem;\n                console.log(`📺 إضافة المادة: ${mediaItem.name} (${mediaItem.segments.length} سيجمنت)`);\n                // إضافة السيجمنتات بدون أوقات (إلا الأول)\n                mediaItem.segments.forEach((segment, segIndex)=>{\n                    scheduleRows.push({\n                        id: `segment_${item.id}_${segment.id}`,\n                        type: 'segment',\n                        time: isFirstSegment ? '08:00:00' : undefined,\n                        content: `${mediaItem.name} - ${segment.name}${item.isRerun ? ' (إعادة)' : ''}`,\n                        mediaItemId: item.mediaItemId,\n                        segmentId: segment.id,\n                        segmentCode: segment.code || segment.segmentCode || `${mediaItem.id}_${segment.segmentNumber}`,\n                        duration: segment.duration,\n                        isRerun: item.isRerun || false,\n                        canDelete: false,\n                        originalStartTime: item.startTime // حفظ الوقت الأصلي للمرجع\n                    });\n                    isFirstSegment = false;\n                    // إضافة صفوف فارغة بين السيجمنتات (3 صفوف فقط)\n                    if (segIndex < mediaItem.segments.length - 1) {\n                        for(let i = 0; i < 3; i++){\n                            scheduleRows.push({\n                                id: `empty_seg_${item.id}_${segment.id}_${i}`,\n                                type: 'empty',\n                                canDelete: true\n                            });\n                        }\n                    }\n                });\n            }\n            // إضافة 5 صفوف فارغة بين المواد\n            if (itemIndex < allItems.length - 1) {\n                const nextItem = allItems[itemIndex + 1];\n                for(let i = 0; i < 5; i++){\n                    scheduleRows.push({\n                        id: `filler_${item.id}_${i}`,\n                        type: 'empty',\n                        canDelete: true,\n                        targetTime: nextItem.startTime // حفظ الوقت المستهدف\n                    });\n                }\n            } else {\n                // المادة الأخيرة - إضافة 5 صفوف فقط\n                for(let i = 0; i < 5; i++){\n                    scheduleRows.push({\n                        id: `end_filler_${item.id}_${i}`,\n                        type: 'empty',\n                        canDelete: true\n                    });\n                }\n            }\n        });\n        // جلب جميع المواد المتاحة للقائمة الجانبية مع فلترة المواد الصالحة فقط\n        const allMedia = (0,_shared_data__WEBPACK_IMPORTED_MODULE_3__.getAllMediaItems)().filter((item)=>{\n            // 1. التحقق من خاصية TX\n            const showInTX = item.showInTX === true;\n            if (!showInTX) {\n                console.log(`🚫 مادة غير مفعلة في TX: ${item.name}`);\n                return false;\n            }\n            // 2. التحقق من الحالة\n            const isValidStatus = item.status === 'VALID';\n            if (!isValidStatus) {\n                console.log(`🚫 مادة غير صالحة: ${item.name} (حالة: ${item.status})`);\n                return false;\n            }\n            // 3. التحقق من تاريخ الانتهاء\n            const isNotExpired = !item.endDate || new Date(item.endDate) >= new Date();\n            if (!isNotExpired) {\n                console.log(`🚫 مادة منتهية الصلاحية: ${item.name}`);\n                return false;\n            }\n            console.log(`✅ مادة صالحة للقائمة: ${item.name} (نوع: ${item.type})`);\n            return true;\n        });\n        console.log(`📦 تم جلب ${dayItems.length} مادة إجمالية`);\n        console.log(`📋 المواد المجدولة: ${JSON.stringify(dayItems.map((i)=>i.mediaItem?.name))}`);\n        console.log(`📝 صفوف الجدول: ${scheduleRows.length} صف`);\n        console.log(`📚 المواد المتاحة: ${allMedia.length} مادة`);\n        if (allMedia.length === 0) {\n            console.log(`⚠️ لا توجد مواد متاحة في القائمة الجانبية!`);\n            console.log(`💡 تحقق من:`);\n            console.log(`  - وجود مواد في قاعدة البيانات`);\n            console.log(`  - خاصية showInTX = true`);\n            console.log(`  - حالة المواد = 'VALID'`);\n            console.log(`  - تاريخ انتهاء الصلاحية`);\n        } else {\n            console.log(`✅ عينة من المواد المتاحة:`);\n            allMedia.slice(0, 3).forEach((item, index)=>{\n                console.log(`  ${index + 1}. ${item.name} (${item.type}) - TX: ${item.showInTX} - حالة: ${item.status}`);\n            });\n        }\n        console.log(`📤 إرسال الاستجابة:`);\n        console.log(`  - التاريخ: ${dateParam}`);\n        console.log(`  - يوم الأسبوع: ${dayOfWeek}`);\n        console.log(`  - المواد المجدولة: ${dayItems.length}`);\n        console.log(`  - صفوف الجدول: ${scheduleRows.length}`);\n        console.log(`  - المواد المتاحة: ${allMedia.length}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                date: dateParam,\n                dayOfWeek,\n                scheduleItems: dayItems,\n                scheduleRows,\n                availableMedia: allMedia\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في جلب الجدول الإذاعي:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في جلب الجدول الإذاعي'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - حفظ تعديلات الجدول الإذاعي\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { date, scheduleRows } = body;\n        if (!date || !scheduleRows) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'البيانات مطلوبة'\n            }, {\n                status: 400\n            });\n        }\n        // حفظ تعديلات الجدول الإذاعي في ملف JSON\n        console.log('💾 حفظ تعديلات الجدول الإذاعي للتاريخ:', date);\n        console.log('📝 عدد الصفوف:', scheduleRows.length);\n        // إنشاء اسم ملف فريد للتاريخ\n        const fileName = `daily-schedule-${date}.json`;\n        const filePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'saved-schedules', fileName);\n        // إنشاء مجلد إذا لم يكن موجوداً\n        const dirPath = path__WEBPACK_IMPORTED_MODULE_2___default().dirname(filePath);\n        if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(dirPath)) {\n            fs__WEBPACK_IMPORTED_MODULE_1___default().mkdirSync(dirPath, {\n                recursive: true\n            });\n        }\n        // بيانات الحفظ\n        const saveData = {\n            date: date,\n            savedAt: new Date().toISOString(),\n            scheduleRows: scheduleRows,\n            totalRows: scheduleRows.length,\n            segments: scheduleRows.filter((row)=>row.type === 'segment').length,\n            fillers: scheduleRows.filter((row)=>row.type === 'filler').length,\n            emptyRows: scheduleRows.filter((row)=>row.type === 'empty').length\n        };\n        // حفظ الملف\n        fs__WEBPACK_IMPORTED_MODULE_1___default().writeFileSync(filePath, JSON.stringify(saveData, null, 2), 'utf8');\n        console.log('✅ تم حفظ الجدول في:', filePath);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم حفظ التعديلات بنجاح',\n            savedFile: fileName,\n            stats: {\n                totalRows: saveData.totalRows,\n                segments: saveData.segments,\n                fillers: saveData.fillers,\n                emptyRows: saveData.emptyRows\n            }\n        });\n    } catch (error) {\n        console.error('❌ خطأ في حفظ الجدول الإذاعي:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'فشل في حفظ التعديلات'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/daily-schedule/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/api/shared-data.ts":
/*!************************************!*\
  !*** ./src/app/api/shared-data.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addMediaItem: () => (/* binding */ addMediaItem),\n/* harmony export */   clearAllMediaItems: () => (/* binding */ clearAllMediaItems),\n/* harmony export */   getAllMediaItems: () => (/* binding */ getAllMediaItems),\n/* harmony export */   getMediaItemById: () => (/* binding */ getMediaItemById),\n/* harmony export */   removeMediaItem: () => (/* binding */ removeMediaItem),\n/* harmony export */   saveImportedData: () => (/* binding */ saveImportedData),\n/* harmony export */   updateMediaItem: () => (/* binding */ updateMediaItem)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n// ملف مشترك للبيانات - يضمن التزامن بين جميع APIs\n\n\n// مسار ملف البيانات المؤقت\nconst DATA_FILE = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'temp-data.json');\n// قاعدة بيانات مشتركة للمواد الإعلامية\nlet mediaItems = [];\n// تحميل البيانات من الملف عند بدء التشغيل\nfunction loadData() {\n    try {\n        if (fs__WEBPACK_IMPORTED_MODULE_0___default().existsSync(DATA_FILE)) {\n            const data = fs__WEBPACK_IMPORTED_MODULE_0___default().readFileSync(DATA_FILE, 'utf8');\n            mediaItems = JSON.parse(data);\n            console.log(`📂 تم تحميل ${mediaItems.length} مادة من الملف المؤقت`);\n        }\n    } catch (error) {\n        console.error('❌ خطأ في تحميل البيانات:', error);\n        mediaItems = [];\n    }\n}\n// حفظ البيانات في الملف\nfunction saveData() {\n    try {\n        fs__WEBPACK_IMPORTED_MODULE_0___default().writeFileSync(DATA_FILE, JSON.stringify(mediaItems, null, 2));\n        console.log(`💾 تم حفظ ${mediaItems.length} مادة في الملف المؤقت`);\n    } catch (error) {\n        console.error('❌ خطأ في حفظ البيانات:', error);\n    }\n}\n// تحميل البيانات عند استيراد الملف\nloadData();\n// دالة لإضافة مادة جديدة\nfunction addMediaItem(item) {\n    mediaItems.push(item);\n    saveData(); // حفظ فوري\n    console.log(`✅ تم إضافة مادة جديدة: ${item.name} (المجموع: ${mediaItems.length})`);\n}\n// دالة لحذف مادة\nfunction removeMediaItem(id) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        const removed = mediaItems.splice(index, 1)[0];\n        saveData(); // حفظ فوري\n        console.log(`🗑️ تم حذف المادة: ${removed.name} (المجموع: ${mediaItems.length})`);\n        return true;\n    }\n    return false;\n}\n// دالة للحصول على جميع المواد\nfunction getAllMediaItems() {\n    return mediaItems;\n}\n// دالة للحصول على مادة بالمعرف\nfunction getMediaItemById(id) {\n    return mediaItems.find((item)=>item.id === id);\n}\n// دالة لتحديث مادة\nfunction updateMediaItem(id, updatedItem) {\n    const index = mediaItems.findIndex((item)=>item.id === id);\n    if (index > -1) {\n        mediaItems[index] = {\n            ...mediaItems[index],\n            ...updatedItem\n        };\n        saveData(); // حفظ فوري\n        console.log(`✏️ تم تحديث المادة: ${updatedItem.name} (المعرف: ${id})`);\n        return true;\n    }\n    return false;\n}\n// دالة لحذف جميع المواد\nfunction clearAllMediaItems() {\n    const count = mediaItems.length;\n    mediaItems = [];\n    saveData(); // حفظ فوري\n    console.log(`🗑️ تم حذف جميع المواد (${count} مادة)`);\n    return count;\n}\n// دالة لحفظ البيانات المستوردة\nfunction saveImportedData(items) {\n    mediaItems = items;\n    saveData(); // حفظ فوري\n    console.log(`📥 تم حفظ ${items.length} مادة مستوردة`);\n    return items.length;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shared-data.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fdaily-schedule%2Froute&page=%2Fapi%2Fdaily-schedule%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdaily-schedule%2Froute.ts&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();